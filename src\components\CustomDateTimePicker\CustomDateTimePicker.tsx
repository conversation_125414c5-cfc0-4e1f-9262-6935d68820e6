import React, {useState, useMemo, useCallback, useEffect} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View, Text, TouchableOpacity, StyleSheet, Image, Platform} from 'react-native';
import {Calendar} from 'react-native-calendars';
import {CButton, CImage, Icon} from '@components/index';
import ModalTimePicker from '../DateTimePicker/ModalTimePicker';
import useTranslation from '@/hooks/useTranslation';

interface Player {
  id: string;
  name: string;
  image: string;
}

interface DateRange {
  startDate: Date | null;
  endDate: Date | null;
  startTime?: string | null;
  endTime?: string | null;
}

interface CustomDateTimePickerProps {
  onClose?: () => void;
  onConfirm: (dateRange: DateRange) => void;
  initialStartDate?: Date | null;
  initialEndDate?: Date | null;
  initialStartTime?: string | null;
  initialEndTime?: string | null;
  minDate?: Date;
  maxDate?: Date;
  allowRangeSelection?: boolean;
  title?: string;
  onAddPlayers?: () => void;
  selectedPlayers?: Player[];
  onDateChange?: (dates: {startDate: Date | null; endDate: Date | null}) => void;
  onTimeChange?: (times: {startTime: string | null; endTime: string | null}) => void;
  hidePlayerInput?: boolean;
  hideActions?: boolean;
}

const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

const weekDays = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

const isSameDay = (date1: Date, date2: Date): boolean => {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
};

// Define a helper function to ensure we have a valid Date object
const getValidDate = (dateInput: any): Date | null => {
  if (!dateInput) return null;

  try {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
    return !isNaN(date.getTime()) ? date : null;
  } catch (e) {
    return null;
  }
};

// Helper function to extract time from time string
const extractTimeFromString = (timeString: string | null): string => {
  if (!timeString) return '';
  return timeString.split(' ')[0] || '';
};

// Helper function to extract AM/PM from time string
const extractAmPmFromString = (timeString: string | null): 'AM' | 'PM' => {
  if (!timeString || !timeString.includes(' ')) return 'AM';
  const ampm = timeString.split(' ')[1];
  return ampm === 'PM' ? 'PM' : 'AM';
};

const CustomDateTimePicker: React.FC<CustomDateTimePickerProps> = ({
  onClose,
  onConfirm,
  initialStartDate = null,
  initialEndDate = null,
  initialStartTime = null,
  initialEndTime = null,
  minDate,
  maxDate,
  allowRangeSelection = true,
  title = '',
  onAddPlayers,
  selectedPlayers = [],
  onDateChange,
  onTimeChange,
  hidePlayerInput = false,
  hideActions = false,
}) => {
  const theme = useThemeStore();
  const dynamicStyles = useMemo(() => createDynamicStyles(theme), [theme]);
  const today = useMemo(() => new Date(), []);
  const {t} = useTranslation();
  // State for month navigation
  const [currentMonth, setCurrentMonth] = useState(
    initialStartDate ? new Date(initialStartDate) : new Date(),
  );

  // State for date range
  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: getValidDate(initialStartDate),
    endDate: getValidDate(initialEndDate),
  });

  // State for time selection
  const [selectedTimeStart, setSelectedTimeStart] = useState<string>(
    extractTimeFromString(initialStartTime) ||
      (initialStartDate && getValidDate(initialStartDate)
        ? `${getValidDate(initialStartDate)!.getHours().toString().padStart(2, '0')}:${getValidDate(initialStartDate)!.getMinutes().toString().padStart(2, '0')}`
        : ''),
  );

  const [selectedTimeEnd, setSelectedTimeEnd] = useState<string>(
    extractTimeFromString(initialEndTime) ||
      (initialEndDate && getValidDate(initialEndDate)
        ? `${getValidDate(initialEndDate)!.getHours().toString().padStart(2, '0')}:${getValidDate(initialEndDate)!.getMinutes().toString().padStart(2, '0')}`
        : ''),
  );

  const [timePickerMode, setTimePickerMode] = useState<'start' | 'end'>('start');
  const [showTimePicker, setShowTimePicker] = useState<boolean>(false);

  const [amPmStart, setAmPmStart] = useState<'AM' | 'PM'>(
    extractAmPmFromString(initialStartTime) ||
      (initialStartDate && getValidDate(initialStartDate)
        ? getValidDate(initialStartDate)!.getHours() < 12
          ? 'AM'
          : 'PM'
        : 'AM'),
  );

  const [amPmEnd, setAmPmEnd] = useState<'AM' | 'PM'>(
    extractAmPmFromString(initialEndTime) ||
      (initialEndDate && getValidDate(initialEndDate)
        ? getValidDate(initialEndDate)!.getHours() < 12
          ? 'AM'
          : 'PM'
        : 'AM'),
  );

  // Calendar logic for react-native-calendars
  const formatDate = (date: Date | null) => (date ? date.toISOString().split('T')[0] : '');

  const onDayPress = (day: any) => {
    const selected = new Date(day.dateString);
    setCurrentMonth(new Date(selected));

    setDateRange(prev => {
      let newDateRange;
      if (
        !allowRangeSelection ||
        (!prev.startDate && !prev.endDate) ||
        (prev.startDate && prev.endDate)
      ) {
        setSelectedTimeStart('');
        setAmPmStart('AM');
        newDateRange = {startDate: selected, endDate: null};
      } else if (prev.startDate && !prev.endDate) {
        if (selected < prev.startDate) {
          setSelectedTimeEnd(selectedTimeStart);
          setAmPmEnd(amPmStart);
          setSelectedTimeStart('');
          setAmPmStart('AM');
          newDateRange = {startDate: selected, endDate: prev.startDate};
        } else {
          setSelectedTimeEnd('');
          setAmPmEnd('AM');
          newDateRange = {...prev, endDate: selected};
        }
      } else {
        newDateRange = prev;
      }

      // Notify parent component about date changes
      if (onDateChange) {
        onDateChange({
          startDate: newDateRange.startDate,
          endDate: newDateRange.endDate,
        });
      }

      return newDateRange;
    });
  };

  // Marked dates for react-native-calendars
  const markedDates: any = useMemo(() => {
    const marked: any = {};
    if (!dateRange.startDate) return marked;
    const start = formatDate(dateRange.startDate);
    const end = dateRange.endDate ? formatDate(dateRange.endDate) : start;
    marked[start] = {
      startingDay: true,
      color: theme.colors.activeColor,
      textColor: theme.colors.greyBackground,
    };
    if (end !== start) {
      marked[end] = {
        endingDay: true,
        color: theme.colors.activeColor,
        textColor: theme.colors.greyBackground,
      };
      // Fill in-between
      const current = new Date(dateRange.startDate!);
      while (formatDate(current) !== end) {
        current.setDate(current.getDate() + 1);
        const currStr = formatDate(current);
        if (currStr !== end)
          marked[currStr] = {
            color: `${theme.colors.activeColor}15`,
            textColor: theme.colors.activeColor,
          };
      }
    }
    return marked;
  }, [dateRange, theme.colors.activeColor]);

  // Month navigation
  const goToPreviousMonth = useCallback(() => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() - 1);
      return newMonth;
    });
  }, []);

  const goToNextMonth = useCallback(() => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() + 1);
      return newMonth;
    });
  }, []);

  const toggleAmPm = useCallback(
    (type: 'start' | 'end') => {
      if (type === 'start') {
        const newAmPm = amPmStart === 'AM' ? 'PM' : 'AM';
        setAmPmStart(newAmPm);

        // Notify parent component about time changes
        if (onTimeChange) {
          onTimeChange({
            startTime: selectedTimeStart ? `${selectedTimeStart} ${newAmPm}` : null,
            endTime: selectedTimeEnd && dateRange.endDate ? `${selectedTimeEnd} ${amPmEnd}` : null,
          });
        }
      } else {
        const newAmPm = amPmEnd === 'AM' ? 'PM' : 'AM';
        setAmPmEnd(newAmPm);

        // Notify parent component about time changes
        if (onTimeChange) {
          onTimeChange({
            startTime: selectedTimeStart ? `${selectedTimeStart} ${amPmStart}` : null,
            endTime: selectedTimeEnd ? `${selectedTimeEnd} ${newAmPm}` : null,
          });
        }
      }
    },
    [amPmStart, amPmEnd, selectedTimeStart, selectedTimeEnd, dateRange.endDate, onTimeChange],
  );

  const handleConfirm = useCallback(() => {
    if (!dateRange.startDate) return;
    const start = new Date(dateRange.startDate);
    if (selectedTimeStart) {
      const [startHour, startMinute] = selectedTimeStart.split(':').map(Number);
      let finalStartHour = startHour;
      if (amPmStart === 'PM' && startHour < 12) finalStartHour += 12;
      else if (amPmStart === 'AM' && startHour === 12) finalStartHour = 0;
      start.setHours(finalStartHour, startMinute);
    } else {
      start.setHours(0, 0, 0, 0);
    }
    let end = null;
    if (dateRange.endDate) {
      end = new Date(dateRange.endDate);
      if (selectedTimeEnd) {
        const [endHour, endMinute] = selectedTimeEnd.split(':').map(Number);
        let finalEndHour = endHour;
        if (amPmEnd === 'PM' && endHour < 12) finalEndHour += 12;
        else if (amPmEnd === 'AM' && endHour === 12) finalEndHour = 0;
        end.setHours(finalEndHour, endMinute);
      } else {
        end.setHours(23, 59, 59, 999);
      }
    }
    const result = {
      startDate: start,
      endDate: end,
      startTime: selectedTimeStart ? selectedTimeStart + ' ' + amPmStart : null,
      endTime: dateRange.endDate && selectedTimeEnd ? selectedTimeEnd + ' ' + amPmEnd : null,
    };

    onConfirm(result);
  }, [dateRange, selectedTimeStart, selectedTimeEnd, amPmStart, amPmEnd, onConfirm]);

  // Render time picker modal
  const renderTimePicker = () => {
    if (showTimePicker) {
      const defaultHour = 9;
      const defaultMinute = 0;
      const getInitialHour = () => {
        if (timePickerMode === 'start') {
          return selectedTimeStart && selectedTimeStart.includes(':')
            ? parseInt(selectedTimeStart.split(':')[0]) || defaultHour
            : defaultHour;
        } else {
          return selectedTimeEnd && selectedTimeEnd.includes(':')
            ? parseInt(selectedTimeEnd.split(':')[0]) || defaultHour
            : defaultHour;
        }
      };
      const getInitialMinute = () => {
        if (timePickerMode === 'start') {
          return selectedTimeStart && selectedTimeStart.includes(':')
            ? parseInt(selectedTimeStart.split(':')[1]) || defaultMinute
            : defaultMinute;
        } else {
          return selectedTimeEnd && selectedTimeEnd.includes(':')
            ? parseInt(selectedTimeEnd.split(':')[1]) || defaultMinute
            : defaultMinute;
        }
      };
      return (
        <ModalTimePicker
          visible={showTimePicker}
          title={`Select ${timePickerMode === 'start' ? 'Start' : 'End'} Time`}
          onTimeSelected={(hour: number, minute: number, amPm: 'AM' | 'PM') => {
            const formattedTime = `${hour}:${minute.toString().padStart(2, '0')}`;

            if (timePickerMode === 'start') {
              setSelectedTimeStart(formattedTime);
              setAmPmStart(amPm);

              // Notify parent component about time changes
              if (onTimeChange) {
                onTimeChange({
                  startTime: `${formattedTime} ${amPm}`,
                  endTime:
                    selectedTimeEnd && dateRange.endDate ? `${selectedTimeEnd} ${amPmEnd}` : null,
                });
              }
            } else {
              setSelectedTimeEnd(formattedTime);
              setAmPmEnd(amPm);

              // Notify parent component about time changes
              if (onTimeChange) {
                onTimeChange({
                  startTime: selectedTimeStart ? `${selectedTimeStart} ${amPmStart}` : null,
                  endTime: `${formattedTime} ${amPm}`,
                });
              }
            }
          }}
          initialHour={getInitialHour()}
          initialMinute={getInitialMinute()}
          initialAmPm={timePickerMode === 'start' ? amPmStart : amPmEnd}
          onClose={() => setShowTimePicker(false)}
        />
      );
    }
    return null;
  };

  // Effect to sync with props changes
  useEffect(() => {
    const newStartDate = getValidDate(initialStartDate);
    const newEndDate = getValidDate(initialEndDate);

    // Extract times from initialStartTime/initialEndTime if provided
    const newStartTime = extractTimeFromString(initialStartTime);
    const newEndTime = extractTimeFromString(initialEndTime);

    // Extract AM/PM from initialStartTime/initialEndTime if provided
    const newAmPmStart = extractAmPmFromString(initialStartTime);
    const newAmPmEnd = extractAmPmFromString(initialEndTime);

    // Only update if there's a change in the props
    if (
      newStartDate !== dateRange.startDate ||
      newEndDate !== dateRange.endDate ||
      newStartTime !== selectedTimeStart ||
      newEndTime !== selectedTimeEnd ||
      newAmPmStart !== amPmStart ||
      newAmPmEnd !== amPmEnd
    ) {
      // If props have changed, update all states
      setDateRange({
        startDate: newStartDate,
        endDate: newEndDate,
      });

      if (newStartTime) setSelectedTimeStart(newStartTime);
      if (newEndTime) setSelectedTimeEnd(newEndTime);
      if (newAmPmStart) setAmPmStart(newAmPmStart);
      if (newAmPmEnd) setAmPmEnd(newAmPmEnd);

      // Update current month if start date changes
      if (newStartDate) {
        setCurrentMonth(new Date(newStartDate));
      }
    }
  }, [initialStartDate, initialEndDate, initialStartTime, initialEndTime]);

  // Days count
  const daysCount = useMemo(() => {
    if (!dateRange.startDate || !dateRange.endDate) return '';
    const diffTime = Math.abs(dateRange.endDate.getTime() - dateRange.startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    return `${diffDays} days`;
  }, [dateRange]);

  // Render custom header
  const renderCustomHeader = () => (
    <View style={dynamicStyles.calendarContainer}>
      <View style={dynamicStyles.monthSelector}>
        <View style={dynamicStyles.monthNavContainer}>
          <TouchableOpacity
            onPress={goToPreviousMonth}
            style={dynamicStyles.monthNavButton}
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}>
            <Icon name="Left-chevron" size={16} color={theme.colors.activeColor} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={goToNextMonth}
            style={dynamicStyles.monthNavButton}
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}>
            <Icon name="Right-chevron" size={16} color={theme.colors.activeColor} />
          </TouchableOpacity>
        </View>
        <Text style={dynamicStyles.monthTitle}>
          {months[currentMonth.getMonth()]}
          {/* {currentMonth.getFullYear()} */}
        </Text>
        <View style={dynamicStyles.monthTitle}>
          <Text style={dynamicStyles.monthTitle}>{daysCount ? daysCount : '0 days'}</Text>
        </View>
      </View>
      {/* <View style={dynamicStyles.weekDayHeader}>
        {weekDays.map((day, index) => (
          <Text key={`weekday-${index}`} style={dynamicStyles.weekDayText}>
            {day}
          </Text>
        ))}
      </View> */}
    </View>
  );

  // Render time selectors
  const renderTimeSelectors = () => {
    if (!dateRange.startDate) return null;
    return (
      <View style={dynamicStyles.timeSelectorsContainer}>
        <View style={dynamicStyles.timeSelector}>
          <TouchableOpacity
            style={dynamicStyles.selectedTimeDisplay}
            onPress={() => {
              setTimePickerMode('start');
              setShowTimePicker(true);
            }}>
            <Text
              style={selectedTimeStart ? dynamicStyles.timeDisplay : dynamicStyles.timePlaceholder}>
              {selectedTimeStart || ''}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={dynamicStyles.amPmButton} onPress={() => toggleAmPm('start')}>
            <Text style={dynamicStyles.amPmText}>{amPmStart}</Text>
          </TouchableOpacity>
        </View>
        {allowRangeSelection && dateRange.endDate && (
          <>
            <Text style={dynamicStyles.timeSeparator}>—</Text>
            <View style={dynamicStyles.timeSelector}>
              <TouchableOpacity
                style={dynamicStyles.selectedTimeDisplay}
                onPress={() => {
                  setTimePickerMode('end');
                  setShowTimePicker(true);
                }}>
                <Text
                  style={
                    selectedTimeEnd ? dynamicStyles.timeDisplay : dynamicStyles.timePlaceholder
                  }>
                  {selectedTimeEnd || ''}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity style={dynamicStyles.amPmButton} onPress={() => toggleAmPm('end')}>
                <Text style={dynamicStyles.amPmText}>{amPmEnd}</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    );
  };

  // Render player input
  const renderPlayerInput = () => {
    const hasSelectedPlayers = selectedPlayers && selectedPlayers.length > 0;

    return (
      <View style={dynamicStyles.playersContainer}>
        <TouchableOpacity
          style={dynamicStyles.addPlayersButton}
          onPress={onAddPlayers}
          activeOpacity={0.8}>
          {hasSelectedPlayers ? (
            <View style={dynamicStyles.selectedPlayersContainer}>
              <Text style={dynamicStyles.addPlayersText}>
                {t('customDatePicker.otherPlayers')} :{' '}
              </Text>
              <View style={dynamicStyles.playerAvatarsContainer}>
                {selectedPlayers.slice(0, 3).map((player, index) => (
                  <CImage
                    key={player.id}
                    source={{uri: player.image}}
                    style={{
                      ...dynamicStyles.playerAvatar,
                      marginLeft: index > 0 ? -7 : 0,
                    }}
                  />
                ))}
                {selectedPlayers.length > 3 && (
                  <View style={dynamicStyles.morePlayersCircle}>
                    <Text style={dynamicStyles.morePlayersText}>+{selectedPlayers.length - 3}</Text>
                  </View>
                )}
              </View>
              {/* <Text style={dynamicStyles.addPlayersText}>{selectedPlayers.length}</Text> */}
            </View>
          ) : (
            <Text style={dynamicStyles.addPlayersText}>+ {t('customDatePicker.addPlayers')}</Text>
          )}
        </TouchableOpacity>
        <TouchableOpacity style={dynamicStyles.checkCircle} activeOpacity={0.8}>
          <Icon name="check" size={16} color={theme.colors.activeColor} />
        </TouchableOpacity>
      </View>
    );
  };

  // Render actions
  const renderActions = () => (
    <View style={dynamicStyles.actionsContainer}>
      <CButton
        title="Reserve Court"
        variant="primary"
        containerStyle={dynamicStyles.reserveButton}
        onPress={handleConfirm}
        textStyle={dynamicStyles.standInLineText}
        isDisabled={!dateRange.startDate}
      />
      <CButton
        title="Stand in line"
        variant="outline"
        textStyle={dynamicStyles.standInLineText}
        containerStyle={dynamicStyles.standInLineButton}
        onPress={onClose}
      />
    </View>
  );

  return (
    <View style={[dynamicStyles.container]}>
      {title ? (
        <View style={dynamicStyles.modalHeader}>
          <Text style={dynamicStyles.modalTitle}>{title}</Text>
        </View>
      ) : null}
      {renderCustomHeader()}
      <Calendar
        key={formatDate(currentMonth).slice(0, 7)}
        current={formatDate(currentMonth)}
        markingType="period"
        markedDates={markedDates}
        minDate={minDate ? formatDate(minDate) : undefined}
        maxDate={maxDate ? formatDate(maxDate) : undefined}
        onDayPress={onDayPress}
        hideExtraDays={false}
        enableSwipeMonths={false}
        renderHeader={() => null}
        onMonthChange={month => {
          setCurrentMonth(new Date(month.dateString));
        }}
        theme={{
          backgroundColor: theme.colors.greyBackground,
          calendarBackground: theme.colors.greyBackground,
          textSectionTitleColor: theme.colors.activeColor,
          selectedDayBackgroundColor: theme.colors.activeColor,
          selectedDayTextColor: '#fff',
          todayTextColor: theme.colors.activeColor,
          dayTextColor: theme.colors.activeColor,
          textDisabledColor: theme.colors.midGrey,
          dotColor: theme.colors.activeColor,
          selectedDotColor: '#fff',
          arrowColor: 'transparent',
          monthTextColor: 'transparent',
          textDayFontSize: 16,
          textMonthFontSize: 16,
          textDayHeaderFontSize: 14,
        }}
      />
      {renderTimeSelectors()}
      {renderTimePicker()}
      {!hidePlayerInput && renderPlayerInput()}
      {!hideActions && renderActions()}
    </View>
  );
};

const createDynamicStyles = (theme: any) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      borderRadius: 15,
      backgroundColor: theme.colors.greyBackground,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      padding: 16,
    },
    modalHeader: {
      alignItems: 'center',
      marginBottom: 15,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.activeColor,
    },
    calendarContainer: {
      marginBottom: 0,
    },
    monthSelector: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 20,
      alignItems: 'center',
      //   marginBottom: 10,
    },
    monthNavContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 20,
    },
    monthNavButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: theme.colors.actionBackground,
      justifyContent: 'center',
      alignItems: 'center',
    },
    monthTitle: {
      height: 36,
      flex: 1,
      borderRadius: 18,
      paddingHorizontal: 10,
      textAlign: 'center',
      fontSize: 16,
      color: theme.colors.activeColor,
      backgroundColor: theme.colors.actionBackground,
      ...Platform.select({
        ios: {
          lineHeight: 30, // Match height value for iOS
        },
        android: {
          textAlignVertical: 'center',
        },
      }),
    },
    weekDayHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    weekDayText: {
      flex: 1,
      textAlign: 'center',
      fontSize: 14,
      color: theme.colors.activeColor,
      fontWeight: '600',
    },
    timeSelectorsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 15,
      flex: 1,
    },
    timeSelector: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    selectedTimeDisplay: {
      backgroundColor: theme.colors.actionBackground,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      borderRadius: 20,
      paddingVertical: 8,
      paddingHorizontal: 16,
      minWidth: 80,
      alignItems: 'center',
    },
    timeDisplay: {
      color: theme.colors.activeColor,
      fontSize: 15,
    },
    timePlaceholder: {
      color: theme.colors.activeColor,
      fontSize: 15,
      opacity: 0.7,
    },
    amPmButton: {
      backgroundColor: theme.colors.actionBackground,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      borderRadius: 20,
      paddingVertical: 8,
      paddingHorizontal: 10,
      marginLeft: 5,
      minWidth: 50,
      alignItems: 'center',
      justifyContent: 'center',
    },
    amPmText: {
      color: theme.colors.activeColor,
      fontSize: 15,
      textAlign: 'center',
      ...Platform.select({
        ios: {
          lineHeight: 20,
        },
        android: {
          textAlignVertical: 'center',
        },
      }),
    },
    timeSeparator: {
      color: theme.colors.activeColor,
      fontSize: 15,
      marginHorizontal: 10,
    },
    playersContainer: {
      marginBottom: 15,
      flexDirection: 'row',
      gap: 10,
    },
    checkCircle: {
      width: 40,
      height: 40,
      borderRadius: 40,
      padding: 10,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      backgroundColor: theme.colors.actionBackground,
      justifyContent: 'center',
      alignItems: 'center',
    },
    addPlayersButton: {
      flexDirection: 'row',
      backgroundColor: theme.colors.actionBackground,
      borderRadius: 25,
      paddingVertical: 10,
      paddingHorizontal: 16,
      borderWidth: 1,
      flex: 1,
      borderColor: theme.colors.activeColor,
      alignItems: 'center',
    },
    addPlayersText: {
      color: theme.colors.activeColor,
      fontSize: 15,
    },
    actionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    reserveButton: {
      flex: 1,
      marginRight: 8,
      borderRadius: 40,
    },
    standInLineText: {
      fontSize: 14,
    },
    standInLineButton: {
      flex: 1,
      borderColor: theme.colors.white,
      backgroundColor: theme.colors.actionBackground,
      borderWidth: 1,
    },
    selectedPlayersContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    playerAvatarsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    playerAvatar: {
      width: 24,
      height: 24,
      borderRadius: 12,
      marginLeft: 5,
    },
    morePlayersCircle: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: theme.colors.activeColor,
      justifyContent: 'center',
      alignItems: 'center',
    },
    morePlayersText: {
      color: theme.colors.white,
      fontSize: 12,
      fontWeight: 'bold',
    },
  });
};

export default CustomDateTimePicker;
