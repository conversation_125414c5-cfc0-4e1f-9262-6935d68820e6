import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  LayoutAnimation,
  Platform,
  UIManager,
  Image,
  TextStyle,
  ViewStyle,
  StyleProp,
} from 'react-native';
import {Icon} from '@/components';
import Typography from './Typography';
import {useThemeStore} from '@/store';

// Enable LayoutAnimation for Android
if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

interface CollapsibleViewProps {
  title: string;
  children: React.ReactNode;
  initiallyExpanded?: boolean;
  titleStyle?: StyleProp<TextStyle>;
  headerStyle?: StyleProp<ViewStyle>;
  contentStyle?: StyleProp<ViewStyle>;
  arrowColor?: string;
  animationDuration?: number;
}

interface CollapsibleViewWithIconProps {
  title?: string;
  children: React.ReactNode;
  initiallyExpanded?: boolean;
  titleStyle?: StyleProp<TextStyle>;
  headerStyle?: StyleProp<ViewStyle>;
  contentStyle?: StyleProp<ViewStyle>;
  customArrow?: React.ReactNode;
  animationDuration?: number;
  customHeader?: React.ReactNode;
}

const CollapsibleView = ({
  title,
  children,
  initiallyExpanded = false,
  titleStyle,
  headerStyle,
  contentStyle,
  arrowColor = '#000',
  animationDuration = 300,
}: CollapsibleViewProps) => {
  const [expanded, setExpanded] = useState(initiallyExpanded);
  const rotateAnimation = useRef(new Animated.Value(initiallyExpanded ? 1 : 0)).current;

  // Configure animation when expanded state changes
  useEffect(() => {
    const config = {
      duration: animationDuration,
      toValue: expanded ? 1 : 0,
      useNativeDriver: true,
    };

    Animated.timing(rotateAnimation, config).start();

    // Use LayoutAnimation for smooth height transition
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  }, [expanded, animationDuration, rotateAnimation]);

  // Calculate arrow rotation based on expanded state
  const arrowRotation = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  // Toggle expanded state
  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  // Create static styles for the first component
  const staticStyles = StyleSheet.create({
    container: {
      overflow: 'hidden',
      borderRadius: 4,
      marginVertical: 8,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
    },
    headerText: {
      fontSize: 16,
      fontWeight: '500',
      flex: 1,
    },
    content: {
      paddingHorizontal: 16,
    },
    arrow: {
      width: 0,
      height: 0,
      borderLeftWidth: 6,
      borderLeftColor: 'transparent',
      borderRightWidth: 6,
      borderRightColor: 'transparent',
      borderTopWidth: 6,
    },
  });

  return (
    <View style={staticStyles.container}>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={toggleExpand}
        style={[staticStyles.header, headerStyle]}>
        <Typography style={[staticStyles.headerText, titleStyle]}>{title}</Typography>
        <Animated.View style={{transform: [{rotate: arrowRotation}]}}>
          <View style={[staticStyles.arrow, {borderTopColor: arrowColor}]} />
        </Animated.View>
      </TouchableOpacity>

      {expanded && <View style={[staticStyles.content, contentStyle]}>{children}</View>}
    </View>
  );
};

// Alternative version with custom arrow component
const CollapsibleViewWithIcon = ({
  title,
  children,
  initiallyExpanded = false,
  titleStyle,
  headerStyle,
  contentStyle,
  customArrow,
  customHeader,
  animationDuration = 300,
}: CollapsibleViewWithIconProps) => {
  const [expanded, setExpanded] = useState(initiallyExpanded);
  const rotateAnimation = useRef(new Animated.Value(initiallyExpanded ? 1 : 0)).current;
  const theme = useThemeStore();
  useEffect(() => {
    const config = {
      duration: animationDuration,
      toValue: expanded ? 1 : 0,
      useNativeDriver: true,
    };

    Animated.timing(rotateAnimation, config).start();
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  }, [expanded, animationDuration, rotateAnimation]);

  const arrowRotation = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  return (
    <View style={styles(theme).container}>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={toggleExpand}
        style={[styles(theme).header, headerStyle]}>
        {customHeader ? (
          <View style={styles(theme).customHeader}>
            {customHeader}
            <Animated.View style={{transform: [{rotate: arrowRotation}]}}>
              {customArrow || <Icon name="dropdown" size={28} color={theme.colors.white} />}
            </Animated.View>
          </View>
        ) : (
          <>
            <Typography style={[styles(theme).headerText, titleStyle as TextStyle]}>
              {title}
            </Typography>
            <Animated.View style={{transform: [{rotate: arrowRotation}]}}>
              {customArrow || <Icon name="dropdown" size={28} color={theme.colors.white} />}
            </Animated.View>
          </>
        )}
      </TouchableOpacity>

      {expanded && <View style={[styles(theme).content, contentStyle]}>{children}</View>}
    </View>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      overflow: 'hidden',
      //   marginVertical: 8,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      // padding: 16,
    },
    headerText: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.activeColor,
      flex: 1,
    },
    content: {
      // paddingHorizontal: 16,
    },
    arrow: {
      width: 0,
      height: 0,
      borderLeftWidth: 6,
      borderLeftColor: 'transparent',
      borderRightWidth: 6,
      borderRightColor: 'transparent',
      borderTopWidth: 6,
    },
    arrowIcon: {
      width: 12,
      height: 12,
      resizeMode: 'contain',
    },
    customHeader: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: 10,
    },
  });

export {CollapsibleView, CollapsibleViewWithIcon};
