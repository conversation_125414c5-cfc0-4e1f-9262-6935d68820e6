import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import OptimizedFlatListScreen from '@/screens/examples/OptimizedFlatListScreen';
import SentryTestScreen from '@/screens/examples/SentryTestScreen';

export type ExamplesStackParamList = {
  OptimizedFlatList: undefined;
  SentryTest: undefined;
  // Add more example screens here as needed
};

const Stack = createStackNavigator<ExamplesStackParamList>();

const ExamplesStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="OptimizedFlatList"
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen
        name="SentryTest"
        component={SentryTestScreen}
        options={{animationEnabled: true, gestureEnabled: true}}
      />
      <Stack.Screen
        name="OptimizedFlatList"
        component={OptimizedFlatListScreen}
        options={{animationEnabled: true, gestureEnabled: true}}
      />
      {/* Add more example screens here as needed */}
    </Stack.Navigator>
  );
};

export default ExamplesStack;
