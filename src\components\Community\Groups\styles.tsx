import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    content: {
      flexGrow: 1,
      padding: 16,
    },
    actionContainer: {
      backgroundColor: theme.colors.black,
      paddingHorizontal: 30,
      paddingVertical: 6,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 20,
    },
    cardRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      backgroundColor: theme.colors.TranslucentWhite,
      borderRadius: 20,
      padding: 16,
      marginTop: 20,
      marginBottom: 10,
      gap: 16,
    },
    cardItem: {
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
    },
    shadowWrap: {
      borderRadius: 20,
      marginBottom: 5,
    },
    cardIconContainer: {
      backgroundColor: '#FFFFFF33',
      borderRadius: 20,
      padding: 18,
      marginBottom: 5,
      justifyContent: 'center',
      alignItems: 'center',
    },
    cardLabel: {
      color: theme.colors.white,
      fontSize: 11,
      lineHeight: 13,
    },
    cardContainer: {
      gap: 16,
    },
    cardIcon: {
      width: 32,
      height: 32,
    },
    sectionHeader: {
      color: theme.colors.white,
      fontSize: 14,
      fontWeight: 'bold',
      marginBottom: 8,
      marginTop: 30,
    },
    sectionHeader1: {
      color: theme.colors.white,
      fontSize: 14,
      fontWeight: 'bold',
      marginBottom: 8,
      marginTop: 10,
    },
    groupListContainer: {
      backgroundColor: theme.colors.TranslucentWhite,
      borderRadius: 16,
      padding: 12,
      marginBottom: 16,
    },

    // filter styles
    searchInput: {
      backgroundColor: theme.colors.white,
      borderWidth: 2,
      borderColor: theme.colors.primary,
      borderRadius: 10,
    },
    filter: {
      gap: 10,
      width: '100%',
    },
  });
