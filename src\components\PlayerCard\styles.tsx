import {FONT_SIZE} from '@/utils/fonts';
import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    card: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
      borderWidth: 1,
      borderColor: theme.colors.secondary,
      padding: 12,
      borderRadius: 12,
      position: 'relative', // Add position relative for absolute positioning of status indicator
    },
    avatarContainer: {
      position: 'relative', // For absolute positioning of status indicator
    },
    statusIndicator: {
      width: 12,
      height: 12,
      borderRadius: 6,
      position: 'absolute',
      top: 0,
      right: 5,
      zIndex: 1, // Ensure it appears above the avatar
    },
    avatar: {
      width: 61,
      height: 61,
      borderRadius: 50,
    },

    infoContainer: {
      flex: 1,
      marginLeft: 12,
    },
    nameRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    name: {
      color: theme.colors.text,
      fontWeight: 'bold',
      fontSize: theme?.fontSize?.font18,
      marginRight: 6,
    },
    rating: {
      color: theme.colors.text,
      fontSize: FONT_SIZE.md,
    },
    locationRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 4,
    },
    locationText: {
      color: theme.colors.text,
      marginLeft: 4,
      fontSize: FONT_SIZE.md,
    },
    plusButton: {
      width: 36,
      height: 36,
      borderRadius: 30,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 15,
    },
    plusText: {
      color: theme.colors.text,
      fontSize: FONT_SIZE.xxl,
      fontWeight: 'bold',
    },
  });
