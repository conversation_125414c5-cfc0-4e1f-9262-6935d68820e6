import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    content: {
      flexGrow: 1,
      padding: 16,
    },
    actionContainer: {
      backgroundColor: theme.colors.black,
      paddingHorizontal: 30,
      paddingVertical: 6,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 20,
    },
    cardRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      backgroundColor: theme.colors.TranslucentWhite,
      borderRadius: 20,
      padding: 16,
      marginTop: 24,
      marginBottom: 16,
      gap: 16,
    },
    cardItem: {
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
    },
    cardIconContainer: {
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      borderRadius: 20,
      padding: 18,
      marginBottom: 5,
    },
    cardLabel: {
      color: theme.colors.white,
    },
    cardContainer: {
      gap: 16,
    },
    cardIcon: {
      width: 32,
      height: 32,
    },
  });
