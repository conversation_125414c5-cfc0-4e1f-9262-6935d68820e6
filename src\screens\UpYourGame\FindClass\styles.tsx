import {StyleSheet} from 'react-native';

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    title: {
      textAlign: 'center',
      marginBottom: 10,
    },
    subtitle: {
      textAlign: 'center',
      marginBottom: 20,
    },
    checkboxContainer: {
      width: '100%',
      flexDirection: 'column',
      gap: 16,
    },
    optionButton: {
      width: '100%',
      borderRadius: 15,
      paddingVertical: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    activeOption: {
      backgroundColor: theme.colors.activeColor,
    },
    inactiveOption: {
      backgroundColor: theme.colors.dimGray,
    },
    optionText: {
      fontSize: 20,
      fontWeight: 'bold',
    },
    activeText: {
      color: theme.colors.black,
    },
    inactiveText: {
      color: theme.colors.text,
    },
    pillContainer: {
      width: '100%',
      flexGrow: 1,
      paddingHorizontal: 16,
      paddingBottom: 20,
    },
    separator: {
      height: 16,
    },
    buttonContainer: {
      paddingHorizontal: 16,
      paddingBottom: 20,
      alignItems: 'center',
    },
    errorText: {
      textAlign: 'center',
      marginBottom: 10,
      paddingHorizontal: 16,
    },
  });

export default styles;
