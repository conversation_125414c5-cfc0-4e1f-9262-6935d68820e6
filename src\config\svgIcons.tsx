import React from 'react';
import {View} from 'react-native';
import Svg, {Path} from 'react-native-svg';

export const CustomMarker = () => {
  return (
    <View>
      <Svg width="20" height="28" viewBox="0 0 20 28" fill="none">
        <Path
          d="M7.50872 0.238408C1.03672 1.77341 -1.98628 8.71241 1.40572 14.4394C3.66672 18.2564 6.08172 21.9814 8.43072 25.7454C8.77172 26.2914 9.13072 26.8254 9.50672 27.4034C9.65272 27.2154 9.76272 27.0954 9.84772 26.9594C12.4447 22.8084 15.0467 18.6594 17.6317 14.4994C18.8807 12.4894 19.2657 10.3154 18.9147 7.96641C18.1287 2.70041 12.7077 -0.994592 7.50872 0.238408Z"
          fill="#DFFF4F"
        />
        <Path
          d="M11.4276 4.21818C10.3796 3.54018 9.27563 3.41518 8.12363 3.93418C7.37863 4.27018 6.80063 4.80718 6.33663 5.47118C5.78563 6.26118 5.44963 7.13118 5.37163 8.09518C5.30663 8.89518 5.47063 9.66118 5.74063 10.4072C6.01963 11.1802 6.40463 11.8962 6.95163 12.5112C7.39563 13.0112 7.81063 13.5302 8.17163 14.0932C8.43163 14.4992 8.57663 14.9252 8.56763 15.4232C8.54763 16.5542 8.56863 17.6852 8.57363 18.8162C8.57463 19.0812 8.68163 19.1892 8.94763 19.1902C9.31963 19.1902 9.69163 19.1882 10.0646 19.1862C10.3746 19.1842 10.4736 19.0822 10.4726 18.7692C10.4676 17.5712 10.4656 16.3732 10.4556 15.1752C10.4536 14.9432 10.5036 14.7332 10.6066 14.5292C10.9116 13.9242 11.3256 13.3952 11.7676 12.8892C12.1676 12.4322 12.5496 11.9672 12.8396 11.4322C13.3476 10.4922 13.6906 9.50018 13.6836 8.42218C13.6756 6.99718 13.0946 5.80418 12.0626 4.70618C11.9036 4.58218 11.6786 4.38018 11.4276 4.21818ZM9.83563 18.5492H9.20263C9.19863 17.4872 9.19463 16.4152 9.19063 15.3572C9.40463 15.3552 9.61863 15.3532 9.82363 15.3512C9.82763 16.4132 9.83163 17.4862 9.83563 18.5492ZM9.86163 14.6892C9.63263 14.6892 9.38563 14.6902 9.14763 14.6902C9.00563 14.3852 8.85863 14.0732 8.72163 13.7802C8.96763 13.8112 9.23363 13.8692 9.50063 13.8722C9.76863 13.8752 10.0376 13.8232 10.2886 13.7972C10.1516 14.0832 10.0066 14.3862 9.86163 14.6892ZM12.7986 10.0092C12.5516 10.7112 12.2246 11.3702 11.7596 11.9502C11.4306 12.3602 11.0566 12.7282 10.5846 12.9802C9.78263 13.4082 9.00663 13.3382 8.26063 12.8482C8.05063 12.7102 7.86363 12.5382 7.75863 12.4562C6.81463 11.4762 6.28663 10.4092 6.04863 9.19918C5.82463 8.06118 6.09563 7.01418 6.69563 6.04218C7.08063 5.41918 7.57663 4.90518 8.23463 4.56618C9.23663 4.04918 10.2076 4.13418 11.1266 4.75918C12.4776 5.67718 13.6046 7.71918 12.7986 10.0092Z"
          fill="#2B2B2B"
        />
      </Svg>
    </View>
  );
};
export const DarkCustomMarker = () => {
  return (
    <View>
      <Svg width="20" height="28" viewBox="0 0 20 28" fill="none">
        <Path
          d="M7.50872 0.238408C1.03672 1.77341 -1.98628 8.71241 1.40572 14.4394C3.66672 18.2564 6.08172 21.9814 8.43072 25.7454C8.77172 26.2914 9.13072 26.8254 9.50672 27.4034C9.65272 27.2154 9.76272 27.0954 9.84772 26.9594C12.4447 22.8084 15.0467 18.6594 17.6317 14.4994C18.8807 12.4894 19.2657 10.3154 18.9147 7.96641C18.1287 2.70041 12.7077 -0.994592 7.50872 0.238408Z"
          fill="#DFFF4F"
        />

        <Path
          d="M6.90029 0.752015C5.85229 0.0272568 4.74729 -0.106364 3.59629 0.448429C2.85129 0.807602 2.27329 1.38164 1.80929 2.09143C1.25729 2.93591 0.922289 3.86591 0.844288 4.8964C0.77929 5.75157 0.943289 6.5704 1.21329 7.36784C1.49129 8.19415 1.87729 8.95953 2.42329 9.61695C2.86729 10.1514 3.28329 10.7062 3.64329 11.3081C3.90229 11.7421 4.04829 12.1974 4.04029 12.7298C4.02029 13.9388 4.04129 15.1478 4.04529 16.3568C4.04629 16.64 4.15229 16.7555 4.42029 16.7566C4.79129 16.7566 5.16429 16.7544 5.53629 16.7523C5.84629 16.7502 5.94429 16.6411 5.94329 16.3065C5.94029 15.0259 5.93729 13.7453 5.92829 12.4647C5.92629 12.2167 5.97629 11.9922 6.07929 11.7741C6.38429 11.1274 6.79829 10.5619 7.24029 10.021C7.63829 9.5325 8.02229 9.03543 8.31229 8.46353C8.81929 7.4587 9.16229 6.39829 9.15629 5.24595C9.14729 3.72267 8.56729 2.44739 7.53429 1.27367C7.37629 1.14112 7.15129 0.925188 6.90029 0.752015Z"
          fill="#2B2B2B"
          transform="translate(5, 6)"
        />
      </Svg>
    </View>
  );
};
