import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 16,
    },

    title: {
      textAlign: 'center',
      color: theme.colors.white,
      letterSpacing: 0,
    },
    subtitle: {
      textAlign: 'center',
      fontWeight: '100',
      marginVertical: 10,
      color: theme.colors.white,
      lineHeight: 20,
    },
    button: {
      backgroundColor: theme.colors.activeColor, // neon yellow/green
      paddingHorizontal: 60,
      paddingVertical: 10,
      alignItems: 'center',
      marginTop: 8,
    },
    buttonText: {
      color: theme.colors.black,
      fontWeight: 'bold',
      fontSize: 18,
    },
  });
