<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme for Android 10+ (API 29+) -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="android:statusBarColor">#000000</item>
        <item name="colorPrimaryDark">#000000</item>
        <item name="android:windowBackground">@android:color/black</item>
        <item name="android:forceDarkAllowed">false</item>
    </style>
    
    <!-- Splash screen theme for Android 10+ -->
    <style name="SplashTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:windowBackground">@android:color/black</item>
        <item name="android:statusBarColor">#000000</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:forceDarkAllowed">false</item>
    </style>
</resources>
