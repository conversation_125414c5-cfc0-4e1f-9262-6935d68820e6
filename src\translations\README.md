# GoRaqt Translation System

This directory contains the translation files for the GoRaqt application. The translation system uses JSON files to store translations for different languages.

## Structure

- `en.json` - English translations (default)
- `fr.json` - French translations
- `es.json` - Spanish translations
- `de.json` - German translations

## Usage

### Using the Translation Hook

The easiest way to use translations in your components is with the `useTranslationContext` hook:

```tsx
import {useTranslationContext} from '@/context/TranslationContext';

const MyComponent = () => {
  const {t} = useTranslationContext();

  return (
    <View>
      <Text>{t('common.ok')}</Text>
      <Text>{t('auth.login')}</Text>

      {/* With parameters */}
      <Text>{t('profile.welcome', {name: 'John'})}</Text>
    </View>
  );
};
```

### Changing the Language

To change the application language:

```tsx
import {useTranslationContext} from '@/context/TranslationContext';

const LanguageSettings = () => {
  const {switchLanguage} = useTranslationContext();

  return (
    <View>
      <Button title="English" onPress={() => switchLanguage('en')} />
      <Button title="Français" onPress={() => switchLanguage('fr')} />
      <Button title="Español" onPress={() => switchLanguage('es')} />
      <Button title="Deutsch" onPress={() => switchLanguage('de')} />
    </View>
  );
};
```

### Using the LanguageSelector Component

The app includes a pre-built `LanguageSelector` component:

```tsx
import {LanguageSelector} from '@/components';

const LanguageSettings = () => {
  return (
    <View>
      <Text>Select your language:</Text>
      <LanguageSelector />
    </View>
  );
};
```

## Adding New Translations

1. Add the new translation key to `en.json` (and other language files)
2. Use the key in your component with the `t` function.

## Adding a New Language

1. Create a new JSON file in the `translations` directory (e.g., `it.json` for Italian)
2. Copy the structure from `en.json` and translate the values
3. Update the `SupportedLanguage` type in `src/i18n/index.ts`
4. Add the new language to the `getAvailableLanguages` function in `src/i18n/index.ts`
5. If the language is RTL, add it to the `RTL_LANGUAGES` array

## String Interpolation

You can use variables in your translations with the `{{variableName}}` syntax:

```json
{
  "profile": {
    "welcome": "Welcome, {{name}}!"
  }
}
```

Then pass the variables as parameters:

```tsx
t('profile.welcome', {name: 'John'}); // "Welcome, John!"
```

## Fallback Mechanism

If a translation key is not found in the current language, the system will:

1. Try to find the key in the English translations
2. If still not found, return the key itself as a fallback

## RTL Support

The translation system supports Right-to-Left (RTL) languages. When a language is set as RTL:

1. Text alignment will automatically adjust
2. Layout direction will be reversed
3. Icons and other directional elements will be flipped

To use RTL features:

```tsx
import {useTranslationContext} from '@/context/TranslationContext';

const MyComponent = () => {
  const {isRTL} = useTranslationContext();

  return (
    <View style={[styles.container, isRTL && styles.containerRTL]}>
      {/* Your component content */}
    </View>
  );
};
```
