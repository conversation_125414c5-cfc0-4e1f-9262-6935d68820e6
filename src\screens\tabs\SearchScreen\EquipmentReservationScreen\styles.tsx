import {FONT_SIZE} from '@/utils/fonts';
import {Dimensions, StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    root: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    contentContainer: {
      flex: 1,
      paddingHorizontal: 16,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.secondary,
      marginTop: 10,
    },
    title: {
      fontSize: FONT_SIZE.xxxl,
      color: theme.colors.text,
      marginVertical: 8,
    },
    filterContainer: {
      marginTop: 13,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    tabsContainer: {
      width: '90%',
    },
    filterButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 20,
    },
    filterText: {
      color: theme.colors.text,
    },
    list: {
      flex: 1,
      marginTop: 20,
    },
    sectionContainer: {
      marginBottom: 5,
    },
    brandTitle: {
      color: theme.colors.offWhite,
    },
    mainBrandTitle: {
      color: theme.colors.offWhite,
      marginTop: 4,
    },
    racquetItem: {
      flexDirection: 'row',
      // alignItems: 'center',
    },
    moreButton: {
      marginLeft: 5,
    },
    moreText: {
      color: theme.colors.offWhite,
      fontSize: FONT_SIZE.lg,
      textAlignVertical: 'bottom',
    },
    navigationButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 20,
    },
    navigationButton: {
      minWidth: 120,
      padding: 0,
      paddingVertical: 5,
      borderRadius: 8,
    },
    dropdownContainer: {
      width: 'auto',
      minWidth: 150,
      maxWidth: 200,
      marginVertical: 10,
      borderRadius: 100,
    },
    flex: {
      flex: 1,
    },
    flexGrow: {
      flexGrow: 1,
      paddingBottom: 40,
    },
    filterItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 20,
      marginVertical: 10,
    },
    content: {
      flex: 1,
      gap: 10,
    },
    filter: {
      gap: 25,
      minHeight: Dimensions.get('window').height * 0.4,
    },
    text: {
      color: theme.colors.text,
    },
    btn: {
      width: '100%',
      borderWidth: 0,
      borderColor: theme.colors.text,
    },
    imageContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 15,
      borderWidth: 1,
    },
    categoryImage: {
      width: '100%',
      height: 200,
      marginTop: 15,
      marginBottom: 10,
    },
    cartModalContainer: {
      justifyContent: 'center',
      minWidth: '85%',
    },
    cartModalContent: {
      alignItems: 'center',
      gap: 25,
      width: '100%',
      paddingHorizontal: 20,
      paddingTop: 20,
    },
    cartModalTitle: {textAlign: 'center', color: theme.colors.text},
    cartModalButton: {width: '100%', backgroundColor: theme.colors.activeColor},
    cartModalButtonText: {color: theme.colors.black, fontWeight: 700},
    cartModalButtonOutline: {
      width: '100%',
      borderWidth: 0,
      borderColor: theme.colors.text,
    },
  });
