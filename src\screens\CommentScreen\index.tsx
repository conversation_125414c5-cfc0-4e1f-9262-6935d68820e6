import React, {useState} from 'react';
import {
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  ImageBackground,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
} from 'react-native';
import {useForm, Controller, SubmitHandler} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import CInput from '@components/CInput';
import CButton from '@components/CButton';
import CIcon from '@components/CIcon';
import {useThemeStore} from '@/store/themeStore';
import {Images} from '@/config';
import {CImage, Header, RatingBar} from '@/components';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import createStyles from './styles';
import Typography from '@/components/Typography';
import useTranslation from '@/hooks/useTranslation';

const MAX_IMAGES = 3;

type FormData = {
  rating: number;
  headline: string;
  description: string;
  images: any[];
};

const CommentScreen = () => {
  const theme = useThemeStore();
  const [images, setImages] = useState<any[]>([]);
  const navigation = useNavigation();
  const styles = createStyles(theme);

  const {t} = useTranslation();

  const schema = yup.object({
    rating: yup
      .number()
      .min(1, t('commentScreen.selectRating'))
      .required(t('commentScreen.ratingRequired')),
    headline: yup.string().required(t('commentScreen.headlineRequired')),
    description: yup
      .string()
      .required(t('commentScreen.descriptionRequired'))
      .max(1500, t('commentScreen.maxCharacters')),
    images: yup.array().of(yup.object()).max(MAX_IMAGES).required(),
  });

  const {
    control,
    handleSubmit,
    setValue,
    formState: {errors},
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      rating: 0,
      headline: '',
      description: '',
      images: [],
    },
  });

  const handleAddImage = () => {
    if (images.length >= MAX_IMAGES) return;
    const dummy = Images.profile1;
    const newImages = [...images, dummy];
    setImages(newImages);
    setValue('images', newImages, {shouldValidate: true});
  };

  const handleRemoveImage = (idx: number) => {
    const newImages = images.filter((_, i) => i !== idx);
    setImages(newImages);
    setValue('images', newImages, {shouldValidate: true});
  };

  const onSubmit: SubmitHandler<FormData> = data => {
    console.log('Form Data:', data);
  };

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const ImagePickerRow = ({
    images,
    onAdd,
    onRemove,
  }: {
    images: any[];
    onAdd: () => void;
    onRemove: (idx: number) => void;
  }) => (
    <View style={{flexDirection: 'row', justifyContent: 'center', marginVertical: 15}}>
      {[...Array(MAX_IMAGES)].map((_, idx) => (
        <TouchableOpacity
          key={idx}
          style={{
            marginHorizontal: 8,
            borderRadius: 8,
            backgroundColor: theme.colors.TranslucentWhite,
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
            height: 80,
            width: 60,
          }}
          onPress={() => (images[idx] ? onRemove(idx) : onAdd())}
          activeOpacity={0.7}>
          {images[idx] ? (
            <CImage
              source={images[idx]}
              resizeMode="cover"
              style={{width: '100%', height: '100%'}}
            />
          ) : (
            <CIcon name="add" size={32} color={theme.colors.activeColor} />
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage}>
      <SafeAreaView style={styles.container}>
        <Header
          leftIcon={{
            name: 'side-menu',
            size: 24,
            color: theme.colors.white,
            containerStyle: theme.colors.primary,
          }}
          leftIconButtonStyle={styles.menuButton}
          onLeftPress={openDrawer}
          rightIcons={[
            {name: 'notification', size: 24, badge: 0},
            {name: 'chat', size: 24, badge: 14},
          ]}
          pageTitle={t('commentScreen.title')}
          backgroundColor="transparent"
        />
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}>
          <ScrollView contentContainerStyle={styles.content}>
            {/* Rating Bar */}
            <Controller
              control={control}
              name="rating"
              render={({field: {onChange, value}}) => (
                <RatingBar
                  value={value}
                  onChange={onChange}
                  iconSize={32}
                  containerStyle={styles.ratingContainer}
                  ratingCount={5}
                />
              )}
            />
            {errors.rating && (
              <Typography
                variant="errorText"
                color={theme.colors.coralRed}
                style={{textAlign: 'center', marginBottom: 10}}>
                {errors.rating.message}
              </Typography>
            )}

            {/* Image/Video Upload */}
            <ImagePickerRow images={images} onAdd={handleAddImage} onRemove={handleRemoveImage} />
            <View style={{alignItems: 'center'}}>
              <CButton
                title={t('commentScreen.uploadImagesAndVideos')}
                variant="active"
                onPress={handleAddImage}
                containerStyle={{marginBottom: 16, width: '70%'}}
                textStyle={{color: theme.colors.black, fontSize: theme.fontSize.medium}}
              />
            </View>

            {/* Headline Input */}
            <Controller
              control={control}
              name="headline"
              render={({field: {onChange, onBlur, value}}) => (
                <CInput
                  label={t('commentScreen.writeHeadline')}
                  showLabel={false}
                  placeholder={t('commentScreen.writeHeadline')}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  hasError={!!errors.headline}
                  error={errors.headline?.message}
                  containerStyle={{marginBottom: 12}}
                  inputStyle={{
                    ...styles.inputStyle,
                  }}
                />
              )}
            />

            {/* Description Input */}
            <Controller
              control={control}
              name="description"
              render={({field: {onChange, onBlur, value}}) => (
                <CInput
                  label={t('commentScreen.describeExperience')}
                  showLabel={false}
                  placeholder={t('commentScreen.describeExperience')}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  hasError={!!errors.description}
                  error={errors.description?.message}
                  inputStyle={{
                    ...styles.inputStyle,
                    minHeight: Dimensions.get('screen').height * 0.35,
                    textAlignVertical: 'top',
                  }}
                  containerStyle={{marginBottom: 12}}
                  multiline
                  maxLength={1500}
                />
              )}
            />

            {/* Submit Button */}
            <View style={{alignItems: 'center', paddingBottom: 20}}>
              <CButton
                title="Submit"
                variant="active"
                onPress={handleSubmit(onSubmit)}
                containerStyle={{width: '50%', marginTop: 24}}
                textStyle={{color: theme.colors.black, fontSize: theme.fontSize.medium}}
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default CommentScreen;
