import React, {useState, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Image,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import {useTranslation} from '@/hooks/useTranslation';
import Typography from '@/components/Typography';
import {useThemeStore} from '@/store/themeStore';
import {Icon} from '@/components';
import {useTranslationContext} from '@/context/TranslationContext';
import {FLAGS} from '@/assets/images/flags';
import {SupportedLanguage} from '@/store/i18nStore';

interface LanguageSelectorProps {
  containerStyle?: object;
  initialModalVisible?: boolean;
  onClose?: () => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  containerStyle,
  initialModalVisible = false,
  onClose,
}) => {
  const {currentLanguage, switchLanguage, availableLanguages} = useTranslationContext();
  const {t} = useTranslationContext();
  const theme = useThemeStore(state => state);
  const [modalVisible, setModalVisible] = useState(initialModalVisible);
  const [selectedLanguage, setSelectedLanguage] = useState<SupportedLanguage>(currentLanguage);
  const [isLoading, setIsLoading] = useState(false);

  // Ensure selected language is updated if currentLanguage changes
  useEffect(() => {
    setSelectedLanguage(currentLanguage);
  }, [currentLanguage]);

  const openLanguageModal = () => {
    setSelectedLanguage(currentLanguage);
    setModalVisible(true);
  };

  const handleLanguageSelect = (languageCode: SupportedLanguage) => {
    setSelectedLanguage(languageCode);
  };

  const handleSubmit = async () => {
    if (selectedLanguage !== currentLanguage) {
      setIsLoading(true);
      try {
        // Apply the language change
        await switchLanguage(selectedLanguage);

        // Small delay to ensure the language change is applied
        setTimeout(() => {
          setIsLoading(false);
          setModalVisible(false);
          if (onClose) {
            onClose();
          }
        }, 300);
      } catch (error) {
        console.error('Error changing language:', error);
        setIsLoading(false);
      }
    } else {
      setModalVisible(false);
      if (onClose) {
        onClose();
      }
    }
  };

  const handleCancel = () => {
    setModalVisible(false);
    if (onClose) {
      onClose();
    }
  };

  const renderLanguageItem = ({item}: {item: {code: string; name: string}}) => {
    const isSelected = selectedLanguage === item.code;

    // Define selected styles
    const selectedItemStyle = isSelected
      ? {
          backgroundColor: `${theme.colors.primary}20`,
          // borderLeftWidth: 4,
          borderLeftColor: theme.colors.primary,
        }
      : undefined;

    const selectedFlagStyle = isSelected
      ? {
          borderColor: theme.colors.primary,
          // borderWidth: 1,
        }
      : undefined;

    return (
      <TouchableOpacity
        style={[themedStyles.languageItem, selectedItemStyle]}
        onPress={() => handleLanguageSelect(item.code as SupportedLanguage)}>
        <View style={styles.languageItemContent}>
          <Image
            source={FLAGS[item.code as keyof typeof FLAGS]}
            style={[styles.flagIcon, selectedFlagStyle]}
            resizeMode="cover"
          />
          <Typography
            variant="bodyMedium"
            style={styles.languageName}
            color={isSelected ? theme.colors.primary : theme.colors.text}>
            {item.name}
          </Typography>
        </View>
        {isSelected && <Icon name="check" size={20} color={theme.colors.primary} />}
      </TouchableOpacity>
    );
  };

  // Find the current language object
  const currentLanguageObj = availableLanguages.find(lang => lang.code === currentLanguage);

  // Create theme-dependent styles
  const themedStyles = {
    selectorButton: {
      ...styles.selectorButton,
      borderColor: theme.colors.offWhite,
    },
    // We'll use inline styles for the modal content
    modalHeader: {
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      alignItems: 'center' as const,
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.offWhite,
    },
    modalFooter: {
      ...styles.modalFooter,
      borderTopColor: theme.colors.offWhite,
    },
    cancelButton: {
      ...styles.cancelButton,
      backgroundColor: theme.colors.darkGray || '#333333',
    },
    languageItem: {
      ...styles.languageItem,
      borderBottomColor: theme.colors.offWhite,
    },
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {!initialModalVisible && (
        <TouchableOpacity style={themedStyles.selectorButton} onPress={openLanguageModal}>
          <View style={styles.currentLanguage}>
            <Image
              source={FLAGS[currentLanguage as keyof typeof FLAGS]}
              style={styles.currentFlag}
              resizeMode="contain"
            />
            <Typography
              variant="bodyMedium"
              style={styles.currentLanguageName}
              color={theme.colors.text}>
              {currentLanguageObj?.name}
            </Typography>
          </View>
          <Icon name="dropdown2" size={20} color={theme.colors.text} />
        </TouchableOpacity>
      )}

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCancel}>
        <View style={styles.modalOverlay}>
          <View
            style={{
              width: '85%',
              backgroundColor: theme.colors.background,
              borderRadius: 12,
              overflow: 'hidden' as const,
              elevation: 5,
              shadowColor: '#000',
              shadowOffset: {width: 0, height: 2},
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              borderWidth: 1,
              borderColor: theme.colors.offWhite,
            }}>
            <View style={themedStyles.modalHeader}>
              <Typography variant="subTitle4" style={styles.modalTitle} color={theme.colors.text}>
                {t('settings.language')}
              </Typography>
              <TouchableOpacity onPress={handleCancel} style={styles.closeButton}>
                <Icon name="close" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={availableLanguages}
              renderItem={renderLanguageItem}
              keyExtractor={item => item.code}
              style={[styles.languageList, {backgroundColor: theme.colors.background}]}
            />

            <View style={themedStyles.modalFooter}>
              <TouchableOpacity
                style={[styles.footerButton, themedStyles.cancelButton]}
                onPress={handleCancel}
                disabled={isLoading}>
                <Typography variant="bodyMedium" color={theme.colors.text}>
                  {t('common.cancel')}
                </Typography>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.footerButton,
                  styles.submitButton,
                  {backgroundColor: theme.colors.primary},
                ]}
                onPress={handleSubmit}
                disabled={isLoading}>
                <View style={styles.buttonContent}>
                  {isLoading ? (
                    <ActivityIndicator size="small" color={theme.colors.white} />
                  ) : (
                    <Typography variant="bodyMedium" color={theme.colors.white}>
                      {t('common.apply')}
                    </Typography>
                  )}
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
  },
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  currentLanguage: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentFlag: {
    width: 28,
    height: 20,
    marginRight: 10,
    borderRadius: 2,
  },
  currentLanguageName: {
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '85%',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    borderWidth: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  languageList: {
    maxHeight: 300,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  languageItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flagIcon: {
    width: 32,
    height: 22,
    marginRight: 12,
    borderRadius: 2,
    borderWidth: 0.5,
  },
  languageName: {
    fontSize: 16,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
  },
  footerButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    marginLeft: 8,
    minWidth: 80,
  },
  buttonContent: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 24,
  },
  cancelButton: {},
  submitButton: {},
});

export default LanguageSelector;
