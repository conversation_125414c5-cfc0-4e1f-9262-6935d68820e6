import React from 'react';
import {View, StyleSheet} from 'react-native';
import Typography from '../Typography';
import { useThemeStore } from '@/store/themeStore';;

interface OfferBannerProps {
  text: string;
  style?: any;
}

export const OfferBanner: React.FC<OfferBannerProps> = ({text, style}) => {
  const theme = useThemeStore();

  return (
    <View style={[styles(theme).footer, style]}>
      <Typography variant="body" style={styles(theme).footerText} align="center">
        {text}
      </Typography>
    </View>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    footer: {
      padding: 4,
      backgroundColor: theme.colors.primary,
      borderRadius: 0,
      marginHorizontal: 0,
      marginVertical: 0,
    },
    footerText: {
      color: theme.colors.white,
    },
  });
