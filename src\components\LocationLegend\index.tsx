import React, {useCallback, useMemo, useState} from 'react';
import {LayoutChangeEvent, StyleSheet, View} from 'react-native';
import Animated, {Extrapolation, interpolate, useAnimatedStyle} from 'react-native-reanimated';
import {useSafeAreaFrame} from 'react-native-safe-area-context';
import {useThemeStore} from '@/store/themeStore';
import Typography from '../Typography';
import {Icon} from '@/components';

interface LocationLegendProps {
  animatedIndex: Animated.SharedValue<number>;
  animatedPosition: Animated.SharedValue<number>;
}

const SPACE = 8;

const LocationLegend = ({animatedIndex, animatedPosition}: LocationLegendProps) => {
  //#region state
  const [height, setHeight] = useState(0);
  //#endregion

  //#region hooks
  const {height: screenHeight} = useSafeAreaFrame();
  const theme = useThemeStore();
  //#endregion

  //#region callbacks
  const handleOnLayout = useCallback(({nativeEvent: {layout}}: LayoutChangeEvent) => {
    setHeight(layout.height);
  }, []);
  //#endregion

  //#region styles
  const containerAnimatedStyle = useAnimatedStyle(() => {
    const belowMiddlePosition = screenHeight - animatedPosition.value < 270; // Using 300 as middle point
    return {
      opacity: interpolate(animatedIndex.value, [1, 1.125], [1, 0], Extrapolation.CLAMP),
      transform: [
        {
          translateY: belowMiddlePosition
            ? animatedPosition.value - height - SPACE
            : screenHeight - 270 - height - SPACE,
        },
      ],
    };
  }, [animatedIndex.value, animatedPosition.value, height, screenHeight]);

  const containerStyle = useMemo(
    () => [styles.container, containerAnimatedStyle],
    [containerAnimatedStyle],
  );
  //#endregion

  return (
    <Animated.View onLayout={handleOnLayout} style={containerStyle}>
      <View style={styles.weatherContainer}>
        <View style={styles.legendItem}>
          <Icon name="location3" size={25} color="#DFFF4F" />
          <Typography style={[styles.legendText, {color: theme.colors.white}]}>
            Available kiosk
          </Typography>
        </View>
        <View style={styles.legendItem}>
          <Icon name="location3" size={25} color={theme.colors.orange} />
          <Typography style={[styles.legendText, {color: theme.colors.white}]}>
            Planned location
          </Typography>
        </View>
        {/* <View style={styles.legendItem}>
          <View style={styles.userLocationIcon}>
            <View style={styles.userLocationInner} />
          </View>
          <Typography style={[styles.legendText, {color: theme.colors.white}]}>
            Your location
          </Typography>
        </View> */}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 3,
    top: 0,
    borderRadius: 8,
    padding: 12,
  },
  weatherContainer: {
    flexDirection: 'column',
    gap: 2,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 0,
  },
  legendText: {
    fontSize: 14,
    fontWeight: '400',
    letterSpacing: 0.5,
    textAlignVertical: 'bottom',
  },
  userLocationIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(37, 150, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(37, 150, 255, 0.5)',
    marginLeft: 2,
    marginRight: 3,
  },
  userLocationInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#2596FF',
    borderWidth: 1,
    borderColor: 'white',
  },
});

export default LocationLegend;
