import React, {useState, useRef, useEffect} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Animated, ImageBackground} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import QRScanner from '@/components/QRScanner';
import QRGenerator from '@/components/QRGenerator';
import {useNavigation, DrawerActions} from '@react-navigation/native';
import {setDarkStatusBar} from '@/utils/statusBarConfig';
import {useCallback} from 'react';
import {Header} from '@/components';
import {Images} from '@/config/images';
import api from '@/services/api';
import useTranslation from '@/hooks/useTranslation';
import {toaster} from '@/utils/commonFunctions';

const ShareScreen = () => {
  const theme = useThemeStore();
  const [activeTab, setActiveTab] = useState<'pickup' | 'direct'>('pickup');
  const navigation = useNavigation();

  const {t} = useTranslation();
  const styles = StyleSheet.create({
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    container: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    headerContainer: {
      backgroundColor: 'transparent',
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
    },
    tabsWrapper: {
      paddingHorizontal: 16,
      paddingTop: 10,
      backgroundColor: 'transparent',
    },
    tabContainer: {
      flexDirection: 'row',
      height: 48,
      borderRadius: 25,
      overflow: 'hidden',
      backgroundColor: theme.colors.jetBlack,
      padding: 4,
    },
    tabButton: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      marginHorizontal: 2,
      borderRadius: 25,
    },
    activeTabButton: {
      backgroundColor: theme.colors.lime,
    },
    inactiveTabButton: {
      backgroundColor: theme.colors.jetBlack,
    },
    tabText: {
      fontWeight: 'bold',
      fontSize: 14,
    },
    activeTabText: {
      color: theme.colors.black,
    },
    inactiveTabText: {
      color: theme.colors.white,
    },
    content: {
      flex: 1,
      position: 'relative',
      backgroundColor: 'transparent',
    },
    contentItem: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    helpContainer: {
      alignItems: 'center',
      paddingVertical: 16,
      backgroundColor: 'transparent',
      width: '100%',
    },
    helpText: {
      color: theme.colors.lime,
      fontSize: 22,
      fontWeight: 'bold',
    },
  });

  // Initial setup
  useEffect(() => {
    // Set dark status bar (black background with light content)
    setDarkStatusBar();

    // For react-navigation status bar options
    navigation.setOptions({
      headerShown: false,
      statusBarColor: theme.colors.black,
      statusBarStyle: 'light-content',
    });
  }, [navigation, theme.colors.black]);

  // Make sure status bar is black when screen is focused
  useCallback(() => {
    // This runs when the screen comes into focus
    setDarkStatusBar();

    // Optional: Clean up when screen loses focus
    return () => {
      // If you want to reset to default when screen is unfocused
      // (usually not needed as the next screen will set its own)
    };
  }, []);

  // Animation references for content fading
  const contentOpacity = useRef({
    pickup: new Animated.Value(1),
    direct: new Animated.Value(0),
  }).current;

  const sendFriendRequest = async (d: string) => {
    try {
      const res = await api.post('/user/send-friend-request', {
        friend_id: d,
      });
      console.log('🚀 ~ sendFriendRequest ~ res:', res);
      toaster('success', 'Friend Request Sent Successfully');
    } catch (error) {
      console.log('🚀 ~ sendFriendRequest ~ error:', error);
    }
  };

  const handleScan = (data: string) => {
    console.log('🚀 ~ handleScan ~ data:', data);
    if (data) {
      sendFriendRequest(data);
    }
  };

  const switchTab = (tab: 'pickup' | 'direct') => {
    // Only animate content
    Animated.parallel([
      Animated.timing(contentOpacity.pickup, {
        toValue: tab === 'pickup' ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(contentOpacity.direct, {
        toValue: tab === 'direct' ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Update active tab state
    setActiveTab(tab);

    // Log tab switch for debugging
    console.log(`Switched to ${tab} tab`);
  };

  const renderTabButton = (tab: 'pickup' | 'direct', label: string) => {
    const isActive = activeTab === tab;

    return (
      <TouchableOpacity
        style={[styles.tabButton, isActive ? styles.activeTabButton : styles.inactiveTabButton]}
        onPress={() => switchTab(tab)}>
        <Text style={[styles.tabText, isActive ? styles.activeTabText : styles.inactiveTabText]}>
          {label}
        </Text>
      </TouchableOpacity>
    );
  };

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage} resizeMode="cover">
      <View style={[styles.container]}>
        <Header
          leftIcon={{
            name: 'side-menu',
            size: 22,
            color: theme.colors.white,
          }}
          leftIconButtonStyle={styles.menuButton}
          rightIcons={[
            {name: 'notification', size: 24},
            {name: 'chat', size: 24, badge: 14},
          ]}
          onLeftPress={openDrawer}
          backgroundColor="transparent"
          transparent={true}
          containerStyle={styles.headerContainer}
          headerStyle={{borderBottomWidth: 0}}
        />

        <View style={styles.tabsWrapper}>
          <View style={styles.tabContainer}>
            {renderTabButton('pickup', t('shareScreen.pickup'))}
            {renderTabButton('direct', t('shareScreen.direct'))}
          </View>
        </View>

        <View style={styles.content}>
          <Animated.View
            style={[
              styles.contentItem,
              {opacity: contentOpacity.pickup, zIndex: activeTab === 'pickup' ? 1 : 0},
            ]}
            pointerEvents={activeTab === 'pickup' ? 'auto' : 'none'}
            accessibilityElementsHidden={activeTab !== 'pickup'}>
            <QRGenerator size={240} style={{flex: 1}} />
          </Animated.View>

          <Animated.View
            style={[
              styles.contentItem,
              {opacity: contentOpacity.direct, zIndex: activeTab === 'direct' ? 1 : 0},
            ]}
            pointerEvents={activeTab === 'direct' ? 'auto' : 'none'}
            accessibilityElementsHidden={activeTab !== 'direct'}>
            <QRScanner
              key={`qr-scanner-${activeTab}`}
              onScan={handleScan}
              isActive={activeTab === 'direct'}
            />
          </Animated.View>
        </View>

        <View style={styles.helpContainer}>
          <TouchableOpacity onPress={() => navigation.navigate('Help')}>
            <Text style={[styles.helpText]} accessibilityRole="text">
              {t('shareScreen.needHelp')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ImageBackground>
  );
};

export default ShareScreen;
