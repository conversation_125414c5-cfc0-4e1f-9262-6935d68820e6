# Sentry Integration Guide

This document provides an overview of the Sentry error tracking implementation in the GoRaqt application.

## Overview

Sentry is integrated throughout the application to provide comprehensive error tracking, performance monitoring, and user feedback capabilities. The implementation includes:

1. **Error Boundaries**: React components that catch JavaScript errors in their child component tree and display fallback UIs
2. **Data Scrubbing**: Automatic removal of sensitive information before sending to Sentry
3. **Logging Levels**: Appropriate logging levels for different types of events
4. **Context Enrichment**: Adding user, navigation, and other contexts to error reports

## Key Components

### Error Boundaries

Three types of error boundaries are implemented:

1. **Generic Error Boundary** (`ErrorBoundary`): For general component errors
2. **API Error Boundary** (`ApiErrorBoundary`): Specifically for API-related errors
3. **Navigation Error Boundary** (`NavigationErrorBoundary`): For navigation-related errors

Usage example:

```tsx
// Wrap components with appropriate error boundaries
<ErrorBoundary componentName="MyComponent">
  <MyComponent />
</ErrorBoundary>

// For API-related components
<ApiErrorBoundary apiName="UserAPI">
  <UserProfileComponent />
</ApiErrorBoundary>

// For navigation
<NavigationErrorBoundary routeName="HomeScreen">
  <HomeScreen />
</NavigationErrorBoundary>
```

### Sentry Utilities

The `sentryUtils.ts` file provides utilities for:

- Initializing Sentry with proper configuration
- Setting user context
- Setting navigation context
- Scrubbing sensitive data
- Logging errors and messages
- Creating performance transactions
- Adding breadcrumbs

### API Monitoring

The API service is enhanced with Sentry monitoring through:

1. **Request/Response Interceptors**: Track API calls and capture errors
2. **Performance Monitoring**: Measure API call performance
3. **Context Enrichment**: Add request/response data to error reports
4. **Data Scrubbing**: Remove sensitive information from API payloads

## Sensitive Data Handling

The following types of data are automatically scrubbed before sending to Sentry:

- **Personal Information**: Email, phone, birthdate, location, etc.
- **Authentication**: Tokens, passwords, API keys
- **Payment Information**: Credit card numbers, banking details

## Testing

A dedicated test screen (`SentryTestScreen`) is available to verify the Sentry integration:

1. Navigate to Examples > SentryTest
2. Use the buttons to trigger different types of errors and events
3. Verify that they appear correctly in the Sentry dashboard

## Best Practices

### When to Use Error Boundaries

- **Component Level**: Wrap complex components with `ErrorBoundary`
- **API Calls**: Wrap components that make API calls with `ApiErrorBoundary`
- **Navigation**: Use `NavigationErrorBoundary` for screen components

### Logging Best Practices

- Use appropriate severity levels:
  - `fatal`: For critical errors that crash the app
  - `error`: For errors that prevent features from working
  - `warning`: For non-critical issues
  - `info`: For informational events
  - `debug`: For debugging information

- Add context to errors:
  ```typescript
  logError(error, {
    component: 'UserProfile',
    action: 'fetchUserData',
    userId: user.id,
  });
  ```

### Performance Monitoring

For critical operations, use performance transactions:

```typescript
const transaction = startTransaction('checkout', 'payment');
try {
  // Perform operation
  transaction.setStatus('ok');
} catch (error) {
  transaction.setStatus('error');
  throw error;
} finally {
  transaction.finish();
}
```

## Sentry Dashboard

The Sentry dashboard is available at:
https://groovy-g3.sentry.io/

## Troubleshooting

If errors are not appearing in Sentry:

1. Check that the DSN is correctly configured
2. Verify that the error is not being caught and handled silently
3. Check that the error is not in the list of ignored errors
4. Ensure the app has internet connectivity

## Further Resources

- [Sentry React Native Documentation](https://docs.sentry.io/platforms/react-native/)
- [Error Boundary Documentation](https://reactjs.org/docs/error-boundaries.html)
- [Sentry Data Scrubbing](https://docs.sentry.io/product/data-management-settings/scrubbing/)
