# Flag Images for Language Selection

This directory contains flag images for each supported language in the application.

## Required Files

You need to add the following flag images to this directory:

1. `en.png` - Flag for English (e.g., UK or US flag)
2. `fr.png` - Flag for French
3. `es.png` - Flag for Spanish
4. `de.png` - Flag for German

## Image Requirements

- Images should be in PNG format
- Recommended size: 48x48 pixels
- Use transparent background if possible
- Keep file sizes small for better performance

## How to Add New Language Flags

1. Add the flag image file (e.g., `it.png` for Italian)
2. Update the `index.ts` file to include the new flag:

```typescript
export const FLAGS = {
  en: require('./en.png'),
  fr: require('./fr.png'),
  es: require('./es.png'),
  de: require('./de.png'),
  it: require('./it.png'), // Add the new language flag
};
```

3. Update the supported languages in `src/i18n/index.ts`
