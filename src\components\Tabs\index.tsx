import React, {useRef, useEffect} from 'react';
import {View, TouchableOpacity, ViewStyle, StyleProp, TextStyle} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import Typography from '../Typography';
import {Icon} from '@/components';
import {styles as createStyles} from './styles';
import {BottomSheetFlatList, BottomSheetView} from '@gorhom/bottom-sheet';
import {FlatList} from 'react-native-gesture-handler';

interface TabsProps {
  tabs: string[];
  activeTab: string;
  onTabPress: (tab: string) => void;
  variant?: 'default' | 'equipment' | 'square';
  tabStyle?: StyleProp<ViewStyle>;
  tabTitleStyle?: StyleProp<TextStyle>;
  type?: 'default' | 'inner';
  listContainerStyle?: StyleProp<ViewStyle>;
  bottomSheet?: boolean;
}

const ICON_MAP = {
  racket: 'racket4',
  padel: 'racket3',
  ball: 'ball',
  bag: 'bag',
} as const;

const Tabs: React.FC<TabsProps> = ({
  tabs,
  activeTab,
  onTabPress,
  variant = 'default',
  tabStyle,
  tabTitleStyle,
  type = 'default',
  listContainerStyle,
  bottomSheet = false,
}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const listRef = useRef<any>(null);

  // Effect to scroll active tab to center when it changes
  useEffect(() => {
    const activeIndex = tabs.indexOf(activeTab);
    if (activeIndex !== -1 && listRef.current) {
      listRef.current.scrollToIndex({
        index: activeIndex,
        animated: true,
        viewPosition: 0.5, // Center the item
      });
    }
  }, [activeTab, tabs]);

  const getTabStyles = (item: string) => {
    const isActive = activeTab === item;
    switch (variant) {
      case 'equipment':
        return [styles.equipmentTab, isActive ? styles.activeTab1 : styles.inactiveTabEquipment1];
      case 'square':
        return [
          styles.squareTab,
          isActive && {backgroundColor: theme.colors.activeColor},
          tabStyle,
        ];
      default:
        return [styles.tab, isActive ? styles.activeTab : styles.inactiveTab];
    }
  };

  const renderItem = ({item}: {item: string}) => (
    <TouchableOpacity
      activeOpacity={0.8}
      style={getTabStyles(item)}
      onPress={() => onTabPress(item)}>
      {variant === 'equipment' ? (
        <Icon
          name={ICON_MAP[item as keyof typeof ICON_MAP] || ''}
          size={26}
          color={activeTab === item ? theme.colors.text : theme.colors.gray}
        />
      ) : variant === 'square' ? (
        <Typography
          variant={type === 'inner' ? 'squareTabTitle' : 'subtitle'}
          style={tabTitleStyle as TextStyle}
          color={activeTab === item ? theme.colors.black : theme.colors.white}>
          {item}
        </Typography>
      ) : (
        <Typography
          variant="subtitle"
          color={activeTab === item ? theme.colors.text : theme.colors.primary}>
          {item}
        </Typography>
      )}
    </TouchableOpacity>
  );

  const contentContainerStyle: ViewStyle = {
    flexGrow: 1,
    gap: variant === 'equipment' ? 8 : variant === 'square' ? 8 : 20,
    justifyContent: variant === 'default' ? 'space-around' : 'flex-start',
    paddingHorizontal: 10,
  };

  // Handle scroll to index error
  const handleScrollToIndexFailed = (info: {index: number}) => {
    const wait = new Promise(resolve => setTimeout(resolve, 500));
    wait.then(() => {
      if (listRef.current) {
        listRef.current.scrollToIndex({
          index: info.index,
          animated: true,
          viewPosition: 0.5,
        });
      }
    });
  };
  if (bottomSheet) {
    return (
      <BottomSheetView style={{width: '100%'}}>
        <BottomSheetFlatList
          ref={listRef}
          data={tabs}
          renderItem={renderItem}
          keyExtractor={item => item}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={[{gap: 16, flexDirection: 'row'}, listContainerStyle]}
          bounces={false}
          onScrollToIndexFailed={handleScrollToIndexFailed}
          scrollEnabled={true}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled={true}
          keyboardShouldPersistTaps="handled"
          onEndReached={() => console.log('Hello')}
        />
      </BottomSheetView>
    );
  } else {
    return (
      <View style={styles.container}>
        <FlatList
          ref={listRef}
          data={tabs}
          renderItem={renderItem}
          keyExtractor={item => item}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={[contentContainerStyle, listContainerStyle]}
          bounces={false}
          onScrollToIndexFailed={handleScrollToIndexFailed}
        />
      </View>
    );
  }
};

export default Tabs;
