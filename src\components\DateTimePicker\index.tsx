import React, {useState, useCallback, useMemo} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {CButton, CImage, Icon} from '@components/index';
import ModalTimePicker from './ModalTimePicker';
import {useNavigation} from '@react-navigation/native';
import {SafeAreaView, ScrollView} from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';

interface Day {
  date: Date;
  day: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  isInRange: boolean;
  isStartDate: boolean;
  isEndDate: boolean;
  isDisabled: boolean;
}

type DateRange = {
  startDate: Date | null;
  endDate: Date | null;
  startTime?: string | null;
  endTime?: string | null;
};

interface DateTimePickerProps {
  onClose?: () => void;
  onConfirm: (dateRange: DateRange) => void;
  initialStartDate?: Date | null;
  initialEndDate?: Date | null;
  minDate?: Date;
  maxDate?: Date;
  allowRangeSelection?: boolean;
  showTime?: boolean;
  mode?: 'datetime' | 'date' | 'time';
  title?: string;
  onAddPlayers?: () => void;
  selectedPlayers?: Array<{id: string; image: string; name: string}>;
}

const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

const weekDays = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

// Helper to check if two dates are the same day
const isSameDay = (date1: Date, date2: Date): boolean => {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
};

const DateTimePicker: React.FC<DateTimePickerProps> = ({
  onClose,
  onConfirm,
  initialStartDate = null,
  initialEndDate = null,
  minDate,
  maxDate,
  allowRangeSelection = false,
  showTime = true,
  mode = 'datetime',
  title = '',
  onAddPlayers,
  selectedPlayers = [],
}) => {
  const theme = useThemeStore();

  // Create dynamic styles based on theme
  const dynamicStyles = useMemo(() => createDynamicStyles(theme), [theme]);

  // Move 'today' into useMemo to avoid dependencies issues
  const today = useMemo(() => new Date(), []);

  const [currentMonth, setCurrentMonth] = useState(
    initialStartDate ? new Date(initialStartDate) : new Date(),
  );

  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: initialStartDate,
    endDate: initialEndDate,
  });

  const [selectedTimeStart, setSelectedTimeStart] = useState<string>(
    initialStartDate
      ? `${initialStartDate.getHours().toString().padStart(2, '0')}:${initialStartDate.getMinutes().toString().padStart(2, '0')}`
      : '11:30',
  );

  const [selectedTimeEnd, setSelectedTimeEnd] = useState<string>(
    initialEndDate
      ? `${initialEndDate.getHours().toString().padStart(2, '0')}:${initialEndDate.getMinutes().toString().padStart(2, '0')}`
      : '11:30',
  );

  const [timePickerMode, setTimePickerMode] = useState<'start' | 'end'>('start');
  const [showTimePicker, setShowTimePicker] = useState<boolean>(false);

  const [amPmStart, setAmPmStart] = useState<'AM' | 'PM'>(
    initialStartDate ? (initialStartDate.getHours() < 12 ? 'AM' : 'PM') : 'AM',
  );

  const [amPmEnd, setAmPmEnd] = useState<'AM' | 'PM'>(
    initialEndDate ? (initialEndDate.getHours() < 12 ? 'AM' : 'PM') : 'AM',
  );

  // Navigate to previous month
  const goToPreviousMonth = useCallback(() => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() - 1);
      return newMonth;
    });
  }, []);

  // Navigate to next month
  const goToNextMonth = useCallback(() => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() + 1);
      return newMonth;
    });
  }, []);

  // Generate days for the current month view
  const generateDays = useCallback(() => {
    const days: Day[] = [];
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();

    // Get first day of the month
    const firstDayOfMonth = new Date(year, month, 1);
    const startingDayOfWeek = firstDayOfMonth.getDay();

    // Get days from previous month to fill the first week
    const daysInPreviousMonth = new Date(year, month, 0).getDate();
    for (let i = startingDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(year, month - 1, daysInPreviousMonth - i);
      const isStartDate = dateRange.startDate ? isSameDay(date, dateRange.startDate) : false;
      const isEndDate = dateRange.endDate ? isSameDay(date, dateRange.endDate) : false;
      const isInRange =
        dateRange.startDate && dateRange.endDate
          ? date >= new Date(dateRange.startDate.setHours(0, 0, 0, 0)) &&
            date <= new Date(dateRange.endDate.setHours(0, 0, 0, 0))
          : false;

      days.push({
        date,
        day: daysInPreviousMonth - i,
        isCurrentMonth: false,
        isToday: isSameDay(date, today),
        isSelected: isStartDate || isEndDate,
        isInRange,
        isStartDate,
        isEndDate,
        isDisabled: minDate ? date < minDate : false,
      });
    }

    // Current month days
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(year, month, i);
      const isToday = isSameDay(date, today);
      const isStartDate = dateRange.startDate ? isSameDay(date, dateRange.startDate) : false;
      const isEndDate = dateRange.endDate ? isSameDay(date, dateRange.endDate) : false;

      // Fix range check by properly comparing dates
      const isInRange =
        dateRange.startDate && dateRange.endDate
          ? date.getTime() > new Date(dateRange.startDate).setHours(0, 0, 0, 0) &&
            date.getTime() < new Date(dateRange.endDate).setHours(0, 0, 0, 0)
          : false;

      // Never disable the current date
      const isDisabled = isToday
        ? false
        : (minDate && date < minDate) || (maxDate && date > maxDate) || false;

      days.push({
        date,
        day: i,
        isCurrentMonth: true,
        isToday,
        isSelected: isStartDate || isEndDate,
        isInRange,
        isStartDate,
        isEndDate,
        isDisabled,
      });
    }

    // Next month days to complete the grid (if needed)
    const totalDaysDisplayed = days.length;
    const daysToAdd = 42 - totalDaysDisplayed; // 6 rows of 7 days
    for (let i = 1; i <= daysToAdd; i++) {
      const date = new Date(year, month + 1, i);

      const isStartDate = dateRange.startDate ? isSameDay(date, dateRange.startDate) : false;
      const isEndDate = dateRange.endDate ? isSameDay(date, dateRange.endDate) : false;
      const isInRange =
        dateRange.startDate && dateRange.endDate
          ? date >= new Date(dateRange.startDate.setHours(0, 0, 0, 0)) &&
            date <= new Date(dateRange.endDate.setHours(0, 0, 0, 0))
          : false;

      days.push({
        date,
        day: i,
        isCurrentMonth: false,
        isToday: false,
        isSelected: isStartDate || isEndDate,
        isInRange,
        isStartDate,
        isEndDate,
        isDisabled: maxDate ? date > maxDate : false,
      });
    }

    return days;
  }, [currentMonth, dateRange, minDate, maxDate, today]);

  // Handle day selection
  const handleDayPress = useCallback(
    (day: Day) => {
      if (day.isDisabled) {
        return;
      }

      setDateRange(prev => {
        // Single date selection
        if (
          !allowRangeSelection ||
          (!prev.startDate && !prev.endDate) ||
          (prev.startDate && prev.endDate)
        ) {
          return {startDate: day.date, endDate: null};
        }

        // Start date already selected, selecting end date
        if (prev.startDate && !prev.endDate) {
          if (day.date < prev.startDate) {
            return {startDate: day.date, endDate: prev.startDate};
          } else {
            return {...prev, endDate: day.date};
          }
        }

        return prev;
      });
    },
    [allowRangeSelection],
  );

  // Toggle AM/PM
  const toggleAmPm = useCallback((type: 'start' | 'end') => {
    if (type === 'start') {
      setAmPmStart(prev => (prev === 'AM' ? 'PM' : 'AM'));
    } else {
      setAmPmEnd(prev => (prev === 'AM' ? 'PM' : 'AM'));
    }
  }, []);

  // Handle confirm button press
  const handleConfirm = useCallback(() => {
    if (!dateRange.startDate) {
      return;
    }

    // Extract hours and minutes from time strings
    const [startHour, startMinute] = selectedTimeStart.split(':').map(Number);
    let finalStartHour = startHour;

    // Adjust for AM/PM
    if (amPmStart === 'PM' && startHour < 12) {
      finalStartHour += 12;
    } else if (amPmStart === 'AM' && startHour === 12) {
      finalStartHour = 0;
    }

    // Create start date with selected time
    const start = new Date(dateRange.startDate);
    start.setHours(finalStartHour, startMinute);

    // If end date is selected
    let end = null;
    if (dateRange.endDate) {
      const [endHour, endMinute] = selectedTimeEnd.split(':').map(Number);
      let finalEndHour = endHour;

      // Adjust for AM/PM
      if (amPmEnd === 'PM' && endHour < 12) {
        finalEndHour += 12;
      } else if (amPmEnd === 'AM' && endHour === 12) {
        finalEndHour = 0;
      }

      end = new Date(dateRange.endDate);
      end.setHours(finalEndHour, endMinute);
    }

    // Make sure we're passing actual Date objects, not strings
    const result = {
      startDate: start,
      endDate: end,
      startTime: selectedTimeStart + ' ' + amPmStart,
      endTime: dateRange.endDate ? selectedTimeEnd + ' ' + amPmEnd : null,
    };

    console.log(
      'DateTimePicker sending dates:',
      'startDate:',
      start,
      'endDate:',
      end,
      'startTime:',
      result.startTime,
      'endTime:',
      result.endTime,
    );

    onConfirm(result);
  }, [dateRange, selectedTimeStart, selectedTimeEnd, amPmStart, amPmEnd, onConfirm]);

  // Calculate days based on current month
  const days = useMemo(() => generateDays(), [generateDays]);

  // Get number of days selected
  const daysCount = useMemo(() => {
    if (!dateRange.startDate || !dateRange.endDate) {
      return '';
    }

    const diffTime = Math.abs(dateRange.endDate.getTime() - dateRange.startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    return `${diffDays} days`;
  }, [dateRange]);

  // Render calendar grid
  const renderCalendarGrid = () => {
    return (
      <View style={dynamicStyles.calendarContainer}>
        <View style={dynamicStyles.monthSelector}>
          <View style={dynamicStyles.monthNavContainer}>
            <TouchableOpacity
              onPress={goToPreviousMonth}
              style={dynamicStyles.monthNavButton}
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}>
              <Icon name="Left-chevron" size={16} color={theme.colors.activeColor} />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={goToNextMonth}
              style={dynamicStyles.monthNavButton}
              hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}>
              <Icon name="Right-chevron" size={16} color={theme.colors.activeColor} />
            </TouchableOpacity>
          </View>

          <Text style={dynamicStyles.monthTitle}>
            {months[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </Text>

          <View style={dynamicStyles.monthTitle}>
            <Text style={dynamicStyles.monthTitle}>{daysCount ? daysCount : '0 days'}</Text>
          </View>
        </View>

        <View style={dynamicStyles.weekDayHeader}>
          {weekDays.map((day, index) => (
            <Text key={`weekday-${index}`} style={dynamicStyles.weekDayText}>
              {day}
            </Text>
          ))}
        </View>

        <View style={dynamicStyles.daysContainer}>
          {days.map((day, index) => (
            <TouchableOpacity
              key={`day-${index}`}
              style={[
                dynamicStyles.dayButton,
                day.isCurrentMonth ? dynamicStyles.currentMonth : dynamicStyles.otherMonth,
                // day.isToday && dynamicStyles.today,
                day.isSelected && dynamicStyles.selected,
                day.isInRange && dynamicStyles.inRange,
                day.isStartDate && dynamicStyles.startDate,
                day.isEndDate && dynamicStyles.endDate,
                day.isDisabled && dynamicStyles.disabled,
              ]}
              onPress={() => handleDayPress(day)}
              disabled={day.isDisabled}>
              <Text
                style={[
                  dynamicStyles.dayText,
                  day.isSelected && dynamicStyles.selectedText,
                  day.isInRange && dynamicStyles.inRangeText,
                  day.isToday && dynamicStyles.todayText,
                  day.isDisabled && dynamicStyles.disabledText,
                ]}>
                {day.day}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  // Render time picker
  const renderTimePicker = () => {
    if (showTimePicker) {
      return (
        <ModalTimePicker
          visible={showTimePicker}
          title={`Select ${timePickerMode === 'start' ? 'Start' : 'End'} Time`}
          onTimeSelected={(hour: number, minute: number, amPm: 'AM' | 'PM') => {
            const formattedTime = `${hour}:${minute.toString().padStart(2, '0')}`;
            if (timePickerMode === 'start') {
              setSelectedTimeStart(formattedTime);
              setAmPmStart(amPm);
            } else {
              setSelectedTimeEnd(formattedTime);
              setAmPmEnd(amPm);
            }
          }}
          initialHour={
            timePickerMode === 'start'
              ? parseInt(selectedTimeStart.split(':')[0])
              : parseInt(selectedTimeEnd.split(':')[0])
          }
          initialMinute={
            timePickerMode === 'start'
              ? parseInt(selectedTimeStart.split(':')[1])
              : parseInt(selectedTimeEnd.split(':')[1])
          }
          initialAmPm={timePickerMode === 'start' ? amPmStart : amPmEnd}
          onClose={() => setShowTimePicker(false)}
        />
      );
    }
    return null;
  };

  // Render time selectors
  const renderTimeSelectors = () => {
    if (!showTime || mode === 'date') {
      return null;
    }

    return (
      <View style={dynamicStyles.timeSelectorsContainer}>
        <View style={dynamicStyles.timeSelector}>
          <TouchableOpacity
            style={dynamicStyles.selectedTimeDisplay}
            onPress={() => {
              setTimePickerMode('start');
              setShowTimePicker(true);
            }}>
            <Text style={dynamicStyles.timeDisplay}>{selectedTimeStart}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={dynamicStyles.amPmButton} onPress={() => toggleAmPm('start')}>
            <Text style={dynamicStyles.amPmText}>{amPmStart}</Text>
          </TouchableOpacity>
        </View>

        {allowRangeSelection && dateRange.endDate && (
          <>
            <Text style={dynamicStyles.timeSeparator}>—</Text>
            <View style={dynamicStyles.timeSelector}>
              <TouchableOpacity
                style={dynamicStyles.selectedTimeDisplay}
                onPress={() => {
                  setTimePickerMode('end');
                  setShowTimePicker(true);
                }}>
                <Text style={dynamicStyles.timeDisplay}>{selectedTimeEnd}</Text>
              </TouchableOpacity>

              <TouchableOpacity style={dynamicStyles.amPmButton} onPress={() => toggleAmPm('end')}>
                <Text style={dynamicStyles.amPmText}>{amPmEnd}</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    );
  };

  // Render text input field for adding players
  const renderPlayerInput = () => {
    const hasSelectedPlayers = selectedPlayers && selectedPlayers.length > 0;

    return (
      <View style={dynamicStyles.playersContainer}>
        <TouchableOpacity style={dynamicStyles.addPlayersButton} onPress={onAddPlayers}>
          {hasSelectedPlayers ? (
            <View style={dynamicStyles.selectedPlayersContainer}>
              <Text style={dynamicStyles.addPlayersText}>Other Players : </Text>
              <View style={dynamicStyles.playerAvatarsContainer}>
                {selectedPlayers.slice(0, 3).map((player, index) => (
                  <CImage
                    key={player.id}
                    source={{uri: player.image}}
                    style={{
                      ...dynamicStyles.playerAvatar,
                      marginLeft: index > 0 ? -7 : 0,
                    }}
                  />
                ))}
                {selectedPlayers.length > 3 && (
                  <View style={dynamicStyles.morePlayersCircle}>
                    <Text style={dynamicStyles.morePlayersText}>+{selectedPlayers.length - 3}</Text>
                  </View>
                )}
              </View>
              {/* <Text style={dynamicStyles.addPlayersText}>{selectedPlayers.length}</Text> */}
            </View>
          ) : (
            <Text style={dynamicStyles.addPlayersText}>+ Add Players</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  };

  // Render action buttons
  const renderActions = () => {
    return (
      <View style={dynamicStyles.actionsContainer}>
        <CButton
          title="Reserve Court"
          variant="primary"
          containerStyle={dynamicStyles.reserveButton}
          onPress={handleConfirm}
          textStyle={dynamicStyles.standInLineText}
          isDisabled={!dateRange.startDate}
        />
        {/* {onClose && ( */}
        <CButton
          title="Stand in line"
          variant="outline"
          textStyle={dynamicStyles.standInLineText}
          containerStyle={dynamicStyles.standInLineButton}
          onPress={onClose}
        />
        {/* )} */}
      </View>
    );
  };

  return (
    <View style={[dynamicStyles.container]}>
      {title ? (
        <View style={dynamicStyles.modalHeader}>
          <Text style={dynamicStyles.modalTitle}>{title}</Text>
        </View>
      ) : null}

      {renderCalendarGrid()}
      {renderTimeSelectors()}
      {renderTimePicker()}
      {renderPlayerInput()}
      {renderActions()}
    </View>
  );
};

// Create dynamic styles based on theme
const createDynamicStyles = (theme: any) => {
  return StyleSheet.create({
    container: {
      width: '100%',
      borderRadius: 15,
      backgroundColor: theme.colors.greyBackground, //theme.colors.greyBackground,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      padding: 16,
    },
    modalHeader: {
      alignItems: 'center',
      marginBottom: 15,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.activeColor,
    },
    dateRangeDisplay: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 15,
    },
    rangeText: {
      fontSize: 16,
      color: theme.colors.activeColor,
    },
    daysCount: {
      fontSize: 16,
      color: theme.colors.activeColor,
    },
    calendarContainer: {
      marginBottom: 15,
    },
    monthSelector: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 20,
      alignItems: 'center',
      marginBottom: 10,
    },
    monthNavContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 20,
    },
    monthNavButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: theme.colors.actionBackground,
      justifyContent: 'center',
      alignItems: 'center',
    },
    monthTitle: {
      height: 36,
      borderRadius: 18,
      paddingHorizontal: 10,
      textAlignVertical: 'center',
      textAlign: 'center',
      fontSize: 16,
      color: theme.colors.activeColor,
      backgroundColor: theme.colors.actionBackground,
    },
    weekDayHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    weekDayText: {
      flex: 1,
      textAlign: 'center',
      fontSize: 14,
      color: theme.colors.activeColor,
      fontWeight: '600',
    },
    daysContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'flex-start',
    },
    dayButton: {
      width: '14.28%',
      height: 40,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 5,
    },
    dayText: {
      fontSize: 15,
      color: theme.colors.activeColor,
    },
    currentMonth: {},
    otherMonth: {
      opacity: 0.5,
    },
    today: {
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      borderRadius: 20,
    },
    todayText: {
      fontWeight: 'bold',
    },
    selected: {
      borderColor: theme.colors.activeColor,
      borderWidth: 1,
      borderRadius: 40,
    },
    selectedText: {
      color: theme.colors.white,
      fontWeight: 'bold',
    },
    inRange: {
      backgroundColor: `${theme.colors.activeColor}33`, // 20% opacity
    },
    inRangeText: {
      color: theme.colors.activeColor,
    },
    startDate: {
      borderColor: theme.colors.activeColor,
      borderWidth: 1,
    },
    endDate: {
      borderColor: theme.colors.activeColor,
      borderWidth: 1,
    },
    disabled: {
      opacity: 1,
    },
    disabledText: {
      color: theme.colors.midGrey,
    },
    timeSelectorsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 15,
    },
    timeSelector: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    selectedTimeDisplay: {
      backgroundColor: theme.colors.actionBackground,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      borderRadius: 20,
      paddingVertical: 8,
      paddingHorizontal: 16,
      minWidth: 70,
      alignItems: 'center',
    },
    timeDisplay: {
      color: theme.colors.activeColor,
      fontSize: 15,
    },
    amPmButton: {
      backgroundColor: theme.colors.actionBackground,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      borderRadius: 20,
      paddingVertical: 8,
      paddingHorizontal: 10,
      marginLeft: 5,
      minWidth: 45,
      alignItems: 'center',
    },
    amPmText: {
      color: theme.colors.activeColor,
      fontSize: 15,
    },
    timeSeparator: {
      color: theme.colors.activeColor,
      fontSize: 15,
      marginHorizontal: 10,
    },
    playersContainer: {
      marginBottom: 15,
    },
    addPlayersButton: {
      flexDirection: 'row',
      backgroundColor: theme.colors.actionBackground, // Add 10% opacity to activeColor
      borderRadius: 25,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      alignItems: 'center',
    },
    addPlayersText: {
      color: theme.colors.activeColor,
      fontSize: 15,
    },
    actionsContainer: {
      flexDirection: 'row',
      // flex: 1,
      justifyContent: 'space-between',
    },
    reserveButton: {
      flex: 1,
      marginRight: 8,
      borderRadius: 40,
    },
    standInLineText: {
      fontSize: 14,
    },
    standInLineButton: {
      flex: 1,
      borderRadius: 40,
      borderColor: theme.colors.white,
      backgroundColor: theme.colors.actionBackground,
      borderWidth: 1,
      // flex: 1,
      // marginLeft: 8,
    },
    selectedPlayersContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    playerAvatarsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    playerAvatar: {
      width: 24,
      height: 24,
      borderRadius: 12,
      marginLeft: 5,
    },
    morePlayersCircle: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: theme.colors.activeColor,
      justifyContent: 'center',
      alignItems: 'center',
    },
    morePlayersText: {
      color: theme.colors.white,
      fontSize: 12,
      fontWeight: 'bold',
    },
  });
};

const DatePickerDemo: React.FC = () => {
  const navigation = useNavigation();
  const theme = useThemeStore();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      padding: 20,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 30,
    },
    backButton: {
      padding: 5,
      marginRight: 15,
    },
    title: {
      fontSize: 24,
      color: theme.colors.white,
      marginBottom: 0,
    },
    content: {
      flex: 1,
    },
    demoSection: {
      backgroundColor: theme.colors.semiTransparentWhite,
      borderRadius: 12,
      padding: 20,
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.highlightColor,
      marginBottom: 15,
    },
    dateDisplay: {
      marginBottom: 15,
    },
    dateLabel: {
      fontSize: 16,
      color: theme.colors.white,
      marginBottom: 5,
      fontWeight: '500',
    },
    dateValue: {
      fontSize: 16,
      color: theme.colors.transparentWhite1,
    },
    optionsSection: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: 10,
      paddingTop: 15,
      borderTopWidth: 1,
      borderTopColor: theme.colors.semiTransparentWhite,
    },
    optionLabel: {
      fontSize: 16,
      color: theme.colors.white,
      fontWeight: '500',
    },
    toggleButton: {
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 20,
    },
    toggleActive: {
      backgroundColor: theme.colors.highlightColor,
    },
    toggleInactive: {
      backgroundColor: theme.colors.graniteGrey,
    },
    toggleText: {
      fontWeight: 'bold',
      color: theme.colors.black,
    },
    button: {
      marginTop: 20,
    },
  });

  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: null,
    endDate: null,
  });

  const handleBack = () => {
    navigation.goBack();
  };

  const handleConfirm = (range: DateRange) => {
    setDateRange(range);
    // hideDatePicker();
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={handleBack}
            style={styles.backButton}
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}>
            <FontAwesome name="arrow-left" size={20} color={theme.colors.white} />
          </TouchableOpacity>
          <Text style={[styles.title]}>Date Picker Demo</Text>
        </View>

        <DateTimePicker
          onClose={() => {}}
          onConfirm={handleConfirm}
          initialStartDate={dateRange.startDate}
          initialEndDate={dateRange.endDate}
          allowRangeSelection
          minDate={new Date()}
          title=""
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default DateTimePicker;
