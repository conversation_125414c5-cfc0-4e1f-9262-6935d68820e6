/**
 * This script updates import paths to use path aliases in all TypeScript files
 * Run with: node update-imports.js
 */
const fs = require('fs');
const path = require('path');

// Configuration
const srcDir = path.join(__dirname, 'src');
const fileExtensions = ['.ts', '.tsx'];
const pathMappings = {
  '../navigation': '@navigation/index',
  '../../navigation': '@navigation/index',
  '../store': '@store',
  '../../store': '@store',
  '../utils': '@utils',
  '../../utils': '@utils',
  '../components': '@components/index',
  '../../components': '@components/index',
  '../config': '@config',
  '../../config': '@config',
  '../assets': '@assets',
  '../../assets': '@assets',
  '../data': '@data',
  '../../data': '@data',
  '../screens': '@screens',
  '../../screens': '@screens',
};

// Function to recursively find all files in a directory
function findFiles(dir, extensions, filesFound = []) {
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && file !== 'node_modules') {
      findFiles(filePath, extensions, filesFound);
    } else if (stat.isFile() && extensions.includes(path.extname(file))) {
      filesFound.push(filePath);
    }
  }

  return filesFound;
}

// Function to update import paths in a file
function updateImportPaths(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;

  for (const [oldPath, newPath] of Object.entries(pathMappings)) {
    // Regular expressions to match different import styles
    const defaultImportRegex = new RegExp(`import (.+?) from ['"]${oldPath}['"]`, 'g');
    const namedImportsRegex = new RegExp(`import {([^}]+)} from ['"]${oldPath}['"]`, 'g');
    const subpathRegex = new RegExp(`import (.+?) from ['"]${oldPath}/([^'"]+)['"]`, 'g');
    const namedSubpathRegex = new RegExp(`import {([^}]+)} from ['"]${oldPath}/([^'"]+)['"]`, 'g');

    // Replace default imports
    content = content.replace(defaultImportRegex, (match, importName) => {
      updated = true;
      return `import ${importName} from '${newPath}'`;
    });

    // Replace named imports
    content = content.replace(namedImportsRegex, (match, importNames) => {
      updated = true;
      return `import {${importNames}} from '${newPath}'`;
    });

    // Replace subpath imports
    content = content.replace(subpathRegex, (match, importName, subpath) => {
      updated = true;
      const basePath = newPath.split('/')[0]; // Get @folder part
      return `import ${importName} from '${basePath}/${subpath}'`;
    });

    // Replace named subpath imports
    content = content.replace(namedSubpathRegex, (match, importNames, subpath) => {
      updated = true;
      const basePath = newPath.split('/')[0]; // Get @folder part
      return `import {${importNames}} from '${basePath}/${subpath}'`;
    });
  }

  if (updated) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated imports in: ${filePath}`);
  }
}

// Find all TypeScript files
const files = findFiles(srcDir, fileExtensions);
console.log(`Found ${files.length} TypeScript files to process`);

// Update import paths in all files
for (const file of files) {
  updateImportPaths(file);
}

console.log('All files processed. Import paths have been updated.');
