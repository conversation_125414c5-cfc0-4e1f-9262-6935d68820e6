import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    root: {
      flex: 1,
      paddingHorizontal: 16,
    },
    title: {
      color: theme.colors.text,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    scrollContainer: {
      // flexGrow: 1,
      // paddingVertical: 20,
    },
    form: {
      marginBottom: 20,
      marginTop: 20,
    },
    inputContainer: {
      marginBottom: 16,
    },
    label: {
      fontWeight: '500',
      color: theme.colors.text,
      fontSize: theme.fontSize.font14,
    },
    input: {
      height: 50,
      paddingHorizontal: 16,
      borderRadius: 8,
      borderWidth: 1,
    },
    radioContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      columnGap: 10,
    },
    radioTitle: {
      color: theme.colors.text,
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: 10,
      // flex: 1,
    },
    buttonText: {
      color: theme.colors.orange,
      fontSize: theme.fontSize.medium,
      fontWeight: '400',
    },
    flex1: {
      flex: 1,
    },
    btnStyle: {
      borderWidth: 1,
    },
    errorText: {
      color: theme.colors.coralRed,
      marginTop: -3,
      fontSize: theme.fontSize.small,
      marginBottom: -5,
    },
    subTitle: {
      color: theme.colors.text,
      fontSize: theme.fontSize.medium,
      marginTop: 5,
    },
  });
