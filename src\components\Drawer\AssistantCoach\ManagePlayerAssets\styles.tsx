import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    padding16: {
      paddingHorizontal: 16,
    },
    closeButton: {
      alignItems: 'flex-end',
      margin: 12,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingBottom: 30,
      paddingHorizontal: 10,
      marginTop: 10,
      gap: 16,
    },
    headerRow: {
      gap: 5,
    },
    headerCol: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 5,
    },
    filterContainer: {
      alignItems: 'center',
      justifyContent: 'space-between',
      flexDirection: 'row',
      marginTop: 10,
    },
    collapsibleContainer: {
      borderWidth: 1,
      borderRadius: 12,
      padding: 10,
      borderColor: theme.colors.secondary,
    },
    collapsibleTitle: {
      color: theme.colors.text,
      fontSize: 14,
      flex: 1,
    },
    customHeader: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    avatar: {
      width: 50,
      height: 50,
      borderRadius: 100,
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.secondary,
      width: '100%',
      marginVertical: 10,
    },
    iconContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 5,
      marginTop: 5,
    },
    name: {
      lineHeight: 30,
    },
    videoContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 16,
    },
    videoItem: {
      width: 100,
      height: 100,
      borderRadius: 14,
      backgroundColor: theme.colors.shadowColor,
    },
    videoCardContent: {
      flex: 1,
    },
    videoCardHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: 5,
    },
  });
