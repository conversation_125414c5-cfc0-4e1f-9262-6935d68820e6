import React from 'react';
import {View, StyleSheet} from 'react-native';
import ParksSearch from '../ParksSearch';

interface SelectParkSectionProps {
  onParkSelect: (park: any) => void;
}

export const SelectParkSection: React.FC<SelectParkSectionProps> = ({onParkSelect}) => {
  return (
    <View style={styles.container}>
      <ParksSearch onParkSelect={onParkSelect} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
