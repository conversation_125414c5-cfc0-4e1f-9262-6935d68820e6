// components/UtrRatingSlider.tsx
import React, {useState, useMemo} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import Slider from '@react-native-community/slider';
import Typography from './Typography';
import {useThemeStore} from '@/store';

const RatingSlider: React.FC = () => {
  const [rating, setRating] = useState<number>(16.5);
  const theme = useThemeStore();

  const styles = useMemo(
    () =>
      StyleSheet.create({
        range: {
          color: theme.colors.offWhite,
        },
        sliderContainer: {
          flexDirection: 'row',
          flexWrap: 'nowrap',
          alignItems: 'center',
          marginBottom: 10,
        },
        slider: {
          width: '92%',
          backgroundColor: '#ddd',
          borderRadius: 16, // half of height for full rounding
          height: 26, // adjust as needed for your design
          marginTop: 5,
        },
        value: {
          color: theme.colors.offWhite,
          marginLeft: 8,
          fontSize: 14,
          fontWeight: 'bold',
        },
      }),
    [theme],
  );

  return (
    <View style={{paddingVertical: 16}}>
      <Typography variant="subtitle" color={theme.colors.offWhite}>
        UTR Rating <Text style={styles.range}>(1 - 16.5)</Text>
      </Typography>
      <View style={styles.sliderContainer}>
        <Slider
          style={styles.slider}
          minimumValue={1}
          maximumValue={16.5}
          step={0.1}
          value={rating}
          onValueChange={setRating}
          minimumTrackTintColor="#ccc"
          maximumTrackTintColor="#ccc"
          thumbTintColor="#000"
        />
        <Text style={styles.value}>{rating.toFixed(1)}</Text>
      </View>
    </View>
  );
};

export default RatingSlider;
