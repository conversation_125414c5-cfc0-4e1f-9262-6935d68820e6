import {Header, SafeAreaView} from '@/components';
import CustomDateTimePicker from '@/components/CustomDateTimePicker/CustomDateTimePicker';
import InvitePlayers from '@/components/InvitePlayers';
import ParkCard from '@/components/ParkCard';
import Typography from '@/components/Typography';
import useTranslation from '@/hooks/useTranslation';
import {useConfigStore, useThemeStore} from '@/store';
import BottomSheet from '@gorhom/bottom-sheet';
import {DrawerActions, NavigationProp, useNavigation} from '@react-navigation/native';
import React, {useRef, useState} from 'react';
import {StyleSheet, ScrollView} from 'react-native';
// import BottomSheet, {BottomSheetCompHandles} from '@/components/BottomSheet';

const DateScreen = ({route}: {route: any}) => {
  console.log('route', route);
  const theme = useThemeStore();
  const {bookingData} = useConfigStore();
  const navigation = useNavigation<NavigationProp<any>>();
  const bottomSheetRef = useRef<any>(null);
  const {t} = useTranslation();

  const [dateTime, setDateTime] = useState<any>(null);
  const {parkData} = route?.params || {};
  const [selectedPlayers, setSelectedPlayers] = useState<any>([]);
  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const openBottomSheet = () => {
    bottomSheetRef.current?.snapToIndex(1); // Open to full height
  };
  // Handle date updates in real-time
  const handleDateChange = (dates: {startDate: Date | null; endDate: Date | null}) => {
    console.log('dates', JSON.stringify(dates));
    const newDate = JSON.parse(JSON.stringify(dates));
    // Only update dateTime in booking data, don't navigate
    if (newDate.startDate) {
      setDateTime({
        ...dateTime,
        startDate: newDate.startDate,
        endDate: newDate.endDate,
      });
    }
  };

  // Handle time updates in real-time
  const handleTimeChange = (times: {startTime: string | null; endTime: string | null}) => {
    // Only update dateTime in booking data, don't navigate
    const newTime = JSON.parse(JSON.stringify(times));
    setDateTime({
      ...dateTime,
      startTime: newTime.startTime,
      endTime: newTime.endTime,
    });
  };
  return (
    <>
      <SafeAreaView includeBottom={false} style={styles(theme).container}>
        <Header
          backgroundImage={true}
          parkData={parkData}
          leftIcon={{
            name: 'side-menu',
            size: 24,
            color: theme.colors.white,
            containerStyle: theme.colors.primary,
          }}
          leftIconButtonStyle={styles(theme).menuButton}
          onLeftPress={openDrawer}
          backgroundColor="transparent"
          mapBackButton
          onSkip={() => {
            navigation.navigate('EquipmentReservationScreen', {
              bookingData: {
                parkData: parkData,
                dateTime: dateTime,
              },
            });
          }}
        />
        <ScrollView
          contentContainerStyle={styles(theme).datePickerView}
          keyboardShouldPersistTaps="handled"
          scrollEnabled={true}
          showsVerticalScrollIndicator={false}
          bounces={false}>
          <ParkCard
            parkData={parkData}
            onSelect={d => {
              console.log('Selected park data:', d);
            }}
            style={{marginBottom: 10, marginTop: 20}}
            variant="equipment"
            onEditPress={() => {
              // navigation.reset({
              //   index: 0,
              //   routes: [{name: 'SearchScreen'}],
              // });

              // need to remove below code and uncomment above code when stack navigation issue will fix.
              navigation.navigate('Drawer', {
                screen: 'MainTabs',
                params: {
                  screen: 'SEARCH',
                  params: {
                    screen: 'SearchScreen',
                  },
                },
              });
            }}
          />
          <Typography
            variant="subtitle"
            color={theme.colors.white}
            style={styles(theme).titleStyle}>
            {t('dateScreen.selectDateAndTime')}
          </Typography>
          <CustomDateTimePicker
            onConfirm={() => {
              navigation.navigate('EquipmentReservationScreen', {
                bookingData: {
                  parkData: parkData,
                  dateTime: dateTime,
                },
              });
            }}
            // onClose={handleBack}
            allowRangeSelection
            minDate={new Date()}
            title=""
            onAddPlayers={() => openBottomSheet()}
            selectedPlayers={selectedPlayers || []}
            initialStartDate={bookingData?.dateTime?.startDate || null}
            initialEndDate={bookingData?.dateTime?.endDate || null}
            initialStartTime={bookingData?.dateTime?.startTime || null}
            initialEndTime={bookingData?.dateTime?.endTime || null}
            onDateChange={handleDateChange}
            onTimeChange={handleTimeChange}
          />
        </ScrollView>
      </SafeAreaView>
      <BottomSheet
        backgroundStyle={{backgroundColor: theme.colors.background}}
        handleIndicatorStyle={{backgroundColor: theme.colors.bottomSheetBackground}}
        enableOverDrag={false}
        enablePanDownToClose
        keyboardBlurBehavior="restore"
        topInset={100}
        index={-1}
        ref={bottomSheetRef}
        style={{paddingHorizontal: 20}}
        snapPoints={['25%', '50%', '90%']}
        onChange={() => {}}>
        <InvitePlayers
          onConfirm={(data: any) => {
            bottomSheetRef.current?.close();
            setSelectedPlayers(data);
          }}
          initialSelectedPlayers={selectedPlayers || []}
        />
      </BottomSheet>
    </>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    datePickerView: {
      flexGrow: 1,
      paddingHorizontal: 16,
      paddingBottom: 40,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
    },
    titleStyle: {
      marginVertical: 10,
      fontSize: theme.fontSize.xlarge,
      fontWeight: 'bold',
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    backButtonStyle: {
      backgroundColor: theme.colors.dimGray,
      borderRadius: 10,
      padding: 5,
    },
  });
export default DateScreen;
