import React, {ReactNode} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  ImageBackground,
  Linking,
  Pressable,
} from 'react-native';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {useThemeStore} from '@/store/themeStore';
import {Icon} from '@components/index';
import Typography from '../Typography';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';

type NavigationProp = StackNavigationProp<RootStackParamList>;
import {useAuthStore, useConfigStore} from '@/store';
import {Images} from '@/config';
import {MapPin} from '@/data';
import useTranslation from '@/hooks/useTranslation';

interface IconConfig {
  name: string;
  color?: string;
  size?: number;
  style?: any;
  badge?: number | boolean;
  badgeColor?: string;
  containerStyle?: any;
  onPress?: () => void;
}

interface HeaderProps {
  title?: string;
  titleStyle?: any;
  leftIcon?: string | IconConfig;
  leftIconContainerStyle?: any;
  leftIconButtonStyle?: any;
  rightIcons?: (string | IconConfig)[];
  rightIconsContainerStyle?: any;
  onLeftPress?: () => void;
  onRightPress?: (index: number) => void;
  showBackButton?: boolean;
  centerTitle?: boolean;
  backgroundColor?: string;
  containerStyle?: any;
  headerStyle?: any;
  leftComponent?: ReactNode;
  centerComponent?: ReactNode;
  rightComponent?: ReactNode;
  transparent?: boolean;
  hideStatusBar?: boolean;
  pageTitle?: string;
  showBack?: boolean;
  pageTitleStyle?: any;
  backNavigation?: () => void;
  isBackPress?: boolean;
  mapBackButton?: boolean;
  backgroundImage?: boolean;
  onSkip?: () => void;
  parkData?: MapPin;
  skipBtn?: boolean;
}

const Header = ({
  title,
  titleStyle,
  leftIcon,
  leftIconContainerStyle,
  leftIconButtonStyle,
  rightIcons,
  rightIconsContainerStyle,
  onLeftPress,
  onRightPress,
  showBackButton = false,
  centerTitle = true,
  backgroundColor,
  containerStyle,
  headerStyle,
  leftComponent,
  centerComponent,
  rightComponent,
  transparent = false,
  hideStatusBar = false,
  pageTitle,
  pageTitleStyle,
  showBack = true,
  backNavigation,
  isBackPress = true,
  mapBackButton = false,
  backgroundImage = false,
  onSkip,
  parkData = {},
  skipBtn = true,
}: HeaderProps) => {
  const navigation = useNavigation<NavigationProp>();
  const theme = useThemeStore();
  const insets = useSafeAreaInsets();
  const {userLocation} = useAuthStore();
  const {t} = useTranslation();

  const {getCartData} = useConfigStore();

  const cartData = getCartData() || [];
  console.log('cartData=====>>>>>', cartData.length);

  const handleBackPress = () => {
    const iconName = typeof leftIcon === 'string' ? leftIcon : leftIcon?.name;
    if (iconName === 'side-menu') {
      navigation.dispatch(DrawerActions.openDrawer());
    } else if (onLeftPress) {
      onLeftPress();
    } else {
      navigation.goBack();
    }
  };

  const renderIcon = (icon: string | IconConfig, index?: number) => {
    const iconConfig: IconConfig = typeof icon === 'string' ? {name: icon} : icon;
    const {
      name,
      color,
      size,
      style,
      badge,
      badgeColor,
      containerStyle: iconContainerStyle,
    } = iconConfig;

    return (
      <View key={`${name}-${index}`} style={[styles(theme).iconContainer, iconContainerStyle]}>
        <Icon name={name} size={size || 24} color={color || theme.colors.white} style={style} />
        {name === 'cart'
          ? cartData?.length > 0 && (
              <View
                style={[
                  styles(theme).badge,
                  {backgroundColor: badgeColor || theme.colors.activeColor},
                ]}>
                <Text style={styles(theme).badgeText}>
                  {cartData?.length > 9 ? '9+' : cartData?.length}
                </Text>
              </View>
            )
          : badge && (
              <View
                style={[
                  styles(theme).badge,
                  {backgroundColor: badgeColor || theme.colors.activeColor},
                ]}>
                {typeof badge === 'number' && badge > 0 && (
                  <Text style={styles(theme).badgeText}>{badge > 9 ? '9+' : badge}</Text>
                )}
              </View>
            )}
      </View>
    );
  };

  const renderLeftComponent = () => {
    const iconName = typeof leftIcon === 'string' ? leftIcon : leftIcon?.name;

    if (leftComponent) {
      return leftComponent;
    }

    if (showBackButton || leftIcon) {
      // Create proper icon configuration for the left icon
      let iconToRender: string | IconConfig;

      if (leftIcon) {
        if (typeof leftIcon === 'string') {
          iconToRender = {
            name: leftIcon,
            containerStyle: leftIconContainerStyle,
          };
        } else {
          iconToRender = {
            ...leftIcon,
            containerStyle: leftIconContainerStyle || leftIcon.containerStyle,
          };
        }
      } else {
        // Default back arrow icon
        iconToRender = {
          name: 'arrow-left',
          containerStyle: leftIconContainerStyle,
        };
      }

      return (
        <TouchableOpacity
          style={[
            iconName === 'side-menu' ? styles(theme).menuButton : styles(theme).iconButton,
            leftIconButtonStyle,
          ]}
          onPress={handleBackPress}
          hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
          {renderIcon(iconToRender)}
        </TouchableOpacity>
      );
    }

    return <View style={styles(theme).iconPlaceholder} />;
  };

  const renderCenterComponent = () => {
    if (centerComponent) {
      return centerComponent;
    }

    if (title) {
      return (
        <Text
          style={[
            styles(theme).title,
            {color: theme.colors.white, fontSize: theme.fontSize.large},
            titleStyle,
          ]}
          numberOfLines={1}>
          {title}
        </Text>
      );
    }

    return null;
  };

  const renderRightComponent = () => {
    if (rightComponent) {
      return rightComponent;
    }

    if (rightIcons && rightIcons.length > 0) {
      return (
        <View style={[styles(theme).rightIconsContainer, rightIconsContainerStyle]}>
          {rightIcons.map((icon, index) => (
            <TouchableOpacity
              key={`right-icon-${index}`}
              style={styles(theme).iconButton}
              onPress={() => {
                const iconConfig = typeof icon === 'string' ? {name: icon} : icon;
                if (iconConfig.name === 'notification') {
                  navigation.navigate('NotificationsList');
                } else if (iconConfig.name === 'chat') {
                  navigation.navigate('Drawer', {
                    screen: 'MainTabs',
                    params: {
                      screen: 'COMMUNITY',
                      params: {
                        screen: 'CommunityHome',
                        params: {
                          type: 'groups',
                        },
                      },
                    },
                  });
                } else if (iconConfig.name === 'cart') {
                  navigation.navigate('CartScreen');
                } else {
                  onRightPress && onRightPress(index);
                }
              }}
              hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
              {renderIcon(icon, index)}
            </TouchableOpacity>
          ))}
        </View>
      );
    }

    return <View style={styles(theme).iconPlaceholder} />;
  };

  const headerBackgroundColor = transparent
    ? 'transparent'
    : backgroundColor || theme.colors.background;

  // Always use safe area insets for proper padding of the status bar
  const topPadding = insets.top;

  const renderPageTitleComponent = () => {
    if (pageTitle) {
      return (
        <>
          {showBack && (
            <TouchableOpacity
              onPress={() =>
                isBackPress && backNavigation ? backNavigation() : navigation.goBack()
              }>
              <Icon name="Left-chevron" size={20} color={theme.colors.white} />
            </TouchableOpacity>
          )}
          <Typography
            variant="pageTitle"
            color={pageTitleStyle?.color || theme.colors.white}
            style={styles(theme).pageTitle}>
            {pageTitle}
          </Typography>
        </>
      );
    } else if (mapBackButton) {
      return (
        <View style={styles(theme).backButtonContainer}>
          <TouchableOpacity
            activeOpacity={0.8}
            style={styles(theme).backButton}
            onPress={() =>
              isBackPress && backNavigation ? backNavigation() : navigation.goBack()
            }>
            <Icon name="back" size={24} color={theme.colors.white} />
          </TouchableOpacity>
          {skipBtn && (
            <TouchableOpacity activeOpacity={0.8} style={styles(theme).skipButton} onPress={onSkip}>
              <Typography variant="skip" color={theme.colors.white}>
                {t('common.skip')}
              </Typography>
              <Icon name="next" size={24} color={theme.colors.white} />
            </TouchableOpacity>
          )}
        </View>
      );
    }

    return null;
  };

  // const mapRef = useRef<MapView>(null);

  // useEffect(() => {
  //   if (mapRef.current && parkData?.coordinate && userLocation) {
  //     const coordinates = [
  //       parkData.coordinate,
  //       {latitude: Number(userLocation.latitude), longitude: Number(userLocation.longitude)},
  //     ];

  //     mapRef.current.fitToCoordinates(coordinates, {
  //       edgePadding: {top: 100, right: 100, bottom: 100, left: 100},
  //       animated: true,
  //     });
  //   }
  // }, [parkData, userLocation]);

  const openGoogleMaps = () => {
    try {
      if (parkData?.coordinate && userLocation) {
        const url = `https://www.google.com/maps/dir/?api=1&origin=${userLocation?.latitude},${userLocation?.longitude}&destination=${parkData?.coordinate?.latitude},${parkData?.coordinate?.longitude}&travelmode=driving`;
        Linking.openURL(url);
      }
    } catch (error) {
      console.log('Error opening Google Maps', error);
    }
  };

  return (
    <>
      {backgroundImage ? (
        <Pressable onPress={openGoogleMaps}>
          <ImageBackground
            source={Images.headerBg}
            style={[styles(theme).safeArea, containerStyle]}>
            {/* <TouchableOpacity onPress={openGoogleMaps} activeOpacity={1}>
            <Text style={{color: theme.colors.white, position: 'absolute', right: 10, top: 30}}>
              Open
            </Text>
          </TouchableOpacity> */}
            {/* <MapView
            ref={mapRef}
            provider={PROVIDER_GOOGLE}
            customMapStyle={darkMapStyle}
            style={{width: '100%', height: '100%', position: 'absolute'}}
            scrollEnabled={false}
            zoomEnabled={false}
            pitchEnabled={false}
            rotateEnabled={false}> */}
            {/* Park Marker */}
            {/* <Marker coordinate={parkData.coordinate} tracksViewChanges={false}>
              <CustomMarker type={parkData.type as 'available_kiosk' | 'planned_location'} />
            </Marker> */}

            {/* User Marker */}
            {/* <Marker
              coordinate={{
                latitude: Number(userLocation.latitude),
                longitude: Number(userLocation.longitude),
              }}
              pinColor="blue"
              tracksViewChanges={false}
            /> */}

            {/* Directions Path */}
            {/* <MapViewDirections
              origin={{
                latitude: Number(userLocation.latitude),
                longitude: Number(userLocation.longitude),
              }}
              destination={parkData.coordinate}
              apikey={'AIzaSyBYOw52zLyByyx024_iMsQl6LWgWbd9DkQ'}
              strokeWidth={4}
              strokeColor="#00BFFF" // Uber-style blue
              optimizeWaypoints={true}
              mode="DRIVING"
            /> */}
            {/* </MapView> */}
            <View
              style={[
                styles(theme).safeArea,
                {backgroundColor: headerBackgroundColor},
                {paddingTop: topPadding},
                containerStyle,
              ]}>
              {!hideStatusBar && (
                <StatusBar
                  barStyle="light-content"
                  backgroundColor={headerBackgroundColor}
                  translucent={transparent}
                />
              )}

              <View style={[styles(theme).header, headerStyle]}>
                <View style={styles(theme).leftContainer}>{renderLeftComponent()}</View>

                <View
                  style={[
                    styles(theme).titleContainer,
                    centerTitle && styles(theme).centeredTitleContainer,
                  ]}>
                  {renderCenterComponent()}
                </View>

                <View style={styles(theme).rightContainer}>{renderRightComponent()}</View>
              </View>
            </View>
            {(pageTitle || mapBackButton) && (
              <View style={styles(theme).pageTitleContainer}>{renderPageTitleComponent()}</View>
            )}
          </ImageBackground>
        </Pressable>
      ) : (
        <>
          <View
            style={[
              styles(theme).safeArea,
              {backgroundColor: headerBackgroundColor},
              {paddingTop: topPadding},
              containerStyle,
            ]}>
            {!hideStatusBar && (
              <StatusBar
                barStyle="light-content"
                backgroundColor={headerBackgroundColor}
                translucent={transparent}
              />
            )}
            <View style={[styles(theme).header, headerStyle]}>
              <View style={styles(theme).leftContainer}>{renderLeftComponent()}</View>

              <View
                style={[
                  styles(theme).titleContainer,
                  centerTitle && styles(theme).centeredTitleContainer,
                ]}>
                {renderCenterComponent()}
              </View>

              <View style={styles(theme).rightContainer}>{renderRightComponent()}</View>
            </View>
          </View>
          {(pageTitle || mapBackButton) && (
            <View style={styles(theme).pageTitleContainer}>{renderPageTitleComponent()}</View>
          )}
        </>
      )}
    </>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    safeArea: {
      width: '100%',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      height: 56,
      paddingHorizontal: 16,
      // borderBottomWidth: 1,
      // borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    },
    leftContainer: {
      minWidth: 40,
      alignItems: 'flex-start',
      justifyContent: 'center',
    },
    backButton: {
      padding: 10,
      backgroundColor: theme.colors.dimGray,
      borderRadius: 10,
    },
    skipButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    backButtonContainer: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: 8,
    },
    titleContainer: {
      flex: 1,
      marginHorizontal: 8,
      justifyContent: 'center',
    },
    centeredTitleContainer: {
      alignItems: 'center',
    },
    rightContainer: {
      minWidth: 40,
      alignItems: 'flex-end',
      justifyContent: 'center',
    },
    rightIconsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    title: {
      fontWeight: '600',
    },
    iconButton: {
      padding: 4,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    iconContainer: {
      position: 'relative',
    },
    iconPlaceholder: {
      width: 24,
      height: 24,
    },
    badge: {
      position: 'absolute',
      top: -5,
      right: -5,
      minWidth: 18,
      height: 18,
      borderRadius: 9,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 4,
    },
    badgeText: {
      color: '#000',
      fontSize: 10,
      fontWeight: 'bold',
    },
    pageTitleContainer: {
      paddingHorizontal: 16,
      marginVertical: 10,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    pageTitle: {
      // marginTop: 3,
    },
  });

export default Header;
