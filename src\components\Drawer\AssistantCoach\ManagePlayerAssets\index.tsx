import React from 'react';
import {FlatList, TouchableOpacity, View} from 'react-native';
import {useThemeStore} from '@/store';
import Typography from '@/components/Typography';
import {createStyles} from './styles';
import {useNavigation} from '@react-navigation/native';
import CoachWrapper from '@/components/CoachWrapper';
import {CImage, Icon} from '@/components';
import {DrawerNavigationProp} from '@react-navigation/drawer';
import {CollapsibleViewWithIcon} from '@/components/CollapsibleView';
import {Images} from '@/config';
import PlayerStatsChart from '@/components/PlayerStatsChart';
import useTranslation from '@/hooks/useTranslation';

type RootDrawerParamList = {
  CoachOptions: undefined;
  // ... other screens
};
type NavigationProp = DrawerNavigationProp<RootDrawerParamList>;

const ManagePlayerAssetsScreen = (props: any) => {
  const {onClose} = props;
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();

  const {t} = useTranslation();

  const data = [
    {
      id: 1,
      name: 'Player 1',
    },
    {
      id: 2,
      name: 'Player 2',
    },
    {
      id: 3,
      name: 'Player 3',
    },
  ];

  const renderItem = ({item}: {item: any}) => {
    return (
      <View style={styles.collapsibleContainer}>
        <CollapsibleViewWithIcon
          customHeader={
            <View style={styles.customHeader}>
              <CImage source={Images.coachProfile} resizeMode="contain" style={styles.avatar} />
              <View>
                <Typography variant="title2" color={theme.colors.white} style={styles.name}>
                  {item.name}
                </Typography>
                <Typography variant="frequencyTitle" color={theme.colors.text}>
                  in Beginners
                </Typography>
                <View style={styles.iconContainer}>
                  <Icon name="location-pin" size={24} color={theme.colors.activeColor} />
                  <Typography variant="selectAll" color={theme.colors.text}>
                    Fort Greene
                  </Typography>
                </View>
              </View>
            </View>
          }
          titleStyle={styles.collapsibleTitle}>
          <View>
            <View style={styles.divider} />
            <PlayerStatsChart
              variant="state"
              monthLabelStyle={{color: theme.colors.text}}
              containerStyle={{width: '100%', backgroundColor: 'transparent'}}
              showLegend={false}
              initialYear="2024"
            />
            <View style={styles.divider} />
            <View style={styles.filterContainer}>
              <Typography variant="parkTitle" color={theme.colors.text}>
                {t('common.videos')}
              </Typography>
              <TouchableOpacity activeOpacity={0.8}>
                <Typography variant="selectAll" color={theme.colors.offWhite}>
                  {t('common.seeMore')}
                </Typography>
              </TouchableOpacity>
            </View>
            <View style={styles.videoContainer}>
              <View style={styles.videoItem} />
              <View style={styles.videoCardContent}>
                <Typography variant="selectAll" color={theme.colors.text}>
                  January 6, 2025
                </Typography>
                <Typography variant="classCardTitle" color={theme.colors.text}>
                  Beginners
                </Typography>
                <Typography variant="classCardTitle" color={theme.colors.text}>
                  9:06am - 9:11am
                </Typography>
              </View>
              <Icon name="comment" size={24} color={theme.colors.white} />
            </View>
          </View>
        </CollapsibleViewWithIcon>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={[styles.headerRow, styles.padding16]}>
        <View style={styles.headerCol}>
          <TouchableOpacity onPress={onClose}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
          <Typography variant="invitePlayersTitle" color={theme.colors.white}>
            {t('managePlayerAssetsScreen.title')}
          </Typography>
        </View>
        <View style={styles.filterContainer}>
          <Typography variant="invitePlayersTitle" color={theme.colors.white}>
            {t('managePlayerAssetsScreen.students')}
          </Typography>
          <TouchableOpacity activeOpacity={0.8}>
            <Icon name="filter" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        </View>
      </View>
      <FlatList
        contentContainerStyle={styles.scrollContainer}
        data={data}
        renderItem={renderItem}
      />
    </View>
  );
};

export default ManagePlayerAssetsScreen;
