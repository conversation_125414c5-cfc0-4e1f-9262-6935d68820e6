import React, {useEffect, useMemo} from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  StyleProp,
  Platform,
} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {Icon} from '@/components';
import {BottomSheetTextInput} from '@gorhom/bottom-sheet';
import debounce from 'lodash-es/debounce';

interface SearchInputProps {
  placeholder: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholderTextColor?: string;
  variant?: 'primary' | 'light';
  onClear?: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  iconColor?: string;
  inputStyle?: StyleProp<any>;
  type?: 'search' | 'bottomSheetSearch';
  debounceTime?: number;
  onSearch?: (text: string) => void;
}

const SearchInput = ({
  placeholder,
  value,
  onChangeText,
  placeholderTextColor,
  variant = 'primary',
  onClear,
  containerStyle,
  inputStyle,
  iconColor,
  type,
  debounceTime = 300,
  onSearch,
}: SearchInputProps) => {
  const theme = useThemeStore();

  // Create debounced search function
  const debouncedSearch = useMemo(
    () =>
      debounce((text: string) => {
        if (onSearch) {
          onSearch(text);
        }
      }, debounceTime),
    [onSearch, debounceTime],
  );

  // Apply search when value changes
  useEffect(() => {
    if (onSearch) {
      debouncedSearch(value);
    }
  }, [value, debouncedSearch, onSearch]);

  // Handle text change
  const handleChangeText = (text: string) => {
    onChangeText(text);
  };

  return (
    <View
      style={[
        variant === 'light' ? styles(theme).lightSearchBar : styles(theme).primarySearchBar,
        containerStyle,
      ]}>
      <Icon
        name="search-1"
        size={24}
        color={iconColor || (variant === 'light' ? theme.colors.primary : theme.colors.white)}
      />
      {type === 'bottomSheetSearch' ? (
        <BottomSheetTextInput
          style={[
            variant === 'light' ? styles(theme).lightSearchInput : styles(theme).primarySearchInput,
            inputStyle,
          ]}
          placeholder={placeholder}
          placeholderTextColor={
            placeholderTextColor ||
            (variant === 'light' ? theme.colors.primary : theme.colors.white)
          }
          onChangeText={handleChangeText}
          value={value}
        />
      ) : (
        <TextInput
          style={[
            variant === 'primary'
              ? styles(theme).primarySearchInput
              : styles(theme).lightSearchInput,
            inputStyle,
          ]}
          placeholder={placeholder}
          placeholderTextColor={
            placeholderTextColor ||
            (variant === 'light' ? theme.colors.primary : theme.colors.white)
          }
          value={value}
          onChangeText={handleChangeText}
        />
      )}
      {value !== '' && (
        <TouchableOpacity onPress={onClear}>
          <Icon name="close" size={20} color={theme.colors.red} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    primarySearchBar: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      paddingHorizontal: 20,
      paddingVertical: Platform.OS === 'ios' ? 20 : 10,
      gap: 5,
    },
    lightSearchBar: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: 8,
      paddingHorizontal: 15,
      paddingVertical: Platform.OS === 'ios' ? 12 : 2,
      gap: 5,
      backgroundColor: theme.colors.white,
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    searchIcon: {
      marginRight: 8,
      fontSize: 16,
    },
    primarySearchInput: {
      flex: 1,
      color: theme.colors.text,
      fontSize: 16,
    },
    lightSearchInput: {
      flex: 1,
      color: theme.colors.primary,
      fontSize: 16,
    },
  });
export default SearchInput;
