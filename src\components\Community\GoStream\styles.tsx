import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 25,
    },

    content: {
      gap: 30,
    },

    cardImage: {
      width: '100%',
      height: 203,
    },

    cardContent: {
      width: '100%',
      height: 30,
      backgroundColor: theme.colors.black,
      justifyContent: 'center',
      alignItems: 'flex-end',
      paddingHorizontal: 10,
    },

    videoPreview: {
      width: '100%',
      height: 203,
    },
    videoContainer: {
      width: '100%',
      height: 203,
      overflow: 'hidden',
      backgroundColor: theme.colors.darkGray,
    },
    playButton: {
      backgroundColor: 'transparent',
      borderColor: theme.colors.orange,
      borderWidth: 5,
    },
    playArrow: {
      tintColor: theme.colors.orange,
    },
    video: {
      width: '100%',
      height: '100%',
    },
    thumbnail: {
      width: '100%',
      height: '100%',
    },
    wrapper: {
      flex: 1,
    },
  });
