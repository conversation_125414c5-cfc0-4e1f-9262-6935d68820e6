import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    root: {
      backgroundColor: theme.colors.TranslucentWhite,
      borderRadius: 10,
      paddingHorizontal: 15,
      borderColor: theme.colors.white,
      paddingVertical: 15,
      flexDirection: 'row',
      gap: 18,
    },
    contentContainer: {
      flex: 1,
    },
    title: {
      color: theme.colors.text,
    },
    description: {
      color: theme.colors.white,
      justifyContent: 'center',
    },
    descriptionContainer: {
      gap: 10,
      marginTop: 3,
    },
    thumbnailContainer: {
      height: 56,
      width: 56,
      overflow: 'hidden',
    },
    thumbnail: {
      height: '100%',
      width: '100%',
    },
  });
