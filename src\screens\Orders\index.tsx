import React, {useState} from 'react';
import {View, TouchableOpacity} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {useNavigation} from '@react-navigation/native';
import {Header, Icon, SafeAreaView} from '@/components';
import {OfferBanner} from '@/components/common/OfferBanner';
import SearchInput from '@/components/SearchInput';
import {styles as createStyles} from './styles';
import useTranslation from '@/hooks/useTranslation';

const OrdersScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation();

  const styles = createStyles(theme);

  const [searchValue, setSearchValue] = useState('');

  const {t} = useTranslation();

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with back button */}
      <Header
        leftComponent={
          <TouchableOpacity onPress={() => navigation.goBack()} activeOpacity={0.7}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        }
        rightIcons={[
          {name: 'notification', size: 24, color: theme.colors.activeColor},
          {name: 'chat', size: 24, badge: 14, color: theme.colors.activeColor},
        ]}
        pageTitle={t('ordersScreen.title')}
        backgroundColor={'transparent'}
        transparent={false}
        showBack={false}
      />
      <View style={styles.searchContainer}>
        <SearchInput
          placeholder={t('ordersScreen.searchOrders')}
          value={searchValue}
          onChangeText={setSearchValue}
        />
      </View>
      <View style={styles.footer}>
        <OfferBanner text="Invite friends, get 10% off" />
      </View>
    </SafeAreaView>
  );
};

export default OrdersScreen;
