import {Images} from './images';

export const drawerMenuList = [
  {
    id: 'notifications',
    label: 'drawer.notifications',
    title: 'Notifications',
    isVisible: true,
  },
  {
    id: 'refer_friend',
    label: 'drawer.referFriend',
    title: 'Refer a friend',
    isVisible: true,
  },
  {
    id: 'my_matches',
    label: 'drawer.myMatches',
    title: 'My Matches',
    isVisible: true,
  },
  // {
  //   id: 'payment',
  //   label: 'Payment',
  //   title: 'Payment',
  //   isVisible: true,
  // },
  {
    id: 'rewards',
    label: 'drawer.rewards',
    title: 'Rewards',
    isVisible: true,
  },
  {
    id: 'recycleBalls',
    label: 'drawer.recycleBalls',
    title: 'RecycleBalls',
    isVisible: true,
  },
  {
    id: 'help',
    label: 'drawer.help',
    title: 'Help',
    isVisible: true,
  },
  {
    id: 'settings',
    label: 'drawer.settings',
    screen: 'Settings',
    title: 'Settings',
    isVisible: true,
  },
  {
    id: 'manage_community',
    label: 'drawer.manageCommunity',
    title: 'Manage Community',
    isVisible: true,
  },
  {
    id: 'orders',
    label: 'drawer.orders',
    title: 'Orders',
    isVisible: true,
  },
];

export const createProfileRadioData = {
  bestDescribeOptions: [
    {label: 'createProfile.bestDescribeList.player', value: 'player'},
    {label: 'createProfile.bestDescribeList.coach', value: 'coach'},
    {label: 'createProfile.bestDescribeList.both', value: 'both'},
  ],
  describeFitnessOptions: [
    {label: 'createProfile.describeFitnessList.slow_and_steady', value: 'slow_and_steady'},
    {label: 'createProfile.describeFitnessList.workout_warrior', value: 'workout_warrior'},
    {label: 'createProfile.describeFitnessList.can_keep_a_rally', value: 'can_keep_a_rally'},
    {label: 'createProfile.describeFitnessList.play_competitively', value: 'play_competitively'},
  ],
};

export const parks = [
  {
    id: '1',
    name: 'Governor Smith Playground',
    location: 'Morris Avenue between E. 151 and E. 155 Streets',
  },
  {
    id: '2',
    name: 'Orchard Beach',
    location: 'Pelham Bay Park 1 Orchard Beach Road',
  },
  {
    id: '3',
    name: 'Seton Park',
    location: 'Seton Park W. 232nd to 235th St., Palisade and Independence Aves.',
  },
  {
    id: '4',
    name: "St. Mary's Park",
    location: "St. Mary's Park E. 145 St. and St. Ann's Ave.",
  },
  {
    id: '5',
    name: 'Haffen Park',
    location: 'Haffen Park Hammersley, Ely, and Gunther Aves.',
  },
  {
    id: '6',
    name: 'Pelham Bay Park',
    location: 'Pelham Bay Park Buckner Blvd. and Middletown Rd.',
  },
  {
    id: '7',
    name: 'St. James Park',
    location: 'St. James Park Jerome Ave. and E. 193d St.',
  },
  {
    id: '8',
    name: 'Stadium Tennis Center at Mill Pond Park',
    location: 'Mill Pond Park Gateway Center Boulevard (Exterior Street and E. 150th Street)',
  },
  {
    id: '9',
    name: 'Van Cortlandt Park',
    location: 'Van Cortlandt Park Stadium - W. 242nd St. and Broadway',
  },
  {
    id: '10',
    name: 'Williamsbridge Oval',
    location: 'E. 208th St. and Bainbridge Ave.',
  },
  {
    id: '11',
    name: 'Cooper Park Tennis Courts',
    location: 'Morgan Ave. between Maspeth Ave and Sharon Street',
  },
  {
    id: '12',
    name: 'Friends Field',
    location: '159th St. between S. Powell & 65th Ave.',
  },
  // Adding more mock data for pagination demonstration
  {
    id: '13',
    name: 'Central Park',
    location: '59th to 110th St., between Fifth Ave and Central Park West',
  },
  {
    id: '14',
    name: 'Prospect Park',
    location: 'Prospect Park West to Flatbush Ave, Park Slope',
  },
  {
    id: '15',
    name: 'Flushing Meadows Corona Park',
    location: 'Between Grand Central Pkwy and Van Wyck Expwy',
  },
  {
    id: '16',
    name: 'Brooklyn Bridge Park',
    location: 'Along the East River waterfront in Brooklyn',
  },
  {
    id: '17',
    name: 'Riverside Park',
    location: 'Along the Hudson River from 72nd to 158th St.',
  },
  {
    id: '18',
    name: 'Battery Park',
    location: 'Southern tip of Manhattan facing the harbor',
  },
  {
    id: '19',
    name: 'Washington Square Park',
    location: 'Fifth Ave, Waverly Pl, W. 4th St, and MacDougal St',
  },
  {
    id: '20',
    name: 'Bryant Park',
    location: 'Between 40th and 42nd St., and Fifth and Sixth Ave',
  },
];
export const equipmentData = [
  {label: 'Tennis', value: 'Tennis'},
  {label: 'Platform Tennis', value: 'Platform Tennis'},
];
export const padelData = [
  {label: 'Padel', value: 'Padel'},
  {label: 'Pickleball', value: 'Pickleball'},
];
export const pTennisData = [
  {label: 'Tennis', value: 'Tennis'},
  {label: 'Platform Tennis', value: 'Platform Tennis'},
  {label: 'Padel', value: 'Padel'},
  {label: 'Pickleball', value: 'Pickleball'},
];
export const pickleBallData = [
  {label: 'Tennis', value: 'Tennis'},
  {label: 'Platform Tennis', value: 'Platform Tennis'},
  {label: 'Padel', value: 'Padel'},
  {label: 'Pickleball', value: 'Pickleball'},
];

export const equipmentTabs = [
  {
    id: '1',
    iconName: 'racket4',
    name: 'racket',
  },
  {
    id: '2',
    iconName: 'racket3',
    name: 'padel',
  },
  {
    id: '3',
    iconName: 'ball',
    name: 'ball',
  },
  {
    id: '4',
    iconName: 'ball',
    name: 'bag',
  },
];

export const sportsData = [
  {
    id: 1,
    sportsTitle: 'Tennis',
    icon: 'tennis',
  },
  {
    id: 2,
    sportsTitle: 'Platform Tennis',
    icon: 'platform-tennis',
  },
  {
    id: 3,
    sportsTitle: 'Pickleball',
    icon: 'pickleball',
  },
  {
    id: 4,
    sportsTitle: 'Padel',
    icon: 'padel',
  },
];

export const servicesData = [
  {
    id: 1,
    sportsTitle: 'Re-Stringing',
    icon: 'stringing',
  },
  {
    id: 2,
    sportsTitle: 'Re-gripping',
    icon: 'gripping',
  },
];

export const myMatchesData = [
  {id: 'calendar', title: 'drawerMyMatches.calendar'},
  {id: 'matchHistory', title: 'drawerMyMatches.matchHistory'},
  {id: 'notifications', title: 'drawerMyMatches.notificationsWidget'},
  {id: 'chat', title: 'drawerMyMatches.chatWidget'},
];

export const communityTabs = [
  {id: 1, label: 'communityScreen.home', description: 'Main community page'},
  {id: 2, label: 'communityScreen.playerConnect', description: 'Connect with other players'},
  {id: 3, label: 'communityScreen.reviews', description: 'Equipment and facility reviews'},
  {id: 4, label: 'communityScreen.groups', description: 'Join community groups'},
  {id: 5, label: 'communityScreen.goLife', description: 'Lifestyle content'},
  {id: 6, label: 'communityScreen.upYourGame', description: 'Improve your skills'},
  {id: 7, label: 'communityScreen.goStream', description: 'Watch live streams'},
];

export const invitePlayerTabs = [
  {id: 1, title: 'invitePlayers.invitePlayerTabs.friends'},
  {id: 2, title: 'invitePlayers.invitePlayerTabs.groups'},
  {id: 3, title: 'invitePlayers.invitePlayerTabs.nearby'},
  {id: 4, title: 'invitePlayers.invitePlayerTabs.invite'},
];

export const communityHomeList = [
  {
    id: 1,
    icon: 'chat',
    iconType: 'MaterialIcons',
    source: Images.personMaker,
    title: 'Find Players Now',
    description:
      'Practice, match, play - or start your own player community with Player Connect. Go!',
    screenName: 'communityScreen.playerConnect',
  },
  {
    id: 2,
    icon: 'tennis',
    iconType: 'MaterialCommunityIcons',
    source: Images.review,
    title: 'Review: Dunlop SX 300',
    description: 'Provides awesome spin-end control without compromising power.',
    color: '#F59E0B',
    navigate: () => console.log('Navigate to Review'),
  },
  {
    id: 3,
    icon: 'people',
    iconType: 'MaterialIcons',
    source: Images.conversation,
    title: 'Join or start a conversation',
    description: 'Group convos for all levels on all topics of racquet sports. Go!',
    navigate: () => console.log('Navigate to Conversation'),
  },
  {
    id: 4,
    icon: 'play-circle',
    iconType: 'Feather',
    source: Images.goDeeper,
    title: 'Video: GoDeeper to play better',
    description: '5 minutes for 5 perfect service placements, for the win. Go!',
    navigate: () => console.log('Navigate to Video'),
  },
  {
    id: 5,
    icon: 'play-circle',
    iconType: 'Feather',
    source: Images.tips,
    title: 'Tips no one talks about',
    description: 'Roger finally reveals why his game lasted so long. Go!',
    navigate: () => console.log('Navigate to Tips'),
  },
  {
    id: 6,
    icon: 'flash',
    iconType: 'Ionicons',
    source: Images.goFit,
    title: 'GoFit: Play injury-free',
    description: 'Three easy moves to protect your wrists, knees, and ankles. Go!',
    navigate: () => console.log('Navigate to GoFit'),
  },
  {
    id: 7,
    icon: 'tennis-ball',
    iconType: 'MaterialCommunityIcons',
    source: Images.dunlop,
    title: 'Dunlop releases CX 200',
    description: 'Check out Dunlops feature article on the gear',
    color: '#F59E0B',
    navigate: () => console.log('Navigate to Dunlop'),
  },
];

export const findPayerTabs = [
  {id: 1, title: 'findPlayer.findPlayerTabs.schedulePlay'},
  {id: 2, title: 'findPlayer.findPlayerTabs.nearby'},
  {id: 3, title: 'findPlayer.findPlayerTabs.friends'},
  {id: 4, title: 'findPlayer.findPlayerTabs.group'},
  {id: 5, title: 'findPlayer.findPlayerTabs.invite'},
];
export const sportsList = [
  {id: 1, title: 'sportsList.tennis', value: 'tennis'},
  {id: 2, title: 'sportsList.platformTennis', value: 'platform-tennis'},
  {id: 3, title: 'sportsList.pickleball', value: 'pickleball'},
  {id: 4, title: 'sportsList.padel', value: 'padel'},
];
export const groupsList = [
  {id: 1, title: 'groupsList.friends', value: 'friends'},
  {id: 2, title: 'groupsList.invited', value: 'invited'},
  {id: 3, title: 'groupsList.sponsored', value: 'sponsored'},
];
