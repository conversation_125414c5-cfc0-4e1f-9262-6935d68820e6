import React from 'react';
import BottomSheetComp from '../BottomSheet';
import {StyleSheet, Text} from 'react-native';

const QactBottomSheet = () => {
  return (
    <BottomSheetComp>
      <Text style={styles.text}>Qact Sheet 🔍</Text>
      {/* <View style={styles.content}>
        <InvitePlaters />
      </View> */}
    </BottomSheetComp>
  );
};

const styles = StyleSheet.create({
  content: {flexGrow: 1},
  text: {
    fontSize: 20,
    fontWeight: 'bold',
  },
});

export default QactBottomSheet;
