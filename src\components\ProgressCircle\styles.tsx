import FONTS from '@/utils/fonts';
import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      padding: 20,
      borderRadius: 26,
      backgroundColor: 'rgba(43,43,43,0.1)', // More visible, soft dark
    },
    progressText: {
      color: theme.colors.text,
      marginBottom: 10,
    },

    progressContainer: {
      alignItems: 'center',
      flex: 1,
    },
    svgWrapper: {
      position: 'relative',
      width: 130,
      height: 130,
      justifyContent: 'center',
      alignItems: 'center',
    },
    progressTextContainer: {
      position: 'absolute',
      justifyContent: 'center',
      alignItems: 'center',
      width: '100%',
      height: '100%',
    },

    progressNumberText: {
      color: theme.colors.white,
    },
    maxProg: {
      fontSize: 12,
      color: theme.colors.placeholderColor,
    },
    progressDescText: {
      color: theme.colors.lightGray,
      fontSize: 14,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginTop: 10,
      gap: 10,
    },
    button: {
      backgroundColor: theme.colors.primary,
      padding: 10,
      borderRadius: 5,
    },
    buttonText: {
      color: theme.colors.white,
      fontWeight: 'bold',
    },
    svgRotated: {
      transform: [{rotate: '270deg'}],
    },
  });
