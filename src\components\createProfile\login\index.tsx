import React, {useRef} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {TextInput, View} from 'react-native';
import {styles as createStyles} from './styles';
import {Controller, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import CInput from '@components/CInput';
import CButton from '@components/CButton/index';
import RadioSelect from '@components/RadioSelect/index';
import Typography from '@/components/Typography';
import {useAuthStore} from '@/store';
import useTranslation from '@/hooks/useTranslation';

interface loginProps {
  completeProfile: () => void;
  setupLater: () => void;
}

const bestDescribeOptions = [
  {label: 'Player', value: 'player'},
  {label: 'Coach', value: 'coach'},
  {label: 'Both', value: 'both'},
];

const describeFitnessOptions = [
  {label: 'Slow and Steady', value: 'slow_and_steady'},
  {label: 'Workout Worrier', value: 'workout_worrier'},
  {label: 'Can keep a rally', value: 'can_keep_rally'},
  {label: 'Play Competitively', value: 'play_competitively'},
];

const Login = (props: loginProps) => {
  const {completeProfile = () => {}, setupLater = () => {}} = props;

  const theme = useThemeStore();
  const styles = createStyles(theme);
  const {user} = useAuthStore();
  const ageInputRef = useRef<TextInput>(null);
  const {t} = useTranslation();

  const schema = yup.object({
    age: yup
      .string()
      .required(t('createProfileLogin.ageRequired'))
      .matches(/^[0-9]+$/, t('createProfileLogin.ageNumber'))
      .test('is-valid-age', t('createProfileLogin.ageRange'), value => {
        const numValue = parseInt(value, 10);
        return !isNaN(numValue) && numValue >= 13 && numValue <= 120;
      }),
    userType: yup.string().required(t('createProfileLogin.userTypeRequired')),
    fitnessLevel: yup.string().required(t('createProfileLogin.fitnessLevelRequired')),
  });

  type FormData = yup.InferType<typeof schema>;

  const {
    control,
    handleSubmit,
    formState: {errors},
    setValue,
    watch,
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      age: '',
      userType: '',
      fitnessLevel: '',
    },
  });

  // Get the current values for the radio buttons
  const userTypeValue = watch('userType');
  const fitnessLevelValue = watch('fitnessLevel');

  const onSubmit = (data: FormData) => {
    console.log('data=====>>>>>', data);
    completeProfile();
    // Further actions with valid data
  };

  return (
    <View style={styles.root}>
      <Typography variant="subtitle2" style={styles.title}>
        {t('login.title')} {user?.name}
      </Typography>
      <Typography variant="bodyMedium1" style={styles.subTitle}>
        {t('login.subTitle')}
      </Typography>
      {/* <View style={styles.scrollContainer}> */}
      <View style={styles.form}>
        <View style={styles.inputContainer}>
          <Controller
            control={control}
            name="age"
            render={({field: {onChange, onBlur, value}}) => (
              <CInput
                labelStyle={styles.label}
                label={t('createProfileLogin.age')}
                showLabel={true}
                variant="light"
                placeholder={t('createProfileLogin.agePlaceholder')}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                hasError={!!errors.age}
                error={errors.age?.message}
                keyboardType="numeric"
                inputStyle={styles.input}
                containerStyle={{marginBottom: 0}}
                ref={ageInputRef}
                returnKeyType="done"
                blurOnSubmit={false}
                useBottomSheetInput={true}
              />
            )}
          />
        </View>

        <View style={styles.inputContainer}>
          <Typography variant="bodyMedium" style={styles.radioTitle}>
            {t('createProfileLogin.userType')}
          </Typography>
          <Controller
            control={control}
            name="userType"
            render={({field: {onChange}}) => (
              <View style={styles.radioContainer}>
                {bestDescribeOptions.map(option => (
                  <RadioSelect
                    key={option.value}
                    label={option.label}
                    selected={userTypeValue === option.value}
                    onPress={() => {
                      setValue('userType', option.value, {
                        shouldValidate: true,
                      });
                    }}
                  />
                ))}
              </View>
            )}
          />
          {errors.userType && (
            <Typography variant="body" style={styles.errorText}>
              {errors.userType.message}
            </Typography>
          )}
        </View>

        <View style={styles.inputContainer}>
          <Typography variant="bodyMedium" style={styles.radioTitle}>
            {t('createProfileLogin.fitnessLevel')}
          </Typography>
          <Controller
            control={control}
            name="fitnessLevel"
            render={({field: {onChange}}) => (
              <View style={styles.radioContainer}>
                {describeFitnessOptions.map(option => (
                  <RadioSelect
                    containerStyle={{width: 170}}
                    key={option.value}
                    label={option.label}
                    selected={fitnessLevelValue === option.value}
                    onPress={() => {
                      setValue('fitnessLevel', option.value, {
                        shouldValidate: true,
                      });
                    }}
                  />
                ))}
              </View>
            )}
          />
          {errors.fitnessLevel && (
            <Typography variant="body" style={styles.errorText}>
              {errors.fitnessLevel.message}
            </Typography>
          )}
        </View>
        <View style={styles.buttonContainer}>
          <CButton
            title={t('createProfileLogin.completeProfile')}
            variant="dark"
            onPress={handleSubmit(onSubmit)}
            containerStyle={[styles.flex1, styles.btnStyle]}
            textStyle={styles.buttonText}
          />
          <CButton
            title={t('createProfileLogin.setupLater')}
            variant="dark"
            onPress={() => {
              setupLater();
            }}
            containerStyle={[styles.flex1, styles.btnStyle]}
            textStyle={styles.buttonText}
          />
        </View>
      </View>
      {/* </View> */}
    </View>
  );
};

export default Login;
