import {StyleSheet} from 'react-native';

const createStyles = (theme: any) =>
  StyleSheet.create({
    backgroundImage: {
      flex: 1,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    container: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    content: {
      flexGrow: 1,
      paddingHorizontal: 20,
    },
    inputStyle: {
      backgroundColor: 'transparent',
    },
    ratingContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      backgroundColor: theme.colors.TranslucentWhite,
      borderRadius: 10,
      padding: 6,
      marginBottom: 6,
    },
  });

export default createStyles;
