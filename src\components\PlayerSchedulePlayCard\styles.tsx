import {FONT_SIZE} from '@/utils/fonts';
import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.bgGrey,
      padding: 12,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: theme.colors.secondary,
      width: '100%',
    },
    imageContainer: {
      width: 61,
      height: 61,
      borderRadius: 100,
      borderWidth: 1,
      borderColor: theme.colors.secondary,
    },
    image: {
      height: '100%',
      width: '100%',
      borderRadius: 100,
    },
    playerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 16,
    },
    playerInfoContainer: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 16,
    },
    rating: {
      marginTop: 4,
    },
    playerContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    locationInfo: {
      marginTop: 8,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    plusButton: {
      width: 36,
      height: 36,
      borderRadius: 30,
      justifyContent: 'center',
      alignItems: 'center',
    },
    plusText: {
      color: theme.colors.text,
      fontSize: FONT_SIZE.xxl,
      fontWeight: 'bold',
    },
    schedulePlayButton: {
      marginTop: 20,
      width: '55%',
      alignSelf: 'center',
      borderRadius: 28,
      backgroundColor: theme.colors.activeColor,
    },
    schedulePlayButtonText: {
      color: theme.colors.background,
      fontSize: FONT_SIZE.xl,
      fontWeight: 'bold',
    },
  });
