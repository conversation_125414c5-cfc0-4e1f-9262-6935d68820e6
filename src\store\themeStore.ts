import {create} from 'zustand';
import {persist, createJSONStorage} from 'zustand/middleware';
import {mmkvStorage} from '@/services/storage';

// Theme types
export interface ThemeState {
  fontSize: {
    xsmall: number;
    small: number;
    medium: number;
    font14: number;
    large: number;
    default: 'small' | 'medium' | 'large' | 'xlarge';
    xlarge: number;
    xxlarge: number;
  };
  colors: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
    activeColor: string;
    white: string;
    orange: string;
    orange1: string;
    error: string;
    black: string;
    shadowColor: string;
    gray: string;
    midGrey: string;
    graniteGrey: string;
    silver: string;
    spanishGray: string;
    davyGrey: string;
    darkGray: string;
    divider: string;
    greyBackground: string;
    actionBackground: string;
    red: string;
    crimsonRed: string;
    inputLabel: string;
    highlightColor: string;
    placeholder: string;
    link: string;
    dotColor: string;
    mediumGray: string;
    coralRed: string;
    bottomSheetBackground: string;
    Gainsboro: string;
    oceanBlue: string;
    Gunmetal: string;
    charcoal: string;
    offWhite: string;
    darkRed: string;
    jetBlack: string;
    lime: string;
    transparentWhite: string;
    transparentWhite1: string;
    semiTransparentWhite: string;
    semiTransparentBlack: string;
    blackBg: string;
    white1: string;
    white2: string;
    bgGrey: string;
    offWhite1: string;
    veryLightGray: string;
    likeRed: string;
    richSkyBlue: string;
    pinkishPurple: string;
    tangerine: string;
    TranslucentWhite: string;
    notificationBg: string;
    dimGray: string;
    storageBg: string;
    lightGray: string;
    recommendBg: string;
    green: string;
  };
}

// Default theme values
const defaultTheme: ThemeState = {
  fontSize: {
    xsmall: 10,
    small: 12,
    font14: 14,
    medium: 16,
    large: 20,
    default: 'medium',
    xlarge: 22,
    xxlarge: 24,
  },
  colors: {
    primary: '#288ECE',
    secondary: '#707070',
    background: '#2B2B2B',
    text: '#FFFFFF',
    activeColor: '#DFFF4F',
    white: '#FFFFFF',
    orange: '#FFAB4C',
    orange1: '#FF5A00',
    error: '#FF6B6B',
    black: '#000000',
    shadowColor: '#0000004D',
    gray: '#818181',
    midGrey: '#888888',
    graniteGrey: '#4A4A4A',
    silver: '#767577',
    spanishGray: '#939393',
    davyGrey: '#343A40',
    darkGray: '#333333',
    divider: '#707070',
    greyBackground: '#666766',
    actionBackground: '#3B3A3A',
    red: '#FF3B30',
    crimsonRed: '#E61A3B',
    inputLabel: '#DFFF4F',
    highlightColor: '#CCFF00',
    placeholder: '#888',
    link: '#3498DB',
    dotColor: '#e0e0e0',
    mediumGray: '#9e9e9e',
    coralRed: '#FF6B6B',
    bottomSheetBackground: '#939393',
    Gainsboro: '#ddd',
    oceanBlue: '#0077C2',
    Gunmetal: '#333542',
    charcoal: '#121212',
    offWhite: '#ADADAD',
    darkRed: '#d32f2f',
    jetBlack: '#2A2A2A',
    lime: '#D6FF00',
    transparentWhite: '#FFFFFFB2',
    transparentWhite1: '#FFFFFFcc',
    semiTransparentWhite: '#FFFFFF33',
    semiTransparentBlack: '#000000B3',
    blackBg: '#111',
    white1: '#FFFFFF1A',
    white2: '#FFFFFF4D',
    bgGrey: '#4E4E4E',
    offWhite1: '#ADADAD',
    veryLightGray: '#E0E1E0',
    likeRed: '#FF003D',
    richSkyBlue: '#298FCE',
    pinkishPurple: '#DFFF4F99',
    tangerine: '#FFAB4C99',
    TranslucentWhite: '#FFFFFF0D',
    notificationBg: '#1A1A1A',
    dimGray: '#616161',
    storageBg: '#00000040',
    lightGray: '#979797',
    recommendBg: '#D9D9D933',
    green: '#09B780',
  },
};

// Define the store type
type ThemeStore = ThemeState & {
  setTheme: (theme: ThemeState | ((prev: ThemeState) => ThemeState)) => void;
};

// Create the theme store with persistence
export const useThemeStore = create<ThemeStore>()(
  persist(
    set => ({
      ...defaultTheme,
      setTheme: theme =>
        set(typeof theme === 'function' ? state => ({...state, ...theme(state)}) : {...theme}),
    }),
    {
      name: 'app-theme',
      storage: createJSONStorage(() => mmkvStorage),
    },
  ),
);
