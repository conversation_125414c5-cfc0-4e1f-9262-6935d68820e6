{"//": "Traductions françaises (Français) pour l'application", "common": {"ok": "OK", "cancel": "Annuler", "back": "Retour", "next": "Suivant", "save": "Enregistrer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "search": "<PERSON><PERSON><PERSON>", "SEARCH": "RECHERCHER", "community": "COMMUNAUTÉ", "kiosk": "KIOSQUE", "filter": "<PERSON><PERSON><PERSON>", "apply": "Appliquer", "reset": "Réinitialiser", "close": "<PERSON><PERSON><PERSON>", "upload": "Télécharger", "invite": "Inviter", "uploadContacts": "Télécharger les contacts", "enterName": "Entrez un nom", "enterEmail": "Entrez une adresse e-mail", "enterPhone": "Entrez un numéro de téléphone", "enterAtpRating": "Entrez le classement ATP (Optionnel)", "on": "ACTIVÉ", "off": "DÉSACTIVÉ", "create": "<PERSON><PERSON><PERSON>", "public": "Public", "private": "Priv<PERSON>", "hidden": "<PERSON><PERSON><PERSON><PERSON>", "skip": "<PERSON><PERSON><PERSON>", "age": "Âge", "leaveComment": "Laisser un commentaire", "likeVideo": "J'aime ce vidéo", "shareVideo": "Partager ce vidéo", "reportAbuse": "Signaler un abus", "inviteMore": "Inviter davantage !", "inviteSent": "L'invitation a été envoyée !", "results": "Résultats", "mySelf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myChildren": "<PERSON><PERSON> en<PERSON>", "someoneElse": "Quelqu'un d'autre", "group": "Groupe", "searchResults": "Résultats de recherche"}, "auth": {"login": "Connexion", "signup": "S'inscrire", "email": "E-mail", "password": "Mot de passe", "forgotPassword": "Mot de passe oublié ?", "loginWithEmail": "Se connecter avec l'e-mail", "loginWithGoogle": "Se connecter avec Google", "loginWithFacebook": "Se connecter avec Facebook", "dontHaveAccount": "Vous n'avez pas de compte ?", "alreadyHaveAccount": "Vous avez déjà un compte ?", "createAccount": "<PERSON><PERSON><PERSON> un compte", "logout": "Déconnexion", "verification": "Vérification"}, "profile": {"profile": "Profil", "settings": "Paramètres", "personalInfo": "Informations personnelles", "name": "Nom", "phoneNumber": "Numéro de téléphone", "bio": "Biographie", "updateProfile": "Mettre à jour le profil", "createProfile": "<PERSON><PERSON>er un profil", "whichBestDescribesYou": "Qu'est-ce qui vous décrit le mieux ?"}, "settings": {"settings": "Paramètres", "language": "<PERSON><PERSON>", "notifications": "Notifications", "darkMode": "Mode sombre", "biometrics": "Biométrie", "termsOfService": "Conditions d'utilisation", "privacyPolicy": "Politique de confidentialité", "shareLocationWithFriends": "Partager la localisation avec les amis", "twoFactorAuthentication": "Authentification à deux facteurs", "about": "À propos", "version": "Version", "searchSettings": "Paramètres de recherche"}, "booking": {"searchParks": "Rechercher des parcs", "selectDateTime": "Sélectionner la date et l'heure", "purchaseEquipments": "Acheter du matériel", "cartView": "<PERSON><PERSON>", "createProfile": "<PERSON><PERSON>er un profil", "signup": "S'inscrire", "advertisement": "Publicité", "subscription": "Abonnement"}, "parks": {"findParks": "Trouver des parcs", "nearbyParks": "Parcs à proximité", "location": "Localisation", "distance": "Distance", "facilities": "Installations", "courts": "Courts", "availableNow": "Disponible maintenant"}, "equipment": {"equipment": "Équipement", "racquets": "RAQUETTES", "balls": "BALLES", "accessories": "Accessoires", "brand": "Marque", "price": "Prix", "addToCart": "A<PERSON>ter au panier", "removeFromCart": "<PERSON><PERSON><PERSON> du panier"}, "permissions": {"locationPermissions": "Autorisations de localisation", "locationDescription": "Cette application nécessite les services de localisation", "enableLocation": "Activer la localisation", "enableBluetooth": "<PERSON><PERSON> <PERSON>", "enableNotifications": "Activer les notifications", "noThanks": "Non merci", "bluetoothPermissions": "Autorisations Bluetooth", "bluetoothDescription": "Fonctionne mieux lorsque le Bluetooth est activé", "notificationPermissions": "Autorisations de notification", "notificationDescription": "Fonctionne mieux lorsque les notifications sont activées"}, "terms": {"title": "Conditions générales", "accept": "Accepter"}, "signupScreen": {"signUpWith": "S'inscrire avec", "facebook": "Facebook", "google": "Google", "email": "E-mail", "alreadyHaveAnAccount": "Vous avez déjà un compte ?", "login": "Connexion", "bySigningUpYouAgreeToOur": "En vous inscrivant, vous acceptez nos", "termsAndConditions": "Conditions générales", "signin": "Se connecter"}, "login": {"title": "Se connecter", "enterYourEmail": "Entrez votre e-mail", "emailAddress": "Adresse e-mail", "enterYourPassword": "Entrez votre mot de passe", "password": "Mot de passe", "forgotYourPassword": "Mot de passe oublié ?", "signUp": "S'inscrire", "submit": "Suivant", "dontHaveAccount": "Vous n'avez pas de compte ?"}, "createProfile": {"createProfileTitle": "<PERSON><PERSON><PERSON> votre profil Go<PERSON>t", "name": "Nom d'affichage", "namePlaceholder": "Entrez le nom d'affichage", "nameError": "Le nom est requis", "email": "E-mail", "emailPlaceholder": "Entrez une adresse e-mail", "emailError": "L'e-mail est requis", "emailFormatError": "Le format de l'e-mail est invalide", "randomNameGenerator": "Noms d'affichage générés", "age": "Âge", "agePlaceholder": "Entrez l'âge", "ageError": "L'âge est requis", "ageNumberError": "L'âge doit être un nombre", "ageLengthError": "L'âge doit être entre 13 et 120", "ageFormatError": "Le format de l'âge est invalide", "userTypeError": "Veuillez sélectionner une option", "fitnessLevelError": "Veuillez sélectionner un niveau de forme physique", "describe": "Décrivez-vous", "describeFitness": "Qu'est-ce qui décrit le mieux votre niveau de forme/capacité ?", "submitBtn": "<PERSON><PERSON><PERSON> le profil", "setupLaterBtn": "Configurer plus tard", "birthYear": "<PERSON><PERSON> de naissance", "birthYearPlaceholder": "Entrez l'année de naissance", "birthYearformatErr": "Doit être une année valide (AAAA)", "birthYearRangeErr": "L'année de naissance ne peut pas être supérieure à l'année actuelle et ne doit pas être supérieure à 60 ans", "birthYearRequired": "L'année de naissance est requise", "bestDescribeList": {"player": "<PERSON><PERSON><PERSON>", "coach": "Entraîneur", "both": "<PERSON><PERSON>ur et entraîneur"}, "describeFitnessList": {"slow_and_steady": "Lent et régulier", "workout_warrior": "Guerrier de l'entraînement", "can_keep_a_rally": "Peut maintenir une récupération", "play_competitively": "<PERSON>ue de manière compétitive"}}, "advertisement": {"title": "Essayez 30 jours de raquettes Wilson pour 25 €", "subTitle": "Vous obtiendrez 1 heure de jeu par jour pendant 30 jours, 50% de réduction sur un tube de balles Wilson et un crédit de 20 € pour l'achat d'une raquette", "skipBtn": "<PERSON><PERSON><PERSON>", "claimOfferBtn": "Ré<PERSON>lamer l'offre"}, "subscription": {"trial": "Essai de 30 jours", "price": "20,44 €", "promoMessage": "Après votre période d'essai gratuite, obtenez 10% de réduction sur l'adhésion avec une carte de crédit ou de débit American Express éligible.", "bulletTitle": "Point de vente", "bulletSubtitle": "adipiscing elit, sed do eiusmod tempor", "promoCodePlaceholder": "ENTREZ LE CODE PROMO", "termsText": "En achetant Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud", "termsLink": "Sous réserve des conditions générales", "acceptBtn": "Accepter"}, "createProfileLogin": {"title": "Bonjour", "subTitle": "Nous avons juste besoin de quelques détails supplémentaires pour vous configurer.", "age": "Quel âge avez-vous ?", "agePlaceholder": "Entrez votre âge", "ageRequired": "L'âge est requis", "ageNumber": "L'âge doit être un nombre", "ageRange": "L'âge doit être entre 13 et 120", "userTypeRequired": "Veuillez sélectionner une option", "fitnessLevelRequired": "Veuillez sélectionner un niveau de forme physique", "userType": "Qu'est-ce qui vous décrit le mieux ?", "fitnessLevel": "Qu'est-ce qui décrit le mieux votre niveau de forme/capacité ?", "completeProfile": "<PERSON><PERSON><PERSON> le profil", "setupLater": "Configurer plus tard", "birthYear": "<PERSON><PERSON> de naissance", "birthYearPlaceholder": "Entrez l'année de naissance", "birthYearformatErr": "Doit être une année valide (AAAA)"}, "ParksSearchScreen": {"niceToSeeYou": "Content de vous voir", "guest": "Invi<PERSON>", "whereDoYouWantToPlay": "Où voulez-vous jouer ?", "noParksFound": "Aucun parc trouvé", "loadingMoreParks": "Chargement de plus de parcs...", "noKiosk": "Il n'y a pas de kiosque intelligent dans cette zone.", "recommendLocation": "Recommander un emplacement", "address": "<PERSON><PERSON><PERSON>", "city": "Ville", "state": "État/Région", "submit": "So<PERSON><PERSON><PERSON>", "zipCode": "Code postal", "nameRequired": "le nom est requis", "nameInvalid": "Le nom ne peut contenir que des lettres", "addressRequired": "l'adresse est requise", "cityRequired": "la ville est requise", "cityInvalid": "La ville ne peut contenir que des lettres", "stateRequired": "l'état/région est requis", "stateInvalid": "L'état/région ne peut contenir que des lettres", "zipCodeRequired": "le code postal est requis", "invalidZipCode": "Code postal invalide", "thankYou": "<PERSON><PERSON><PERSON>"}, "dateScreen": {"selectDateAndTime": "Sélectionnez une date et une heure"}, "datePicker": {"selectDateAndTime": "Sélectionnez une date et une heure"}, "customDatePicker": {"addPlayers": "Ajouter des joueurs", "otherPlayers": "Autres joueurs"}, "invitePlayers": {"invitePlayers": "Inviter des joueurs", "accept": "Accepter", "friends": "<PERSON><PERSON>", "groups": "Groupes", "nearby": "À proximité", "invite,": "Inviter", "searchNameLocationGroup": "Rechercher nom, localisation, groupe, etc.", "filterResults": "Filtrer les résultats", "reserveGear": "Réserver du matériel", "inviteSent": "L'invitation a été envoyée !", "close": "<PERSON><PERSON><PERSON>", "invitePlayerTabs": {"friends": "<PERSON><PERSON>", "groups": "Groupes", "nearby": "À proximité", "invite": "Inviter"}, "name": "Entrez un nom", "email": "Entrez une adresse e-mail", "phoneNumber": "Entrez un numéro de téléphone", "rating": "Entrez une note (optionnel)", "submit": "Inviter", "nameRequired": "le nom est requis", "emailRequired": "l'e-mail est requis", "phoneNumberRequired": "le numéro de téléphone est requis", "emailInvalid": "le format de l'e-mail est invalide", "searchEvent": "Rechercher un événement", "results": "Résultats", "show": "<PERSON><PERSON><PERSON><PERSON>"}, "ShoppingCartSection": {"yourCart": "<PERSON><PERSON><PERSON> panier", "total": "Total", "proceedToCheckout": "Procéder au paiement", "emptyCart": "Votre panier est vide", "findAPark": "Trouver un parc"}, "EquipmentReservation": {"filterResults": "Filtrer les résultats", "availableNowInKiosk": "Disponible maintenant dans le kiosque", "age": "Âge", "apply": "Appliquer", "previous": "Précédent", "next": "Suivant", "reserveOrPurchaseEquipment": "Rés<PERSON>r ou acheter du matériel"}, "EquipmentDetailModal": {"tryItNow": "ESSAYEZ MAINTENANT !", "specifications": "Spécifications", "averageRating": "<PERSON> moyenne", "playerReviews": "<PERSON><PERSON> des joueurs", "more": "Plus", "less": "<PERSON>ins", "staffReviews": "<PERSON><PERSON> du <PERSON>"}, "RacquetsScreen": {"topBrands": "Marques principales", "allBrands": "Toutes les marques", "sports": "Sports", "services": "Services"}, "RacquetCategoryScreen": {"byBrand": "Par marque", "bestForMyGame": "Meilleur pour mon jeu"}, "RacquetsBrandsScreen": {"brands": "Marques"}, "RacquetSelector": {"racquetSelector": "<PERSON><PERSON><PERSON><PERSON>", "howLongHaveYouBeenPlayingTennis": "Depuis combien de temps\njouez-vous au tennis ?", "lessThan2Years": "0-2 ans", "moreThan2Years": "2+ ans", "whichBestDescribesYourFitnessLevel": "Qu'est-ce qui décrit le mieux\nvotre niveau de forme physique ?", "slowAndSteadyOnTheTreadmill": "Lent et régulier\nsur le tapis de course", "workoutWarrior": "Guerrier de\nl'entraînement", "whichCharacteristicsDoYouMostWantInARacquet": "Quelles caractéristiques voulez-vous\nle plus dans une raquette ?", "comfortableAndArmFriendly": "Confortable\net respectueuse du bras", "lotsOfPower": "Beaucoup\nde puissance"}, "RacquetSelectorDetail": {"RacquetSelectorDetail": "<PERSON><PERSON><PERSON><PERSON>", "tryItNow": "ESSAYEZ MAINTENANT !", "results": "Résultats"}, "CartScreen": {"Subscriber": "<PERSON><PERSON><PERSON><PERSON>", "cart": "<PERSON><PERSON>", "description": "Après votre période d'essai gratuite, obtenez 10% de réduction sur l'adhésion avec une carte de crédit ou de débit American Express éligible.", "total": "Total", "continueShopping": "Continuer les achats", "reserve": "Réserver", "youAreAllSetToPlayNow": "Vous êtes prêt à jouer maintenant ! Marchez vers le kiosque le plus proche et scannez le code QR pour commencer à jouer"}, "BottomSheetComp": {"niceToSeeYou": "Content de vous voir", "Guest": "Invi<PERSON>"}, "drawer": {"notifications": "Notifications", "referFriend": "Parrainer un ami", "myMatches": "Mes matchs", "rewards": "Récompenses", "recycleBalls": "Recycler les balles", "help": "Aide", "settings": "Paramètres", "manageCommunity": "<PERSON><PERSON><PERSON> la communauté", "orders": "Commandes", "assistantCoach": "Assistant entraîneur"}, "assistantCoach": {"welcome": "Bienvenue dans Assistant entraîneur", "subtitle": "Votre source unique pour la gestion gratuite des sports de raquette"}, "InvitePlayersAssistantCoach": {"title": "Invitez vos joueurs", "playerName": "Nom du joueur", "playerPhone": "Numéro de téléphone du joueur", "playerEmail": "Adresse e-mail du joueur", "name": "Entrez le nom", "phone": "Entrez le numéro de téléphone", "email": "Entrez l'adresse e-mail"}, "assistantCoachOptions": {"editProfile": "Modifier le profil d'entraîneur", "calendar": "<PERSON><PERSON><PERSON>", "manageClasses": "<PERSON><PERSON><PERSON> les cours", "managePlayerAssets": "<PERSON><PERSON><PERSON> les ressources des joueurs", "manageServiceRequest": "G<PERSON>rer les demandes de service", "manageContent": "<PERSON><PERSON><PERSON> le contenu", "postAnAd": "Publier une annonce", "orderReserveEquipment": "Commander/Réserver du matériel", "getCertified": "Obtenir une certification", "messageNotificationPreferences": "Préférences de messages et notifications", "invitePlayers": "Invitez vos joueurs", "title": "Assistant entraîneur"}, "drawerNotification": {"notifications": "Notifications", "searchNotifications": "Rechercher des notifications", "notificationsWidget": "Widget de notifications", "chatWidget": "Widget de chat", "inviteFriendsGet10Off": "Invitez des amis, obtenez 10% de réduction"}, "drawerReferFriend": {"referFriend": "Parrainer un ami", "searchReferrals": "Rechercher des parrainages", "inviteFriendsGet10Off": "Invitez des amis, obtenez 10% de réduction", "uploadContacts": "Télécharger les contacts"}, "drawerMyMatches": {"myMatches": "Mes matchs", "searchMatches": "Rechercher des matchs", "calendar": "<PERSON><PERSON><PERSON>", "matchHistory": "Historique des matchs", "notificationsWidget": "Widget de notifications", "chatWidget": "Widget de chat", "noMatchesFound": "Aucun match trouvé"}, "balls": {"purchase": "Acheter des balles", "search": "Rechercher des balles", "help": "Aidez-moi à choisir une balle de tennis", "filter": "Filtrer les résultats", "availableNowInKiosk": "Disponible maintenant dans le kiosque", "age": "Âge"}, "shareScreen": {"needHelp": "Besoin d'aide ?", "pickup": "Retrait / Retour", "direct": "Connexion directe"}, "CommunityScreen": {"home": "Accueil", "playerConnect": "Connexion joueur", "reviews": "<PERSON><PERSON>", "groups": "Groupes", "goLife": "GoLife", "upYourGame": "<PERSON><PERSON><PERSON><PERSON> votre jeu", "goStream": "GoStream"}, "editCoachProfile": {"title": "Modifier le profil d'entraîneur", "addPhoto": "AJOUTER UNE PHOTO", "description": "Description du profil", "descriptionPlaceholder": "Entrez la description", "descriptionRequired": "La description est requise", "certifications": "Certifications", "private": "Priv<PERSON>", "locationsAvailable": "Emplacements disponibles", "publicCourts": "Courts publics", "sportsClub": "Clubs sportifs", "videoCoaching": "Coaching vid<PERSON><PERSON>", "rate": "<PERSON><PERSON><PERSON>", "otherServices": "Autres services", "stringing": "Cordage", "gripping": "<PERSON><PERSON>", "customization": "Personnalisation", "CoachingSkillsSets": "Compétences d'entraînement", "doubles": "Double", "footwork": "<PERSON><PERSON> de jambes et conditionnement", "funGames": "Jeux amusants", "groupLessons": "Cours de groupe", "mentalSkills": "Compétences mentales", "privateLessons": "Cours privés", "redOrange": "Progression balle rouge, orange et verte", "singles": "Simple", "technicalFundamentals": "Fondamentaux techniques", "typesOfPlayers": "Types de joueurs", "adult": "Adulte", "learning": "Apprendre à jouer", "middleSchool": "Collège", "preSchool": "Préscolaire et maternelle", "socialPlayer": "Joueur social", "youth": "Je<PERSON>sse", "save": "Enregistrer"}, "calendarScreen": {"title": "<PERSON><PERSON><PERSON>", "newClass": "Nouveau cours", "manageClasses": "<PERSON><PERSON><PERSON> les cours", "upcomingClasses": "Cours à venir", "today": "<PERSON><PERSON><PERSON>'hui", "upcoming": "À venir", "widget": "Widget"}, "manageClassesScreen": {"title": "<PERSON><PERSON><PERSON> les cours", "unscheduled": "Non programmé", "className": "Nom du cours", "classNamePlaceholder": "Entrez le nom", "classDescription": "Description du cours", "classDescriptionPlaceholder": "Entrez la description", "classDescriptionRequired": "La description est requise", "selectDate": "Sélectionnez la date, l'heure et la fréquence", "maximumStudents": "Nombre maximum d'étudiants", "numberPlaceholder": "Entrez le nombre", "private": "Priv<PERSON>", "scheduledClasses": "Cours programmés"}, "managePlayerAssetsScreen": {"title": "<PERSON><PERSON><PERSON> les ressources des joueurs", "students": "Étudiants"}, "manageServiceRequestScreen": {"title": "G<PERSON>rer les demandes de service", "stringing": "Cordage", "grips": "Grips", "customs": "Personnalisations"}, "manageContentScreen": {"title": "<PERSON><PERSON><PERSON> le contenu", "storage": "Stockage", "upgrade": "Mettre à niveau", "stats": "Statistiques", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "videos": "Vid<PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er"}, "postAnAdScreen": {"title": "Publier une annonce", "classListing": "Liste de cours", "saveDraft": "Enregistrer le brouillon", "className": "Nom du cours", "classNamePlaceholder": "Entrez le nom", "classDescription": "Description du cours", "classDescriptionPlaceholder": "Entrez la description", "classDescriptionRequired": "La description est requise", "selectDate": "Sélectionnez la date, l'heure et la fréquence", "maximumStudents": "Nombre maximum d'étudiants", "numberPlaceholder": "Entrez le nombre", "preview": "<PERSON><PERSON><PERSON><PERSON>", "post": "Publier", "classRequired": "Le nom du cours est requis"}, "orderReserveEquipmentScreen": {"title": "Commander/Réserver du matériel", "subTitle": "Rés<PERSON>r ou acheter du matériel"}, "getCertifiedScreen": {"title": "Obtenir une certification", "recCoachWorkshop": "Atelier d'entraîneur ré<PERSON>f", "level1": "Niveau 1", "level2": "Niveau 2", "level3": "Niveau 3", "specialtyWorkshops": "Ateliers spécialisés", "ptrw": "PTRW"}, "messageNotificationPreferencesScreen": {"title": "Préférences de messages et notifications", "pushNotifications": "Notifications push"}, "communityTabs": {"home": "Accueil", "playerConnect": "Connexion joueur", "reviews": "<PERSON><PERSON>", "groups": "Groupes", "goLife": "GoLife", "upYourGame": "<PERSON><PERSON><PERSON><PERSON> votre jeu", "goStream": "GoStream"}, "findPlayer": {"searchPlaceHolder": "Rechercher nom, localisation, groupe, etc.", "accept": "Accepter", "findPlayerTabs": {"schedulePlay": "Programmer un jeu", "nearby": "À proximité", "friends": "<PERSON><PERSON>", "group": "Groupe", "invite": "Inviter"}, "title": "Trouver des joueurs", "invitePlayers": "Inviter des joueurs", "namePlaceholder": "Entrez un nom", "emailPlaceholder": "Entrez une adresse e-mail", "phonePlaceholder": "Entrez un numéro de téléphone", "ratingPlaceholder": "Entrez une note (facultatif)", "nameRequired": "Le nom est requis", "emailInvalid": "Le format de l'e-mail est invalide", "phoneRequired": "Le numéro de téléphone est requis", "name": "Nom", "email": "E-mail", "phoneNumber": "Numéro de téléphone", "rating": "Note", "searchEvent": "Rechercher un événement", "Results": "Résultats"}, "groupsScreen": {"title": "Groupes", "createGroup": "Créer un groupe", "myGroups": "Mes groupes", "joinGroup": "Rejoindre un groupe", "scoreBoard": "Tableau de bord", "allGroups": "Tous les groupes", "favoriteGroups": "Groupes favoris"}, "newGroupScreen": {"title": "Créer un groupe", "newGroup": "Nouveau groupe", "groupNamePlaceholder": "Entrez le nom du groupe", "groupSettings": "Paramètres du groupe", "privacy": "Confidentialité", "favoriteLocation": "<PERSON><PERSON> de jeu favori", "favoriteLocationPlaceholder": "<PERSON><PERSON>z le favori", "tags": "Étiquettes"}, "addMembersScreen": {"title": "Ajouter des membres", "search": "<PERSON><PERSON><PERSON>", "addMembers": "Ajouter des membres", "searchPlaceholder": "Rechercher nom ou e-mail", "results": "Résultats", "filterResults": "Filtrer les résultats", "show": "<PERSON><PERSON><PERSON><PERSON>", "tennis": "Tennis", "pickleball": "Pickleball", "padel": "Pa<PERSON>", "platformTennis": "Tennis de plateforme", "utrRating": "Classement UTR", "friends": "<PERSON><PERSON>", "invited": "Invi<PERSON>", "sponsored": "Sponso<PERSON><PERSON>", "searchEvent": "Rechercher un événement"}, "createGroupMemberListScreen": {"title": "Créer un groupe", "newGroup": "Nouveau groupe", "groupNamePlaceholder": "Entrez le nom du groupe", "groupSettings": "Paramètres du groupe", "members": "Me<PERSON><PERSON>", "of": "de"}, "myGroupsScreen": {"title": "Mes groupes"}, "joinGroupsScreen": {"title": "Rejoindre un groupe", "searchPlaceholder": "Rechercher nom ou e-mail"}, "joinGroupDetailsScreen": {"title": "Rejoindre un groupe", "joinGroup": "Rejoindre un groupe"}, "reviewsScreen": {"title": "Derniers Avis", "refineResults": "Affiner les Résultats", "searchPlaceholder": "Rechercher des Avis", "results": "Résultats", "noReviewsFound": "<PERSON><PERSON><PERSON>", "noReviewsFoundMessage": "Il n'y a aucun avis disponible pour le moment"}, "commentScreen": {"title": "Écrire un commentaire", "rating": "Évaluation", "headline": "Titre", "description": "Description", "addImage": "Ajouter une Image", "ratingRequired": "L'évaluation est obligatoire", "selectRating": "Veuillez sélectionner une évaluation", "headlineRequired": "Le titre est obligatoire", "descriptionRequired": "La description est obligatoire", "maxCharacters": "Max. 1500 caractères", "uploadImagesAndVideos": "Télécharger des images et vidéos", "writeHeadline": "Écrire un Titre", "describeExperience": "Décrivez votre expérience avec cet équipement. Incluez ce qui était bien et pourquoi, les domaines d'amélioration, le cas échéant. Limite de 1500 caractères"}, "BiometricsScreen": {"title": "Authentification biométrique", "notAvailable": "L'authentification biométrique n'est pas disponible sur votre appareil.", "description": "Utilisez {getBiometryTypeText()} pour sécuriser votre application et éviter de saisir votre mot de passe à chaque ouverture de l'application.", "biometrics": "Biométrie", "faceId": "Face ID", "touchId": "Touch ID", "enable": "Activer", "whenEnabled": "Lorsque activé,", "required": " sera requis à chaque ouverture de l'application.", "protection": "Protection", "use": "Utiliser", "toSecure": "pour sécuriser votre application et éviter de saisir votre mot de passe à chaque ouverture de l'application.", "biometricAuthenticationEnabled": "L'authentification biométrique a été activée", "failedToEnableBiometricAuthentication": "Échec de l'activation de l'authentification biométrique", "biometricAuthenticationDisabled": "L'authentification biométrique a été désactivée", "failedToDisableBiometricAuthentication": "Échec de la désactivation de l'authentification biométrique", "errorOccurredWhileUpdatingBiometricSettings": "Une erreur s'est produite lors de la mise à jour des paramètres biométriques"}, "NotificationScreen": {"title": "Notifications", "notifications": "Notifications", "searchNotifications": "Rechercher des notifications", "notificationsWidget": "Widget de notifications", "chatWidget": "Widget de chat", "trash": "<PERSON><PERSON><PERSON><PERSON>", "more": "Plus", "notificationFunction": "Fonction de notification", "reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remindMe": "<PERSON><PERSON><PERSON><PERSON>moi"}, "equipmentReservationScreen": {"title": "Rés<PERSON>r ou acheter du matériel", "age": "Âge", "availableNowInKiosk": "Disponible maintenant dans le kiosque"}, "qrGenerator": {"description": "Placez le code QR sous le scanner du kiosque"}, "upYourGameScreen": {"findCoach": "Trouver un coach", "coachDescription": "Texte court car l'espace est limité", "findClass": "Trouver un cours", "shareCoach": "Partager avec un coach", "getUTC": "Obtenez votre classement UTC ou UTC-P"}, "findCoachScreen": {"title": "Trouver un coach", "lessonsFor": "Pour qui sont ces cours ?", "selectAll": "Sélectionnez toutes les options applicables", "error": "Veuillez sélectionner au moins une option"}, "filterResultsScreen": {"title": "Filtrer les résultats", "days": "<PERSON><PERSON> de la semaine", "weekdays": "<PERSON><PERSON> de se<PERSON>", "weekends": "Week-ends", "dateTime": "Date/Heure", "tennis": "Tennis", "pickleball": "Pickleball", "padel": "Pa<PERSON>", "platformTennis": "Tennis de plateforme", "all": "Tous", "certified": "Certifié", "applyFilters": "Appliquer les filtres"}, "coachProfileScreen": {"title": "<PERSON><PERSON> du coach", "book": "Réserver", "askQuestion": "Poser une question au coach", "sendVideo": "Envoyer vidéo/statistiques"}, "findClassScreen": {"title": "Trouver un cours"}, "qrProfileScreen": {"quickConnect": "Connexion rapide", "scanQrCode": "Scanner le code QR", "needHelp": "Besoin d'aide ?"}, "editProfileScreen": {"title": "Profil", "matches": "Matchs", "following": "Abonnements", "followers": "Abonnés", "displayName": "Nom d'affichage", "randomName": "Générateur de noms aléatoires", "birthYear": "<PERSON><PERSON> de naissance", "profession": "Profession", "primarySport": "Sport principal préf<PERSON><PERSON>", "location": "Localisation", "myCourt": "Mon terrain", "yearsPlaying": "Années de pratique", "skillLevel": "Niveau de compétence", "favoriteBrand": "<PERSON><PERSON>", "memberships": "Adhésions", "affiliations": "Affiliations", "displayNamePlaceholder": "Entrez votre nom d'affichage", "birthYearPlaceholder": "<PERSON><PERSON> de naissance", "professionPlaceholder": "Entrez votre profession", "primarySportPlaceholder": "Entrez votre sport principal préf<PERSON><PERSON>", "locationPlaceholder": "Entrez votre localisation", "myCourtPlaceholder": "Entrez le nom ou la localisation de votre terrain", "yearsPlayingPlaceholder": "Entrez le nombre d'années de pratique", "skillLevelPlaceholder": "Entrez votre niveau de compétence", "favoriteBrandPlaceholder": "Entrez votre marque préférée (facultatif)", "membershipsPlaceholder": "Entrez vos adhésions (facultatif)", "affiliationsPlaceholder": "Entrez vos affiliations (facultatif)", "completeProfile": "Compléter le profil", "fullNameRequired": "Le nom d'affichage est requis", "emailRequired": "L'email est requis", "invalidEmail": "Le format de l'email est invalide", "birthYearRequired": "L'année de naissance est requise", "invalidBirthYear": "L'année de naissance ne peut pas être supérieure à l'année actuelle et ne doit pas remonter à plus de 60 ans", "invalidProfession": "La profession ne peut contenir que des lettres", "primarySportRequired": "Le sport principal préféré est requis", "invalidPrimarySport": "Le sport principal ne peut contenir que des lettres", "locationRequired": "La localisation est requise", "invalidLocation": "La localisation ne peut contenir que des lettres", "myCourtRequired": "Mon terrain est requis", "invalidMyCourt": "Mon terrain ne peut contenir que des lettres", "yearsPlayingRequired": "Les années de pratique sont requises", "invalidYearsPlaying": "Doit être un nombre valide", "negativeYearsPlaying": "Les années de pratique doivent être positives", "skillLevelRequired": "Le niveau de compétence est requis", "selectYear": "Sélectionner l'année", "adult": "ADULTE", "junior": "JUNIOR", "addPhoto": "AJOUTER UNE PHOTO"}, "rewardsScreen": {"title": "Récompenses"}, "recycleBalls": {"title": "RecycleBalls"}, "helpScreen": {"title": "Aide", "searchHelp": "Re<PERSON><PERSON> de l'aide"}, "manageCommunityScreen": {"title": "<PERSON><PERSON><PERSON> la communauté", "searchCommunity": "Rechercher une communauté"}, "ordersScreen": {"title": "Commandes", "searchOrders": "Rechercher des commandes"}, "emailSignupScreen": {"signUp": "S'inscrire", "fullName": "Nom complet", "fullNamePlaceholder": "Entrez votre nom complet", "email": "E-mail", "emailPlaceholder": "Entrez votre e-mail", "password": "Mot de passe", "passwordPlaceholder": "<PERSON><PERSON><PERSON> un mot de passe", "confirmPassword": "Confirmer le mot de passe", "confirmPasswordPlaceholder": "Confirmer votre mot de passe", "submit": "Suivant", "signIn": "Se connecter"}, "forgotPasswordScreen": {"title": "Réinitialiser le mot de passe", "instructions": "Entrez votre adresse e-mail ci-dessous, et nous vous enverrons des instructions pour réinitialiser votre mot de passe.", "email": "E-mail", "emailPlaceholder": "Entrez votre e-mail", "submit": "Réinitialiser le mot de passe", "newPassword": "Nouveau mot de passe", "newPasswordPlaceholder": "Entrez votre nouveau mot de passe", "confirmPassword": "Confirmer le mot de passe", "confirmPasswordPlaceholder": "Confirmer votre mot de passe", "code": "Entrez le code 6 chiffres", "resendCode": "Renvoyer le code", "resendCodeIn": "Renvoyer le code dans", "sending": "Envoi...", "resetPassword": "Réinitialiser le mot de passe", "emailFormatInvalid": "Le format de l'e-mail est invalide", "emailRequired": "L'e-mail est requis", "passwordRequired": "Le mot de passe est requis", "passwordValidation": "Le mot de passe doit contenir au moins 8 caractères, y compris des majuscules, des minuscules, un chiffre et un symbole", "passwordsMustMatch": "Les mots de passe doivent correspondre", "pleaseConfirmYourPassword": "Veuillez confirmer votre mot de passe", "codeMustBe6Digits": "Le code doit contenir 6 chiffres", "codeRequired": "Le code est requis"}, "verificationScreen": {"title": "Vérification", "enterCode": "Entrez le code 6 chiffres", "resendCode": "Renvoyer le code", "resendCodeIn": "Renvoyer le code dans", "verify": "Vérifier"}, "sportsList": {"tennis": "Tennis", "pickleball": "Pickleball", "platformTennis": "Tennis de plateforme", "padel": "Pa<PERSON>"}, "groupsList": {"friends": "<PERSON><PERSON>", "invited": "Invi<PERSON>", "sponsored": "Sponso<PERSON><PERSON>"}}