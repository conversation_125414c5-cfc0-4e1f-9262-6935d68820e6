import React, {useState, useMemo} from 'react';
import {FlatList, View, TouchableOpacity, ActivityIndicator} from 'react-native';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import {CInput, Header, SafeAreaView} from '@/components';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import Typography from '@/components/Typography';
import GroupCard from '@/components/GroupCard';
import useTranslation from '@/hooks/useTranslation';

type NavigationProp = NativeStackNavigationProp<CommunityStackParamList>;

interface GroupsData {
  id: number;
  name: string;
  members: number;
  location: string;
  highlighted: boolean;
  type: string;
  locked: boolean;
}

interface PaginationState {
  page: number;
  loadMore: boolean;
}

const JoinGroups = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  const navigation = useNavigation<NavigationProp>();

  const [paginationLoader, setPaginationLoader] = useState<boolean>(false);
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    loadMore: false,
  });

  const [search, setSearch] = useState<string>('');

  const {t} = useTranslation();

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const GroupsData: GroupsData[] = [
    {
      id: 1,
      name: 'DUMBO Tennis',
      members: 9,
      location: 'DUMBO',
      highlighted: false,
      type: 'group',
      locked: false,
    },
    {
      id: 2,
      name: 'Ft Greene',
      members: 11,
      location: 'Fort Greene',
      highlighted: true,
      type: 'group',
      locked: false,
    },
    {
      id: 3,
      name: 'Babolat',
      members: 19,
      location: 'McCarren Park',
      highlighted: true,
      type: 'group',
      locked: false,
    },
    {
      id: 4,
      name: 'Fort Lauderdale',
      members: 5,
      location: 'Fort Greene',
      highlighted: false,
      type: 'group',
      locked: false,
    },
    {
      id: 5,
      name: 'Pickle Masters',
      members: 7,
      location: 'Brooklyn Heights',
      highlighted: false,
      type: 'group',
      locked: false,
    },
    {
      id: 6,
      name: 'Padel Hous',
      members: 13,
      location: 'Fort Greene',
      highlighted: false,
      type: 'group',
      locked: false,
    },
  ];

  const filteredGroups = useMemo(() => {
    if (!search.trim()) {
      return GroupsData;
    }
    const searchLower = search.toLowerCase();
    return GroupsData.filter(
      group =>
        group.name.toLowerCase().includes(searchLower) ||
        group.location.toLowerCase().includes(searchLower),
    );
  }, [search]);

  const renderComponent = ({item}: {item: GroupsData}) => {
    return (
      <GroupCard
        name={item.name}
        members={item.members}
        highlighted={item.highlighted}
        locked={item.locked}
        location={item.location}
        showJoinGroup={true}
        showMore={true}
        containerStyle={{
          borderWidth: 1,
          borderColor: theme.colors.divider,
          marginVertical: 8,
        }}
        onMorePress={() => navigation.navigate('JoinGroupDetails')}
        onPress={() => navigation.navigate('JoinGroupDetails')}
      />
    );
  };

  function loadMoreData(): void {
    if (pagination?.loadMore && !paginationLoader) {
      setPaginationLoader(true);
      // add api call here
    }
  }

  return (
    <SafeAreaView includeBottom={false} style={styles.root}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
        }}
        rightIcons={[
          {name: 'notification', size: 24, badge: 0},
          {name: 'chat', size: 24, badge: 14},
        ]}
        pageTitle={t('joinGroupsScreen.title')}
        backgroundColor="transparent"
      />
      <View style={styles.headerRow}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Typography variant="parkTitle" color={theme.colors.activeColor}>
            {t('common.cancel')}
          </Typography>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Typography variant="parkTitle" color={theme.colors.secondary}>
            {t('common.next')}
          </Typography>
        </TouchableOpacity>
      </View>
      <View style={styles.searchContainer}>
        <CInput
          inputStyle={styles.searchInput}
          placeholder={t('joinGroupsScreen.searchPlaceholder')}
          placeholderTextColorStyle={theme.colors.secondary}
          value={search}
          onChangeText={setSearch}
        />
      </View>
      <FlatList
        data={filteredGroups}
        showsVerticalScrollIndicator={false}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderComponent}
        contentContainerStyle={styles.content}
        onEndReachedThreshold={0.5}
        onEndReached={loadMoreData}
        ListFooterComponent={() =>
          paginationLoader ? (
            <ActivityIndicator color={theme.colors.activeColor} size="small" />
          ) : null
        }
      />
    </SafeAreaView>
  );
};

export default JoinGroups;
