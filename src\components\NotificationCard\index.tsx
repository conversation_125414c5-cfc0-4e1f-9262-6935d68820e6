import { useThemeStore } from '@/store/themeStore';;
import React from 'react';
import {StyleSheet, View, Image, ImageSourcePropType, TouchableOpacity} from 'react-native';
import Typography from '../Typography';
import {Icon} from '..';

interface NotificationCardProps {
  title?: string;
  type?: 'invite' | 'default';
  image?: ImageSourcePropType;
  community?: string;
  time?: string;
  highlight?: boolean;
}

const NotificationCard = ({
  title,
  type,
  image,
  community,
  time,
  highlight,
}: NotificationCardProps) => {
  const theme = useThemeStore();

  return (
    <View style={styles(theme).container}>
      <View style={styles(theme).imgAndTitleContainer}>
        {image && <Image source={image} style={styles(theme).image} />}
      </View>
      <View style={styles(theme).titleContainer}>
        {community && (
          <View style={styles(theme).communityTimeContainer}>
            <Typography variant="tagTitle" color={theme.colors.white}>
              {community}
            </Typography>
            {time && (
              <Typography variant="tagTitle" color={theme.colors.white}>
                {time}
              </Typography>
            )}
          </View>
        )}
        <Typography
          variant="notificationText"
          style={styles(theme).title}
          color={highlight ? theme.colors.orange : theme.colors.white}>
          {title}
        </Typography>
        {type === 'invite' && (
          <View style={styles(theme).buttonContainer}>
            <TouchableOpacity
              style={[
                styles(theme).plusButton,
                {
                  backgroundColor: theme.colors.activeColor,
                },
              ]}>
              <Icon name={'check'} size={24} color={theme.colors.background} />
            </TouchableOpacity>
            <TouchableOpacity activeOpacity={0.7}>
              <Icon name="editpen" size={30} color={theme.colors.orange} />
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.notificationBg,
      width: '100%',
      paddingLeft: 16,
      paddingVertical: 12,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      borderRadius: 10,
      flexDirection: 'row',
      overflow: 'hidden',
    },
    imgAndTitleContainer: {
      flexDirection: 'row',
    },
    image: {
      width: 42,
      height: 42,
      marginRight: 15,
      borderRadius: 100,
    },
    titleContainer: {
      flex: 1,
      paddingRight: 16,
      justifyContent: 'center',
    },
    title: {
      marginRight: 16,
    },
    plusButton: {
      width: 36,
      height: 36,
      borderRadius: 30,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 15,
    },
    buttonContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 8,
    },
    communityTimeContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 5,
      marginTop: -5,
    },
  });

export default NotificationCard;
