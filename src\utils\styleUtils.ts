import {StyleSheet} from 'react-native';
import { ThemeState } from '@/store/themeStore';

// Define interface for state parameter
interface StateWithTheme {
  theme: ThemeState;
}

export const getGlobalStyles = (state: StateWithTheme) => {
  const {theme} = state;
  const {fontSize, colors} = theme;

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      padding: 16,
    },
    text: {
      color: colors.text,
      fontSize: fontSize[fontSize.default],
    },
    title: {
      color: colors.text,
      fontSize: fontSize[fontSize.default] + 8,
      fontWeight: 'bold',
    },
    button: {
      backgroundColor: colors.primary,
      padding: 12,
      borderRadius: 8,
      alignItems: 'center',
      marginVertical: 8,
    },
    buttonText: {
      color: theme.colors.white,
      fontSize: fontSize[fontSize.default],
      fontWeight: '600',
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.white2,
      borderRadius: 8,
      padding: 12,
      marginVertical: 8,
      color: colors.text,
      fontSize: fontSize[fontSize.default],
      backgroundColor: theme.colors.white1,
    },
  });
};
