/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

// ✅ CRITICAL: react-native-gesture-handler MUST be first import
import 'react-native-gesture-handler';

import React, {useEffect, useRef} from 'react';
import {AppNavigator} from '@/navigation';
// import {ActivityIndicator} from 'react-native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {StyleSheet, AppState, AppStateStatus, Platform} from 'react-native';
import {enableScreens} from 'react-native-screens';
import BiometricGate from '@/components/BiometricGate';
import {configureGoogleSignIn, configureFacebookSDK} from '@/utils/auth';
import {
  setupMemoryCleaning,
  cleanMemoryIfNeeded,
  setupTextureErrorHandlers,
} from '@/utils/memoryManager';
// Zustand stores are initialized automatically

import {EnhancedNetworkProvider} from '@/context/EnhancedNetworkContext';
import {NavigationContainerRef} from '@react-navigation/native';
import {RootStackParamList} from '@/navigation';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {QueryClientProvider} from '@/providers/QueryClientProvider';
// import {prefetchInitialQueries} from '@/utils/prefetchQueries';

// Import geolocation at the top level
import Geolocation from '@react-native-community/geolocation';
import {SocketProvider} from '@/context/SocketContext';
import {TranslationProvider} from '@/context/TranslationContext';
import * as Sentry from '@sentry/react-native';
import {initSentry, setUserContext} from '@/utils/sentryUtils';
import {ErrorBoundary} from '@/components/ErrorBoundary';
import {useAuthStore} from '@/store/authStore';
import {cleanupTokenRefresh, initializeTokenRefresh} from '@/utils/tokenRefreshManager';
import CToast from '@/components/CToast';

// Initialize Sentry with our custom configuration
initSentry();

enableScreens();
// Initialize geolocation service
const initializeGeolocation = () => {
  try {
    // Configure geolocation
    if (Platform.OS === 'ios') {
      Geolocation.setRNConfiguration({
        skipPermissionRequests: false,
        authorizationLevel: 'whenInUse',
      });
    }

    console.log('Geolocation service initialized');
  } catch (error) {
    console.error('Failed to initialize geolocation service:', error);
  }
};

// Main App component with AppContext Provider
function App(): React.JSX.Element {
  // Reference to the navigation container
  const navigationRef = useRef<NavigationContainerRef<RootStackParamList>>(null);
  const {user} = useAuthStore();

  // Set up Sentry user context when user changes
  useEffect(() => {
    setUserContext(user);
  }, [user]);

  // Initialize services
  useEffect(() => {
    // Configure Google Sign-In
    configureGoogleSignIn();

    // Configure Facebook SDK
    configureFacebookSDK();

    // Initialize geolocation service
    initializeGeolocation();

    // Prefetch initial queries
    // prefetchInitialQueries().catch(error => {
    //   console.error('Failed to prefetch initial queries:', error);
    // });
  }, []);

  // Set up memory management
  useEffect(() => {
    // Set up periodic memory cleaning
    const cleanupMemoryManager = setupMemoryCleaning();

    // Set up texture error handlers
    const cleanupErrorHandlers = setupTextureErrorHandlers();

    // Clean memory when app goes to background
    const appStateSubscription = AppState.addEventListener(
      'change',
      (nextAppState: AppStateStatus) => {
        if (nextAppState === 'background' || nextAppState === 'inactive') {
          // App is going to background, clean memory
          cleanMemoryIfNeeded(true);
        }
      },
    );

    // Initial memory clean
    cleanMemoryIfNeeded();

    // Cleanup on unmount
    return () => {
      cleanupMemoryManager();
      cleanupErrorHandlers();
      appStateSubscription.remove();
    };
  }, []);

  // Token refresh lifecycle
  useEffect(() => {
    initializeTokenRefresh();
    return () => cleanupTokenRefresh();
  }, []);

  return (
    <ErrorBoundary componentName="App">
      <SafeAreaProvider>
        <QueryClientProvider>
          <EnhancedNetworkProvider>
            <SocketProvider>
              <TranslationProvider>
                <BiometricGate>
                  <GestureHandlerRootView style={styles.container}>
                    <AppNavigator ref={navigationRef} />
                    <CToast />
                  </GestureHandlerRootView>
                </BiometricGate>
              </TranslationProvider>
            </SocketProvider>
          </EnhancedNetworkProvider>
        </QueryClientProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}

export default Sentry.wrap(App);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
