import {StyleSheet} from 'react-native';
export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingHorizontal: 20,
    },
    headerContainer: {
      flexDirection: 'row',
      marginBottom: 30,
      position: 'relative',
      alignItems: 'center',
      justifyContent: 'flex-start',
      paddingTop: 20,
      gap: 10,
    },
    backButton: {
      padding: 5,
    },
    title: {
      fontSize: theme.fontSize.xlarge,
      color: theme.colors.black,
    },
    form: {
      flex: 1,
      marginBottom: 20,
    },
    inputContainer: {
      marginBottom: 20,
    },
    label: {
      marginBottom: 8,
      fontWeight: '500',
      color: theme.colors.white,
      fontSize: theme.fontSize.font14,
    },
    input: {
      height: 50,
      paddingHorizontal: 16,
      borderRadius: 8,
      borderWidth: 1,
    },
    forgotPasswordContainer: {
      alignSelf: 'flex-end',
      marginBottom: 20,
      padding: 2,
    },
    forgotPasswordText: {
      color: theme.colors.link,
      fontSize: theme.fontSize.font14,
      fontWeight: 'bold',
      textDecorationLine: 'underline',
    },
    nextButton: {
      padding: theme.fontSize.font14,
      borderRadius: 8,
      alignItems: 'center',
      marginBottom: 20,
    },
    nextButtonText: {
      color: theme.colors.white,
      fontSize: theme.fontSize.medium,
      fontWeight: '600',
    },
    signupContainer: {
      alignItems: 'center',
      marginTop: 10,
    },
    signupText: {
      fontSize: theme.fontSize.font14,
      color: theme.colors.gray,
      textAlign: 'center',
    },
    signupLink: {
      fontWeight: 'bold',
      color: theme.colors.link,
      textDecorationLine: 'underline',
    },
  });
