import {useThemeStore} from '@/store/themeStore';
import React from 'react';
import {KeyboardAvoidingView, Platform, ScrollView, TouchableOpacity, View} from 'react-native';
import {createStyles} from './styles';
import {Icon} from '@/components';
import {useNavigation} from '@react-navigation/native';
import Typography from '@/components/Typography';
import {FONT_SIZE} from '@/utils/fonts';
import useTranslation from '@/hooks/useTranslation';

const ManageContent = (props: any) => {
  const {onClose} = props;
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation();

  const {t} = useTranslation();

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardAvoidingView}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}>
        <View style={styles.headerContainer}>
          <TouchableOpacity onPress={onClose}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
          <Typography variant="invitePlayersTitle" color={theme.colors.white}>
            {t('manageContentScreen.title')}
          </Typography>
        </View>
        <View style={styles.titleContainer}>
          <Typography variant="invitePlayersTitle" color={theme.colors.white}>
            {t('manageContentScreen.storage')}
          </Typography>
          <TouchableOpacity activeOpacity={0.8} style={styles.upgradeContainer}>
            <Typography
              variant="invitePlayersTitle"
              color={theme.colors.white}
              style={{fontSize: FONT_SIZE.md, lineHeight: 25}}>
              {t('manageContentScreen.upgrade')}
            </Typography>
          </TouchableOpacity>
        </View>
        <View style={styles.statsContainer}>
          <Typography variant="storageTag" color={theme.colors.offWhite}>
            {t('manageContentScreen.stats')}
          </Typography>
          <TouchableOpacity activeOpacity={0.7}>
            <Typography variant="selectAll" color={theme.colors.offWhite}>
              {t('manageContentScreen.selectAll')}
            </Typography>
          </TouchableOpacity>
        </View>
        <View style={styles.gridContainer}>
          {[...Array(8)].map((_, idx) => (
            <View key={idx} style={styles.gridBox} />
          ))}
        </View>
        <Typography variant="storageTag" color={theme.colors.offWhite}>
          {t('manageContentScreen.videos')}
        </Typography>
        <View style={styles.videosContainer}>
          <Typography variant="selectAll" color={theme.colors.offWhite}>
            {t('manageContentScreen.today')}
          </Typography>
          <TouchableOpacity activeOpacity={0.7}>
            <Typography variant="selectAll" color={theme.colors.offWhite}>
              {t('manageContentScreen.selectAll')}
            </Typography>
          </TouchableOpacity>
        </View>
        <View style={styles.gridContainer}>
          {[...Array(6)].map((_, idx) => (
            <View key={idx} style={styles.gridBox} />
          ))}
        </View>
        <View style={[styles.videosContainer, {marginTop: 0}]}>
          <Typography variant="selectAll" color={theme.colors.offWhite}>
            {t('manageContentScreen.yesterday')}
          </Typography>
          <TouchableOpacity activeOpacity={0.7}>
            <Typography variant="selectAll" color={theme.colors.offWhite}>
              {t('manageContentScreen.selectAll')}
            </Typography>
          </TouchableOpacity>
        </View>
        <View style={styles.gridContainer}>
          {[...Array(3)].map((_, idx) => (
            <View key={idx} style={styles.gridBox} />
          ))}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default ManageContent;
