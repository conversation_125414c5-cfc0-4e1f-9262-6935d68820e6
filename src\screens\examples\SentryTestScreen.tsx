import React, {useState} from 'react';
import {View, StyleSheet, ScrollView, Alert} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import * as Sentry from '@sentry/react-native';
import {logError, logMessage} from '@/utils/sentryUtils';
// import {ApiErrorBoundary} from '@/components/ErrorBoundary';

/**
 * A screen to test Sentry error reporting functionality
 */
const SentryTestScreen = () => {
  const theme = useThemeStore();
  const [isLoading, setIsLoading] = useState(false);

  // Test a handled exception
  const testHandledException = () => {
    try {
      // Simulate an error
      throw new Error('This is a test handled exception');
    } catch (error) {
      if (error instanceof Error) {
        // Log the error to Sentry
        logError(error, {
          context: 'SentryTestScreen',
          action: 'testHandledException',
        });

        // Show a success message
        Alert.alert(
          'Handled Exception Logged',
          'A handled exception was successfully logged to Sentry.',
        );
      }
    }
  };

  // Test an unhandled exception (will be caught by error boundary)
  const testUnhandledException = () => {
    // This will throw an error that will be caught by the error boundary
    const users: any[] = null;
    // @ts-ignore - intentional error for testing
    users.map(user => user.name);
  };

  // Test a message log
  const testMessageLog = () => {
    // Log a message to Sentry
    logMessage('This is a test message from SentryTestScreen', 'info', {
      context: 'SentryTestScreen',
      action: 'testMessageLog',
    });

    // Show a success message
    Alert.alert('Message Logged', 'A message was successfully logged to Sentry.');
  };

  // Test a breadcrumb
  const testBreadcrumb = () => {
    // Add a breadcrumb to Sentry
    Sentry.addBreadcrumb({
      category: 'test',
      message: 'User clicked the test breadcrumb button',
      level: 'info',
    });

    // Show a success message
    Alert.alert('Breadcrumb Added', 'A breadcrumb was successfully added to Sentry.');
  };

  // Test a performance transaction
  const testPerformanceTransaction = async () => {
    setIsLoading(true);

    // Start a transaction
    const transaction = Sentry.startTransaction({
      name: 'test-transaction',
      op: 'test',
    });

    // Set the transaction on the scope
    Sentry.configureScope(scope => {
      scope.setSpan(transaction);
    });

    try {
      // Create a child span
      const span = transaction.startChild({
        op: 'task',
        description: 'Simulating a long task',
      });

      // Simulate a long task
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Finish the span
      span.finish();

      // Show a success message
      Alert.alert(
        'Performance Transaction Completed',
        'A performance transaction was successfully recorded in Sentry.',
      );
    } finally {
      // Finish the transaction
      transaction.finish();
      setIsLoading(false);
    }
  };

  // Test user feedback
  const testUserFeedback = () => {
    // Create an event ID
    const eventId = Sentry.captureMessage('User feedback test');

    // Show the user feedback dialog
    Sentry.showReportDialog({
      eventId,
      title: 'Report a Problem',
      subtitle: 'Tell us what happened',
      subtitle2: 'Your feedback helps us improve the app',
      labelName: 'Name',
      labelEmail: 'Email',
      labelComments: 'What happened?',
      labelSubmit: 'Submit',
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header title="Sentry Test" showBackButton />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Typography variant="h2" style={styles.title}>
          Sentry Error Reporting Tests
        </Typography>

        <Typography variant="body" style={styles.description}>
          This screen allows you to test various Sentry error reporting features. Each button will
          trigger a different type of error or event that will be sent to the Sentry dashboard.
        </Typography>

        <View style={styles.buttonContainer}>
          <CButton
            title="Test Handled Exception"
            onPress={testHandledException}
            containerStyle={styles.button}
          />

          {/* <ApiErrorBoundary apiName="Test API"> */}
          <CButton
            title="Test Test API Unhandled Exception"
            onPress={testUnhandledException}
            containerStyle={styles.button}
            variant="secondary"
          />
          {/* </ApiErrorBoundary> */}

          <CButton
            title="Test Message Log"
            onPress={testMessageLog}
            containerStyle={styles.button}
            variant="outline"
          />

          <CButton
            title="Test Breadcrumb"
            onPress={testBreadcrumb}
            containerStyle={styles.button}
            variant="dark"
          />

          {/* <CButton
            title="Test Performance Transaction"
            onPress={testPerformanceTransaction}
            containerStyle={styles.button}
            loading={isLoading}
          /> */}

          <CButton
            title="Test User Feedback"
            onPress={testUserFeedback}
            containerStyle={styles.button}
            variant="primary"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  title: {
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    marginBottom: 24,
    textAlign: 'center',
  },
  buttonContainer: {
    gap: 12,
  },
  button: {
    marginBottom: 8,
  },
});

export default SentryTestScreen;
