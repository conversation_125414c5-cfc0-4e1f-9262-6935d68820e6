import {Dimensions, StyleSheet} from 'react-native';

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    contentContainer: {
      flexGrow: 1,
      paddingTop: 0,
      paddingBottom: 20,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },

    card: {
      width: Dimensions.get('screen').width * 0.75,
      borderRadius: 27,
    },
    sportsContent: {
      flexGrow: 1,
      marginTop: 4,
    },
    carousel: {paddingBottom: 20, marginLeft: 20, overflow: 'visible'},
    activeDot: {
      backgroundColor: theme.colors.orange1,
      height: 11,
      borderRadius: 10,
      width: 26,
    },
    dot: {
      backgroundColor: 'transparent',
      height: 11,
      width: 26,
    },
    pagination: {
      backgroundColor: theme.colors.secondary,
      borderRadius: 10,
      gap: 0,
      padding: 0,
      marginTop: -10,
    },
  });

export default styles;
