import React from 'react';
import {FlatList, ScrollView, TouchableOpacity, View} from 'react-native';
import {useThemeStore} from '@/store';
import Typography from '@/components/Typography';
import {createStyles} from './styles';
import {useNavigation} from '@react-navigation/native';
import {Icon} from '@/components';
import CustomDateTimePicker from '@/components/CustomDateTimePicker/CustomDateTimePicker';
import {DrawerNavigationProp} from '@react-navigation/drawer';
import PillLabel from '@/components/PillLabel';
import {CollapsibleViewWithIcon} from '@/components/CollapsibleView';
import useTranslation from '@/hooks/useTranslation';

type RootDrawerParamList = {
  CoachOptions: undefined;
  // ... other screens
};
type NavigationProp = DrawerNavigationProp<RootDrawerParamList>;

const CalendarScreen = (props: any) => {
  const {onClose} = props;
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();

  const {t} = useTranslation();

  const renderItem = ({item}: {item: string}) => {
    return (
      <CollapsibleViewWithIcon title={item} titleStyle={styles.collapsibleTitle}>
        <View style={styles.box}>
          <View style={{flex: 1}}>
            <Typography variant="subtitle2" style={{color: theme.colors.text, flex: 1}}>
              Beginners 9:00am - 10:00am
            </Typography>
          </View>
          <Typography variant="tagTitle" color={theme.colors.white} style={{textAlign: 'right'}}>
            January 6, 2025
          </Typography>
        </View>
      </CollapsibleViewWithIcon>
    );
  };
  return (
    <View style={styles.container}>
      <View style={[styles.headerRow, styles.padding16]}>
        <View style={styles.headerCol}>
          <TouchableOpacity onPress={onClose}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
          <Typography variant="title2" color={theme.colors.white}>
            {t('calendarScreen.title')}
          </Typography>
        </View>
        <TouchableOpacity activeOpacity={0.8}>
          <Icon name="setting" size={24} color={theme.colors.white} />
        </TouchableOpacity>
      </View>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.calendarContainer}>
          <CustomDateTimePicker
            onClose={() => {}}
            onConfirm={() => {}}
            //   initialStartDate={new Date()}
            //   initialEndDate={new Date()}
            hideActions
            hidePlayerInput
            allowRangeSelection
            minDate={new Date()}
          />
          <View style={styles.pillContainer}>
            <PillLabel
              label={t('calendarScreen.newClass')}
              containerStyle={styles.pill}
              textStyle={styles.pillText}
            />
            <PillLabel
              label={t('calendarScreen.manageClasses')}
              containerStyle={styles.pill}
              textStyle={styles.pillText}
            />
          </View>
          <View style={styles.divider} />

          <FlatList
            data={[
              t('calendarScreen.today'),
              t('calendarScreen.upcoming'),
              t('calendarScreen.widget'),
            ]}
            renderItem={renderItem}
            contentContainerStyle={{flexGrow: 1}}
            style={{flex: 1}}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default CalendarScreen;
