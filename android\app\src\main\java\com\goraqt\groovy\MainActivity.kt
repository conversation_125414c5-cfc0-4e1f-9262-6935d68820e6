package com.goraqt.groovy

import android.os.Bundle
import android.view.View
import android.view.WindowManager
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate

class MainActivity : ReactActivity() {

  /**
   * Enhanced onCreate to prevent any default splash screen or white flash
   */
  override fun onCreate(savedInstanceState: Bundle?) {
    // Keep the splash theme until React Native is ready to render
    // This prevents the white screen flash
    // super.onCreate(savedInstanceState)
    super.onCreate(null)

    // Make sure we're using a black background
    window.decorView.setBackgroundColor(android.graphics.Color.BLACK)

    // Ensure the status bar is transparent with white icons
    window.statusBarColor = android.graphics.Color.TRANSPARENT
  }

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "GoRaqt"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)
}
