import React, {createContext, useState, useContext, useEffect, ReactNode} from 'react';
import {AppState, AppStateStatus} from 'react-native';
import NetworkService, {
  NetworkStatus,
  ConnectivityLevel,
  NetworkType,
} from '../utils/networkService';
import NoInternetScreen from '../screens/NoInternet/NoInternetScreen';

// Simplified interface without offline functionality
interface EnhancedNetworkContextType {
  networkStatus: NetworkStatus;
  isConnected: boolean;
  isInternetReachable: boolean;
  connectivityLevel: ConnectivityLevel;
  checkConnection: () => Promise<boolean>;
}

const EnhancedNetworkContext = createContext<EnhancedNetworkContextType>({
  networkStatus: {
    isConnected: false,
    isInternetReachable: false,
    type: NetworkType.UNKNOWN,
    connectivityLevel: ConnectivityLevel.UNKNOWN,
    details: null,
    timestamp: Date.now(),
  },
  isConnected: false,
  isInternetReachable: false,
  connectivityLevel: ConnectivityLevel.UNKNOWN,
  checkConnection: async () => false,
});

export const useEnhancedNetwork = () => useContext(EnhancedNetworkContext);

interface EnhancedNetworkProviderProps {
  children: ReactNode;
}

export const EnhancedNetworkProvider: React.FC<EnhancedNetworkProviderProps> = ({children}) => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>(
    NetworkService.getInstance().getCurrentStatus(),
  );
  const [appState, setAppState] = useState<AppStateStatus>(AppState.currentState);
  const [isFirstConnect, setIsFirstConnect] = useState<boolean>(true);
  const [showNoInternet, setShowNoInternet] = useState<boolean>(false);

  // Initialize services
  useEffect(() => {
    // Initialize network service
    NetworkService.getInstance().initialize();

    // Set up network status listener
    const unsubscribeNetwork = NetworkService.getInstance().addListener(status => {
      setNetworkStatus(status);

      // If we're connected for the first time, mark it
      if (status.isConnected && status.isInternetReachable && isFirstConnect) {
        setIsFirstConnect(false);
      }

      // Show no internet screen if we're not connected and not on first load
      if (!status.isConnected && !status.isInternetReachable && !isFirstConnect) {
        setShowNoInternet(true);
      } else if (status.isConnected && status.isInternetReachable) {
        setShowNoInternet(false);
      }
    });

    // Set up app state listener
    const appStateListener = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      unsubscribeNetwork();
      appStateListener.remove();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFirstConnect]);

  // Handle app state changes
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (appState !== nextAppState) {
      setAppState(nextAppState);

      // Update network service with app state
      NetworkService.getInstance().setAppState(
        nextAppState === 'background' || nextAppState === 'inactive',
      );

      // If coming back to foreground, check connection
      if (nextAppState === 'active') {
        checkConnection();
      }
    }
  };

  // Check connection
  const checkConnection = async (): Promise<boolean> => {
    // Force a network check
    try {
      // Force our NetworkService to check connectivity
      await NetworkService.getInstance().checkConnectivityNow();

      // Get the updated status
      const updatedStatus = NetworkService.getInstance().getCurrentStatus();

      // Update our state
      setNetworkStatus(updatedStatus);

      const isConnected = updatedStatus.isConnected && updatedStatus.isInternetReachable;

      // Update no internet screen visibility
      if (isConnected) {
        setShowNoInternet(false);
      } else if (!isFirstConnect) {
        setShowNoInternet(true);
      }

      return isConnected;
    } catch (error) {
      console.error('Error checking connection:', error);

      // Show no internet screen on error if not first connect
      if (!isFirstConnect) {
        setShowNoInternet(true);
      }

      return false;
    }
  };

  // Handle retry connection
  const handleRetry = async () => {
    const isConnected = await checkConnection();
    if (isConnected) {
      setShowNoInternet(false);
    }
  };

  // If we're showing the no internet screen, render it
  if (showNoInternet) {
    return <NoInternetScreen onRetry={handleRetry} />;
  }

  return (
    <EnhancedNetworkContext.Provider
      value={{
        networkStatus,
        isConnected: networkStatus.isConnected && networkStatus.isInternetReachable,
        isInternetReachable: networkStatus.isInternetReachable,
        connectivityLevel: networkStatus.connectivityLevel,
        checkConnection,
      }}>
      {children}
    </EnhancedNetworkContext.Provider>
  );
};
