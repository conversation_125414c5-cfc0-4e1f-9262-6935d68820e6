diff --git a/node_modules/react-native-maps/android/src/main/java/com/rnmaps/maps/MapMarker.java b/node_modules/react-native-maps/android/src/main/java/com/rnmaps/maps/MapMarker.java
index 016e023..937f9b8 100644
--- a/node_modules/react-native-maps/android/src/main/java/com/rnmaps/maps/MapMarker.java
+++ b/node_modules/react-native-maps/android/src/main/java/com/rnmaps/maps/MapMarker.java
@@ -1,5 +1,7 @@
 package com.rnmaps.maps;
 
+import android.animation.ObjectAnimator;
+import android.animation.TypeEvaluator;
 import android.content.Context;
 import android.graphics.Bitmap;
 import android.graphics.BitmapFactory;
@@ -8,11 +10,11 @@ import android.graphics.Color;
 import android.graphics.drawable.Animatable;
 import android.graphics.drawable.Drawable;
 import android.net.Uri;
+import android.os.Handler;
+import android.os.Looper;
+import android.util.Property;
 import android.view.View;
 import android.widget.LinearLayout;
-import android.animation.ObjectAnimator;
-import android.util.Property;
-import android.animation.TypeEvaluator;
 
 import androidx.annotation.Nullable;
 
@@ -318,6 +320,9 @@ public class MapMarker extends MapFeature {
 
         updateMarkerIcon();
 
+        new Handler(Looper.getMainLooper()).post(() -> tracksViewChangesActive = false);
+
+
         return true;
     }
 
@@ -687,6 +692,13 @@ public class MapMarker extends MapFeature {
         return BitmapDescriptorFactory.fromResource(getDrawableResourceByName(name));
     }
 
+    @Override
+    protected void onLayout(boolean changed, int l, int t, int r, int b) {
+        super.onLayout(changed, l, t, r, b);
+        this.height = b - t;
+        this.width = r - l;
+    }
+
     public static Map<String, Object> getExportedCustomBubblingEventTypeConstants() {
         MapBuilder.Builder<String, Object> builder = MapBuilder.builder();
         builder.put(OnPressEvent.EVENT_NAME, MapBuilder.of("registrationName", OnPressEvent.EVENT_NAME));
diff --git a/node_modules/react-native-maps/android/src/main/java/com/rnmaps/maps/ViewChangesTracker.java b/node_modules/react-native-maps/android/src/main/java/com/rnmaps/maps/ViewChangesTracker.java
index 238fcb5..23ffbfb 100644
--- a/node_modules/react-native-maps/android/src/main/java/com/rnmaps/maps/ViewChangesTracker.java
+++ b/node_modules/react-native-maps/android/src/main/java/com/rnmaps/maps/ViewChangesTracker.java
@@ -15,13 +15,13 @@ public class ViewChangesTracker {
   private final long fps = 40;
 
   private ViewChangesTracker() {
-    handler = new Handler(Looper.myLooper());
+    handler = new Handler(Looper.getMainLooper());
     updateRunnable = new Runnable() {
       @Override
       public void run() {
         update();
 
-        if (markers.size() > 0) {
+        if (!markers.isEmpty()) {
           handler.postDelayed(updateRunnable, fps);
         } else {
           hasScheduledFrame = false;
@@ -67,7 +67,7 @@ public class ViewChangesTracker {
     }
 
     // Remove markers that are not active anymore
-    if (markersToRemove.size() > 0) {
+    if (!markersToRemove.isEmpty()) {
       markers.removeAll(markersToRemove);
       markersToRemove.clear();
     }
