import React, {useState, useEffect, useMemo} from 'react';
import FastImageBase, {
  FastImageProps as BaseFastImageProps,
  ResizeMode,
} from 'react-native-fast-image';
import {ActivityIndicator, StyleSheet, View} from 'react-native';

export interface FastImageProps extends Omit<BaseFastImageProps, 'source' | 'style'> {
  source: string | {uri: string};
  style?: object;
  loaderColor?: string;
  fallbackSource?: string | {uri: string};
  resizeMode?: ResizeMode;
}

const CImage: React.FC<FastImageProps> = ({
  source,
  style,
  loaderColor = '#288ECE',
  fallbackSource,
  resizeMode = 'cover',
  ...props
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoadEnd = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const imageSource = useMemo(() => {
    if (typeof source === 'string') {
      return {uri: source};
    }
    return source;
  }, [source]);

  const fallbackImageSource = useMemo(() => {
    if (typeof fallbackSource === 'string') {
      return {uri: fallbackSource};
    }
    return fallbackSource;
  }, [fallbackSource]);

  // Memory management - cleanup when component unmounts or source changes
  useEffect(() => {
    return () => {
      // Clear image from memory cache when component unmounts
      if (imageSource && imageSource.uri) {
        // Use a web cache control strategy to reduce memory pressure
        FastImageBase.preload([{uri: imageSource.uri, cache: FastImageBase.cacheControl.web}]);
      }
    };
  }, [imageSource]);

  return (
    <>
      <FastImageBase
        source={hasError && fallbackImageSource ? fallbackImageSource : imageSource}
        style={style}
        onLoadEnd={handleLoadEnd}
        onError={handleError}
        resizeMode={resizeMode}
        {...props}
      />
      {isLoading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator color={loaderColor} />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  loaderContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});

// Utility function to clear image cache
export const clearImageCache = () => {
  FastImageBase.clearMemoryCache();
  FastImageBase.clearDiskCache();
};

export default CImage;
