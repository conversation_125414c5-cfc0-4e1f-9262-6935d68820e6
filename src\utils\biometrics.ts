import ReactNativeBiometrics, { BiometryTypes } from 'react-native-biometrics';
import AsyncStorage from '@react-native-async-storage/async-storage';

const rnBiometrics = new ReactNativeBiometrics({ allowDeviceCredentials: true });
const BIOMETRICS_ENABLED_KEY = '@goRaqt:biometricsEnabled';

/**
 * Check if the device supports biometric authentication
 * @returns {Promise<{available: boolean, biometryType?: string}>}
 */
export const isBiometricsAvailable = async () => {
  try {
    const { available, biometryType } = await rnBiometrics.isSensorAvailable();
    return { available, biometryType };
  } catch (error) {
    console.error('Error checking biometrics availability:', error);
    return { available: false };
  }
};

/**
 * Enable biometric authentication for the app
 * @returns {Promise<boolean>} - Returns true if biometrics were successfully enabled
 */
export const enableBiometrics = async () => {
  try {
    // First check if biometrics are available
    const { available } = await isBiometricsAvailable();

    if (!available) {
      return false;
    }

    // Create the keys for biometric authentication if they don't exist
    const { keysExist } = await rnBiometrics.biometricKeysExist();

    if (!keysExist) {
      await rnBiometrics.createKeys();
    }

    // Store the preference in AsyncStorage
    await AsyncStorage.setItem(BIOMETRICS_ENABLED_KEY, 'true');
    return true;
  } catch (error) {
    console.error('Error enabling biometrics:', error);
    return false;
  }
};

/**
 * Disable biometric authentication for the app
 * @returns {Promise<boolean>} - Returns true if biometrics were successfully disabled
 */
export const disableBiometrics = async () => {
  try {
    // Delete the keys
    await rnBiometrics.deleteKeys();

    // Remove the preference from AsyncStorage
    await AsyncStorage.removeItem(BIOMETRICS_ENABLED_KEY);
    return true;
  } catch (error) {
    console.error('Error disabling biometrics:', error);
    return false;
  }
};

/**
 * Check if biometric authentication is enabled
 * @returns {Promise<boolean>}
 */
export const isBiometricsEnabled = async () => {
  try {
    const enabled = await AsyncStorage.getItem(BIOMETRICS_ENABLED_KEY);
    return enabled === 'true';
  } catch (error) {
    console.error('Error checking if biometrics is enabled:', error);
    return false;
  }
};

/**
 * Authenticate the user using biometrics
 * @param {string} promptMessage - Message to display in the biometric prompt
 * @returns {Promise<boolean>} - Returns true if authentication was successful
 */
export const authenticateWithBiometrics = async (promptMessage = 'Authenticate to access GoRaqt') => {
  try {
    // Check if biometrics are enabled
    const enabled = await isBiometricsEnabled();
    if (!enabled) {
      return false;
    }

    // Prompt for authentication
    const { success } = await rnBiometrics.simplePrompt({
      promptMessage,
    });

    return success;
  } catch (error) {
    console.error('Error authenticating with biometrics:', error);
    return false;
  }
};
