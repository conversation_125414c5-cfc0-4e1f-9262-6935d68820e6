import React, {useCallback, useMemo, useState, useEffect} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, ScrollView} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {createDrawerNavigator} from '@react-navigation/drawer';
import TabNavigator from './TabNavigator';
import {useThemeStore} from '@/store/themeStore';
import {useAuthStore} from '@/store/authStore';
import {CImage, CustomModal, Icon} from '@/components';
import SettingsScreen from '@/screens/settings/SettingsScreen';
import Typography from '@/components/Typography';
import EditProfileScreen from '@/screens/drawer/EditProfileScreen';
import {Images} from '@/config';
import SearchInput from '@/components/SearchInput';
import OrderReserveEquipment from '@/components/Drawer/AssistantCoach/OrderReserveEquipment';
import InvitePlayers from '@/components/Drawer/AssistantCoach/InvitePlayers';
import CoachOptionsScreen from '@/components/Drawer/AssistantCoach/CoachOptions';
import GetCertified from '@/components/Drawer/AssistantCoach/GetCertified';
import MessageNotificationPreferences from '@/components/Drawer/AssistantCoach/MessageNotificationPreferences';
import WelcomeScreen from '@/components/Drawer/AssistantCoach/Welcome';
import PostAnAd from '@/components/Drawer/AssistantCoach/PostAnAd';
import ManageContent from '@/components/Drawer/AssistantCoach/ManageContent';
import EditCoachProfile from '@/components/Drawer/AssistantCoach/EditCoachProfile';
import ManageClasses from '@/components/Drawer/AssistantCoach/ManageClasses';
import ManageServiceRequest from '@/components/Drawer/AssistantCoach/ManageServiceRequest';
import HelpScreen from '@/screens/Help';
import CalendarScreen from '@/components/Drawer/AssistantCoach/Calendar';
import ManagePlayerAssetsScreen from '@/components/Drawer/AssistantCoach/ManagePlayerAssets';
import {useConfigStore} from '@/store';
import ManageCommunityScreen from '@/screens/ManageCommunity';
import OrdersScreen from '@/screens/Orders';
import RewardsScreen from '@/screens/Rewards';
import RecycleBallsScreen from '@/screens/RecycleBalls';
import ProfileQrScreen from '@/screens/ProfileQrScreen';
import useTranslation from '@/hooks/useTranslation';
import {drawerMenuList} from '@/config/staticData';
import DateScreen from '@/screens/tabs/SearchScreen/DateScreen';
import EquipmentReservationScreen from '@/screens/tabs/SearchScreen/EquipmentReservationScreen';
import RacquetCategoryScreen from '@/screens/tabs/RacquetsScreens/RacqetsCategory';
import RacquetsBrandsScreen from '@/screens/tabs/RacquetsScreens/Brands';
import RacquetSelector from '@/screens/tabs/RacquetsScreens/RacquetSelector';
import RacquetSelectorDetail from '@/screens/tabs/RacquetsScreens/RacquetSelectorDetail';
import PlayerConnectScreen from '@/screens/tabs/PlayerConnectScreen';
import GoEats from '@/screens/GoEats';
import GoTravel from '@/screens/GoTravel';
import CommunityDetails from '@/screens/CommunityDetails';
import CommentScreen from '@/screens/CommentScreen';
import CoachProfile from '@/screens/UpYourGame/CoachProfile';
import FindClass from '@/screens/UpYourGame/FindClass';
import FindCoach from '@/screens/UpYourGame/FindCoach';
import MyGroups from '@/screens/MyGroups';
import NewGroup from '@/components/Community/NewGroup';
import JoinGroups from '@/screens/JoinGroups';
import JoinGroupDetails from '@/screens/JoinGroupDetails';
import PlayerConnectDateScreen from '@/screens/PlayerConnectDateScreen';
import AddMembers from '@/components/Community/AddMembers';
import CreateGroupMemberList from '@/screens/CreateGroupMemberList';
import ChatScreen from '@/screens/Chat';
import GoFit from '@/screens/GoFit';

// Type definition for drawer item
type DrawerItemType = {
  isVisible: boolean;
  id: string;
  label: string;
  screen?: string;
  onPress?: () => void;
};
export type DrawerParamList = {
  welcome: undefined;
  OrderReserveEquipment: undefined;
  InvitePlayers: undefined;
  CoachOptions: undefined;
  getCertified: undefined;
  MessageNotificationPreferences: undefined;
  Settings: undefined;
  EditProfile: undefined;
  MainTabs: undefined;
  PostAnAd: undefined;
  ManageContent: undefined;
  ManageClasses: undefined;
  ManageServiceRequest: undefined;
  ManagePlayerAssets: undefined;
  ManageCommunity: undefined;
  Orders: undefined;
  Help: undefined;
  Rewards: undefined;
  RecycleBalls: undefined;
  ProfileQrScreen: undefined;
  DateScreen: undefined;
  EquipmentReservationScreen: undefined;
  RacquetCategory: {
    sportsTitle: string;
  };
  RacquetBrands: {
    sportsTitle: string;
    category: string;
  };
  RacquetSelector: undefined;
  RacquetSelectorDetail: undefined;
  PlayerConnectScreen: undefined;
  GoEats: undefined;
  GoTravel: undefined;
  GoFit: undefined;
  CommunityDetails: {
    from?: string;
    title?: string;
    data?: any;
  };
  CommentScreen: {
    from?: string;
    title?: string;
    data?: any;
  };
  CoachProfile: undefined;
  FindCoach: undefined;
  FindClass: undefined;
  MyGroups: undefined;
  NewGroup: undefined;
  JoinGroups: undefined;
  JoinGroupDetails: undefined;
  AddMembers: undefined;
  CreateGroupMemberList: {
    data: string[];
  };
  Chat: undefined;
  PlayerConnectDateScreen: undefined;
};

const Drawer = createDrawerNavigator();

// Custom drawer content component
const CustomDrawerContent = ({navigation}: any) => {
  const theme = useThemeStore();
  const {user} = useAuthStore();
  const {coachProfile, setCoachProfile, drawerMenuItems} = useConfigStore();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [filterData, setFilterData] = useState<DrawerItemType[]>(drawerMenuItems || drawerMenuList);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const {t} = useTranslation();

  // Define drawer menu items with their sub-items
  const drawerItems = useMemo<DrawerItemType[]>(
    () => drawerMenuItems || drawerMenuList,
    [drawerMenuItems],
  );

  // Update filterData when drawerItems changes
  useEffect(() => {
    setFilterData(drawerItems);
  }, [drawerItems]);

  // Render a drawer menu item with its label
  const renderMenuItem = (item: DrawerItemType) => {
    if (!item.isVisible) {
      return null;
    }
    const handleItemPress = () => {
      // Close the drawer first
      navigation.closeDrawer();

      // Navigate to the corresponding screen based on the item ID
      if (item.id === 'notifications') {
        navigation.navigate('Notifications' as never);
      } else if (item.id === 'refer_friend') {
        navigation.navigate('ReferFriend' as never);
      } else if (item.id === 'my_matches') {
        navigation.navigate('MyMatches' as never);
      } else if (item.id === 'manage_community') {
        navigation.navigate('ManageCommunity' as never);
      } else if (item.id === 'orders') {
        navigation.navigate('Orders' as never);
      } else if (item.id === 'help') {
        navigation.navigate('Help' as never);
      } else if (item.id === 'rewards') {
        navigation.navigate('Rewards' as never);
      } else if (item.id === 'recycleBalls') {
        navigation.navigate('RecycleBalls' as never);
      } else if (item.screen) {
        navigation.navigate(item.screen as never);
      } else if (item.onPress) {
        item.onPress();
      }
    };

    return (
      <View key={item.id} style={styles(theme).menuItemContainer}>
        <TouchableOpacity style={styles(theme).menuItem} onPress={handleItemPress}>
          <Text style={styles(theme).menuItemText}>{t(item.label)}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderModalContent = () => {
    if (!coachProfile) {
      return (
        <WelcomeScreen
          onPress={() => {
            setCoachProfile(true);
          }}
        />
      );
    } else {
      return <CoachOptionsScreen />;
    }
  };

  const handleSearch = useCallback(
    (query: string) => {
      const filtered = query
        ? drawerItems.filter(item => t(item.label).toLowerCase().includes(query.toLowerCase()))
        : drawerItems;
      setFilterData(filtered);
    },
    [drawerItems],
  );
  const clearSearch = () => setSearchQuery('');

  return (
    <SafeAreaView style={[styles(theme).container]}>
      <View style={styles(theme).headerContainer}>
        <View style={styles(theme).userInfoContainer}>
          <CImage source={Images.profile1} resizeMode="cover" style={styles(theme).avatar} />
          <TouchableOpacity
            style={styles(theme).closeButton}
            onPress={() => navigation.closeDrawer()}>
            <Icon name="close1-1" size={36} color={theme.colors.crimsonRed} />
          </TouchableOpacity>
        </View>
        <View style={styles(theme).userTextContainer}>
          <Typography color={theme.colors.activeColor} variant="userName">
            {user?.name || 'Alex'}
          </Typography>
          <TouchableOpacity
            activeOpacity={0.8}
            style={styles(theme).emailContainer}
            onPress={() => navigation.navigate('EditProfile')}>
            <Typography variant="bodyMedium" color={theme.colors.activeColor}>
              {user?.email || '<EMAIL>'}
            </Typography>
            {/* <TouchableOpacity> */}
            <Icon name="editpen" size={20} color={theme.colors.orange} />
            {/* </TouchableOpacity> */}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles(theme).coach}
            onPress={() => {
              if (coachProfile) {
                setIsModalVisible(true);
              } else {
                setIsModalVisible(true);
              }
            }}>
            <Typography variant="tryNow" color={theme.colors.activeColor}>
              {t('drawer.assistantCoach')}
            </Typography>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles(theme).searchContainer}>
        <SearchInput
          placeholder={t('common.search')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          onClear={clearSearch}
          onSearch={handleSearch}
          debounceTime={300}
        />
      </View>

      <ScrollView contentContainerStyle={styles(theme).scrollContainer}>
        {filterData?.map(renderMenuItem)}
      </ScrollView>
      <CustomModal
        animationType="slide"
        variant="bottom"
        visible={isModalVisible}
        onClose={() => {
          setIsModalVisible(false);
        }}
        modalContainerStyle={{height: '100%'}}
        showCloseButtonRight={true}
        imageBg={true}>
        {renderModalContent()}
      </CustomModal>
    </SafeAreaView>
  );
};

// Drawer Navigator that uses the custom drawer content
const DrawerNavigator = () => {
  const theme = useThemeStore();

  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: theme.colors.black,
          width: '100%',
        },
        drawerType: 'front',
        drawerStatusBarAnimation: 'fade',
        overlayColor: theme.colors.semiTransparentBlack,
      }}
      drawerContent={props => <CustomDrawerContent {...props} />}>
      <Drawer.Screen name="MainTabs" component={TabNavigator} />
      <Drawer.Screen name="Settings" component={SettingsScreen} />
      <Drawer.Screen name="EditProfile" component={EditProfileScreen} />
      <Drawer.Screen name="welcome" component={WelcomeScreen} />
      <Drawer.Screen name="OrderReserveEquipment" component={OrderReserveEquipment} />
      <Drawer.Screen name="InvitePlayers" component={InvitePlayers} />
      <Drawer.Screen name="CoachOptions" component={CoachOptionsScreen} />
      <Drawer.Screen name="getCertified" component={GetCertified} />
      <Drawer.Screen
        name="MessageNotificationPreferences"
        component={MessageNotificationPreferences}
      />
      <Drawer.Screen name="PostAnAd" component={PostAnAd} />
      <Drawer.Screen name="ManageContent" component={ManageContent} />
      <Drawer.Screen name="EditCoachProfile" component={EditCoachProfile} />
      <Drawer.Screen name="ManageClasses" component={ManageClasses} />
      <Drawer.Screen name="ManageServiceRequest" component={ManageServiceRequest} />
      <Drawer.Screen name="Calendar" component={CalendarScreen} />
      <Drawer.Screen name="ManagePlayerAssets" component={ManagePlayerAssetsScreen} />
      <Drawer.Screen name="ManageCommunity" component={ManageCommunityScreen} />
      <Drawer.Screen name="Orders" component={OrdersScreen} />
      <Drawer.Screen name="Help" component={HelpScreen} />
      <Drawer.Screen name="Rewards" component={RewardsScreen} />
      <Drawer.Screen name="RecycleBalls" component={RecycleBallsScreen} />
      <Drawer.Screen name="ProfileQrScreen" component={ProfileQrScreen} />
      <Drawer.Screen name="DateScreen" component={DateScreen} />
      <Drawer.Screen name="EquipmentReservationScreen" component={EquipmentReservationScreen} />
      <Drawer.Screen name="RacquetCategory" component={RacquetCategoryScreen as any} />
      <Drawer.Screen name="RacquetBrands" component={RacquetsBrandsScreen as any} />
      <Drawer.Screen name="RacquetSelector" component={RacquetSelector} />
      <Drawer.Screen name="RacquetSelectorDetail" component={RacquetSelectorDetail} />
      <Drawer.Screen name="PlayerConnectScreen" component={PlayerConnectScreen} />
      <Drawer.Screen name="GoEats" component={GoEats} />
      <Drawer.Screen name="GoTravel" component={GoTravel} />
      <Drawer.Screen name="GoFit" component={GoFit} />
      <Drawer.Screen name="CommunityDetails" component={CommunityDetails} />
      <Drawer.Screen name="CommentScreen" component={CommentScreen} />
      <Drawer.Screen name="CoachProfile" component={CoachProfile} />
      <Drawer.Screen name="FindCoach" component={FindCoach} />
      <Drawer.Screen name="FindClass" component={FindClass} />
      <Drawer.Screen name="MyGroups" component={MyGroups} />
      <Drawer.Screen name="NewGroup" component={NewGroup} />
      <Drawer.Screen name="JoinGroups" component={JoinGroups} />
      <Drawer.Screen name="JoinGroupDetails" component={JoinGroupDetails} />
      <Drawer.Screen name="AddMembers" component={AddMembers} />
      <Drawer.Screen name="CreateGroupMemberList" component={CreateGroupMemberList as any} />
      <Drawer.Screen name="Chat" component={ChatScreen} />
      <Drawer.Screen name="PlayerConnectDateScreen" component={PlayerConnectDateScreen} />
    </Drawer.Navigator>
  );
};

// Styles
const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    headerContainer: {
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Gunmetal,
      backgroundColor: theme.colors.background,
      marginTop: 45,
    },
    userInfoContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },

    avatar: {
      width: 50,
      height: 50,
      borderRadius: 50,
    },
    userTextContainer: {
      marginTop: 15,
    },

    emailContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    coach: {
      marginTop: 20,
    },
    userEmail: {
      fontSize: 14,
      color: theme.colors.lime,
      marginRight: 8,
    },
    closeButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
    },
    searchContainer: {
      paddingHorizontal: 16,
      paddingTop: 12,
    },
    searchBar: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 10,
      backgroundColor: theme.colors.primary,
    },
    searchText: {
      color: theme.colors.white,
      marginLeft: 8,
      fontSize: 16,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingBottom: 30,
    },
    menuItemContainer: {
      marginBottom: 8,
    },
    menuItem: {
      paddingVertical: 12,
      paddingHorizontal: 16,
    },
    menuItemText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.lime,
    },
  });

export default DrawerNavigator;
