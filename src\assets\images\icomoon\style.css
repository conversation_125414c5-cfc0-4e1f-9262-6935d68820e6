@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?hv2olk');
  src:  url('fonts/icomoon.eot?hv2olk#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?hv2olk') format('truetype'),
    url('fonts/icomoon.woff?hv2olk') format('woff'),
    url('fonts/icomoon.svg?hv2olk#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-like:before {
  content: "\e900";
}
.icon-upload:before {
  content: "\e901";
}
.icon-comment:before {
  content: "\e902";
}
.icon-camera-1:before {
  content: "\30";
}
.icon-camera2:before {
  content: "\31";
}
.icon-mic:before {
  content: "\32";
}
.icon-add:before {
  content: "\33";
}
.icon-arrow:before {
  content: "\34";
}
.icon-babolat:before {
  content: "\35";
}
.icon-bag:before {
  content: "\36";
}
.icon-ball:before {
  content: "\37";
}
.icon-ball2:before {
  content: "\38";
}
.icon-Book-1:before {
  content: "\39";
}
.icon-cart:before {
  content: "\41";
}
.icon-chat:before {
  content: "\42";
}
.icon-checkcircle:before {
  content: "\43";
}
.icon-COMMUNITY:before {
  content: "\44";
}
.icon-create-group:before {
  content: "\45";
}
.icon-dropdown2:before {
  content: "\46";
}
.icon-dunlop:before {
  content: "\47";
}
.icon-editpen:before {
  content: "\48";
}
.icon-filter:before {
  content: "\49";
}
.icon-groupicon-1:before {
  content: "\4a";
}
.icon-head:before {
  content: "\4b";
}
.icon-join-group:before {
  content: "\4c";
}
.icon-kiosk:before {
  content: "\4d";
}
.icon-location-pin:before {
  content: "\4e";
}
.icon-location3:before {
  content: "\4f";
}
.icon-location4:before {
  content: "\50";
}
.icon-lock-1:before {
  content: "\51";
}
.icon-next:before {
  content: "\52";
}
.icon-notification:before {
  content: "\53";
}
.icon-padel:before {
  content: "\55";
}
.icon-pickleball:before {
  content: "\56";
}
.icon-platform-tennis:before {
  content: "\57";
}
.icon-racket3:before {
  content: "\58";
}
.icon-racket4:before {
  content: "\59";
}
.icon-RACQUETS-1:before {
  content: "\5a";
}
.icon-regripping:before {
  content: "\61";
}
.icon-restringing:before {
  content: "\62";
}
.icon-scoreboard:before {
  content: "\63";
}
.icon-technifibre:before {
  content: "\64";
}
.icon-tennis:before {
  content: "\65";
}
.icon-back:before {
  content: "\66";
}
.icon-check:before {
  content: "\67";
}
.icon-close:before {
  content: "\68";
}
.icon-close1-1:before {
  content: "\69";
}
.icon-dropdown:before {
  content: "\6a";
}
.icon-Email-1:before {
  content: "\6b";
}
.icon-icon-W:before {
  content: "\6c";
}
.icon-Left-chevron:before {
  content: "\6d";
}
.icon-line:before {
  content: "\6e";
}
.icon-location:before {
  content: "\6f";
}
.icon-plus:before {
  content: "\70";
}
.icon-racker3:before {
  content: "\71";
}
.icon-racket1:before {
  content: "\72";
}
.icon-racket2-1:before {
  content: "\73";
}
.icon-Right-chevron:before {
  content: "\74";
}
.icon-right:before {
  content: "\75";
}
.icon-search-1:before {
  content: "\76";
}
.icon-side-menu:before {
  content: "\77";
}
