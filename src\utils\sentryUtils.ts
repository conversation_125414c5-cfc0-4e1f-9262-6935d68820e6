import * as Sentry from '@sentry/react-native';
import {User} from '@/store/authStore';
import {Platform} from 'react-native';

// Define types for Sentry that might be missing
type SeverityLevel = 'fatal' | 'error' | 'warning' | 'log' | 'info' | 'debug';

// Define a type for the event
interface SentryEvent {
  [key: string]: unknown;
  request?: {
    data?: unknown;
    [key: string]: unknown;
  };
  extra?: Record<string, unknown>;
  contexts?: Record<string, unknown>;
}

// Use our own severity type since Sentry.Severity might not be available
type Severity = SeverityLevel;

/**
 * Sensitive fields that should be scrubbed before sending to Sentry
 */
export const SENSITIVE_FIELDS = [
  // Personal information
  'email',
  'phone',
  'phoneNumber',
  'password',
  'birthdate',
  'birthYear',
  'birthyear',
  'ssn',
  'socialSecurity',
  'address',
  'location',
  'latitude',
  'longitude',
  'gps',

  // Authentication
  'token',
  'accessToken',
  'refreshToken',
  'idToken',
  'jwt',
  'apiKey',
  'secret',

  // Payment information
  'creditCard',
  'cardNumber',
  'cvv',
  'cvc',
  'expiryDate',
  'cardholderName',
  'bankAccount',
  'routingNumber',
  'accountNumber',
  'paymentMethod',
];

/**
 * Initialize Sentry with proper configuration
 */
export const initSentry = () => {
  Sentry.init({
    dsn: 'https://<EMAIL>/****************',

    // Set environment based on __DEV__ flag
    environment: __DEV__ ? 'development' : 'production',

    // Enable debug mode in development
    debug: __DEV__,

    // Disable in development
    enabled: !__DEV__,

    // Configure data scrubbing
    beforeSend: event => {
      try {
        return scrubSensitiveData(event) as any;
      } catch (err) {
        console.warn('Error in beforeSend:', err);
        return event;
      }
    },

    // Set tracing sample rate (adjust as needed)
    tracesSampleRate: __DEV__ ? 1.0 : 0.2,

    // Enable auto session tracking
    enableAutoSessionTracking: true,

    // Set session timeout
    sessionTrackingIntervalMillis: 30000,

    // Add tags to all events
    initialScope: {
      tags: {
        platform: Platform.OS,
        platformVersion: String(Platform.Version),
      },
    },

    // Enable automatic breadcrumbs
    enableNativeCrashHandling: true,
    enableNativeNagger: true,

    // Add more context data to events (IP address, cookies, user, etc.)
    // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
    sendDefaultPii: true,
  });
};

/**
 * Set user context in Sentry
 * @param user User object from auth store
 */
export const setUserContext = (user: User | null) => {
  if (user) {
    // Set user context with minimal identifiable information
    Sentry.setUser({
      // Use a unique identifier that's available in the User object
      // If user.id doesn't exist, use another unique field or generate one
      id: user.name || 'unknown-user', // Using name as a fallback
      username: user.name,
      // Explicitly NOT including email or other PII
    });
  } else {
    // Clear user context when logged out
    Sentry.setUser(null);
  }
};

/**
 * Set navigation context in Sentry
 * @param routeName Current route name
 */
export const setNavigationContext = (routeName: string) => {
  Sentry.addBreadcrumb({
    category: 'navigation',
    message: `Navigated to ${routeName}`,
    level: 'info',
  });

  // Set the current screen as a tag
  Sentry.setTag('screen', routeName);
};

/**
 * Scrub sensitive data from Sentry events
 * @param event Sentry event
 * @returns Scrubbed event
 */
export const scrubSensitiveData = (event: unknown): unknown => {
  try {
    // Deep clone the event to avoid modifying the original
    const scrubbedEvent = JSON.parse(JSON.stringify(event)) as SentryEvent;

    // Scrub request data if present
    if (scrubbedEvent.request?.data) {
      scrubbedEvent.request.data = scrubObject(scrubbedEvent.request.data);
    }

    // Scrub extra data if present
    if (scrubbedEvent.extra) {
      scrubbedEvent.extra = scrubObject(scrubbedEvent.extra) as Record<string, unknown>;
    }

    // Scrub contexts if present
    if (scrubbedEvent.contexts) {
      scrubbedEvent.contexts = scrubObject(scrubbedEvent.contexts) as Record<string, unknown>;
    }

    return scrubbedEvent;
  } catch (error) {
    // If scrubbing fails, return the original event
    console.error('Error scrubbing Sentry event:', error);
    return event;
  }
};

/**
 * Recursively scrub sensitive data from an object
 * @param obj Object to scrub
 * @returns Scrubbed object
 */
export const scrubObject = (obj: unknown): unknown => {
  if (!obj) return obj;

  // If it's an array, scrub each item
  if (Array.isArray(obj)) {
    return obj.map(item => scrubObject(item));
  }

  // If it's not an object, return as is
  if (typeof obj !== 'object') {
    return obj;
  }

  // Create a new object to avoid modifying the original
  const scrubbedObj = {...(obj as Record<string, unknown>)};

  // Scrub each property
  for (const key in scrubbedObj) {
    // Check if the key is sensitive
    if (SENSITIVE_FIELDS.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
      scrubbedObj[key] = '[REDACTED]';
    }
    // Recursively scrub nested objects
    else if (typeof scrubbedObj[key] === 'object') {
      scrubbedObj[key] = scrubObject(scrubbedObj[key]);
    }
  }

  return scrubbedObj;
};

/**
 * Log an error to Sentry with additional context
 * @param error Error to log
 * @param context Additional context
 */
export const logError = (error: Error, context: Record<string, unknown> = {}) => {
  Sentry.withScope(scope => {
    // Add context to the scope
    for (const [key, value] of Object.entries(context)) {
      scope.setExtra(key, value);
    }

    // Capture the error
    Sentry.captureException(error);
  });
};

/**
 * Log a message to Sentry
 * @param message Message to log
 * @param level Log level
 * @param context Additional context
 */
export const logMessage = (
  message: string,
  level: SeverityLevel = 'info',
  context: Record<string, unknown> = {},
) => {
  Sentry.withScope(scope => {
    // Add context to the scope
    for (const [key, value] of Object.entries(context)) {
      scope.setExtra(key, value);
    }

    // Capture the message
    Sentry.captureMessage(message, level as any);
  });
};

/**
 * Create a performance transaction - DISABLED
 * This function is disabled because the Sentry version doesn't support transactions.
 * Instead, it adds a breadcrumb to track the operation.
 *
 * @param name Transaction name
 * @param operation Operation type
 * @returns undefined
 */
export const startTransaction = (name: string, operation: string) => {
  // Instead of starting a transaction, just add a breadcrumb
  Sentry.addBreadcrumb({
    category: 'performance',
    message: `Operation: ${name} (${operation})`,
    level: 'info' as any,
    data: {
      name,
      op: operation,
      timestamp: new Date().toISOString(),
    },
  });

  // Return a dummy object that has the expected methods but does nothing
  return {
    setData: () => {},
    setStatus: () => {},
    finish: () => {},
  };
};

/**
 * Add a breadcrumb to the current scope
 * @param message Breadcrumb message
 * @param category Breadcrumb category
 * @param level Breadcrumb level
 * @param data Additional data
 */
export const addBreadcrumb = (
  message: string,
  category: string,
  level: SeverityLevel = 'info',
  data: Record<string, unknown> = {},
) => {
  Sentry.addBreadcrumb({
    message,
    category,
    level: level as any,
    data: scrubObject(data) as Record<string, unknown>,
  });
};
