import React, {useRef} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '@navigation/index';
import {useForm, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {CInput, CButton, Icon, SafeAreaView} from '@components/index';
import {createStyles} from './styles';
import Typography from '@/components/Typography';
import {getGlobalStyles} from '@/utils';
import {useLogin} from '@/hooks/queries/useAuth';
import {useAuthStore} from '@/store';
import {useTranslationContext} from '@/context/TranslationContext';
import {toaster} from '@/utils/commonFunctions';

type LoginScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Login' | 'EmailSignup' | 'ForgotPassword' | 'Verification'
>;

// Form validation schema
const schema = yup.object({
  email: yup.string().email('Email format is invalid').required('Email is required'),
  password: yup
    .string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
});

// Form data type
type FormData = yup.InferType<typeof schema>;

const LoginScreen = ({route}: {route: {params: {email?: string}}}) => {
  const email = route?.params?.email ?? '';
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const theme = useThemeStore();
  const {t} = useTranslationContext();
  const styles = createStyles(theme);
  const {isApiStatus, loginStart} = useAuthStore();
  // Use React Query for login
  const loginMutation = useLogin();

  // Add refs for the input fields
  const emailInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);

  const globalStyles = getGlobalStyles({theme});

  // Initialize form with react-hook-form
  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      email: email || '',
      password: '',
    },
  });
  const handleBack = () => {
    navigation.goBack();
  };
  const handleSignup = () => {
    navigation.replace('EmailSignup');
  };

  const onSubmit = (data: FormData) => {
    // Use React Query mutation for login
    if (isApiStatus) {
      loginMutation.mutate(
        {
          email: data.email,
          password: data.password,
        },
        {
          onSuccess: response => {
            toaster('success', response.message, 'top');
            if (response.data?.user_not_verified === true) {
              navigation.navigate('Verification', {
                email: data.email,
              });
            }
          },
          onError: error => {
            toaster('error', error.message, 'top');
          },
        },
      );
    } else {
      navigation.navigate('Verification', {
        email: data.email,
      });
    }
  };

  const handleForgotPassword = () => {
    const emailStr = control._formValues.email;
    navigation.navigate('ForgotPassword', {email: emailStr});
  };

  return (
    <SafeAreaView includeTop style={[styles.container]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}>
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled">
          <View style={styles.headerContainer}>
            <TouchableOpacity
              onPress={handleBack}
              style={styles.backButton}
              activeOpacity={0.7}
              hitSlop={{top: 10, right: 10, bottom: 10, left: 10}}>
              <Icon name="Left-chevron" size={22} color={theme.colors.gray} />
            </TouchableOpacity>
            <Typography variant="subtitle" style={[globalStyles.title, {color: theme.colors.text}]}>
              {t('login.title')}
            </Typography>
          </View>

          <View style={styles.form}>
            <View style={{flex: 1}}>
              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="email"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      variant="dark"
                      label={t('login.emailAddress')}
                      showLabel={true}
                      placeholder={t('login.enterYourEmail')}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.email}
                      error={errors.email?.message}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={emailInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => passwordInputRef.current?.focus()}
                    />
                  )}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="password"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      variant="dark"
                      label={t('login.password')}
                      showLabel={true}
                      placeholder={t('login.enterYourPassword')}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.password}
                      error={errors.password?.message}
                      secureTextEntry
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={passwordInputRef}
                      returnKeyType="done"
                      onSubmitEditing={handleSubmit(onSubmit)}
                    />
                  )}
                />
              </View>

              <TouchableOpacity
                style={styles.forgotPasswordContainer}
                onPress={handleForgotPassword}
                activeOpacity={0.7}
                hitSlop={{top: 8, right: 8, bottom: 8, left: 8}}>
                <Text style={styles.forgotPasswordText}>{t('login.forgotYourPassword')}</Text>
              </TouchableOpacity>
            </View>

            <CButton
              title={t('login.submit')}
              variant="primary"
              onPress={handleSubmit(onSubmit)}
              loading={loginMutation.isPending}
              isDisabled={loginMutation.isPending}
            />

            <View style={styles.signupContainer}>
              <Text style={styles.signupText}>
                {t('login.dontHaveAccount')}{' '}
                <Text style={styles.signupLink} onPress={handleSignup}>
                  {t('login.signUp')}
                </Text>
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LoginScreen;
