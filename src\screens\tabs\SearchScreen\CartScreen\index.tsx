import {<PERSON><PERSON>, Icon, SafeAreaView, CButton, CustomModal} from '@/components';
import Typography from '@/components/Typography';
import {useThemeStore} from '@/store';
import {DrawerActions, NavigationProp, useNavigation} from '@react-navigation/native';
import React, {useState} from 'react';
import {ScrollView, TouchableOpacity, View} from 'react-native';
import {createStyles} from './styles';
import {Images} from '@/config';
import CartCard from '@/components/CartCard';
import useTranslation from '@/hooks/useTranslation';

const CartScreen = ({route}: {route: any}) => {
  console.log('route', route);
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp<any>>();
  const {t} = useTranslation();
  const [isModalVisible, setIsModalVisible] = useState(false);
  //   const {parkData} = route?.params;
  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const staticCartData = {
    name: 'Wilson Clash 100 V2 Britto Hearts',
    location: 'Location 1',
    courts: 1,
    image: Images.racquet1,
    hour: '1-hour',
    size: '4-1/2',
    string: 'Polyester, 52 lbs',
    price: 100,
  };

  return (
    <SafeAreaView includeBottom={false} style={styles.container}>
      <Header
        hideStatusBar={true}
        leftComponent={
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Icon name="back" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        }
        onLeftPress={openDrawer}
        backgroundColor="transparent"
        rightComponent={
          <TouchableOpacity activeOpacity={0.8} onPress={() => {}}>
            <Typography variant="subtitle" color={theme.colors.dimGray}>
              {t('CartScreen.Subscriber')}
            </Typography>
          </TouchableOpacity>
        }
      />
      <View style={styles.titleContainer}>
        <Typography variant="subtitle" color={theme.colors.white} style={styles.titleStyle}>
          {t('CartScreen.cart')}
        </Typography>
      </View>
      <ScrollView contentContainerStyle={styles.datePickerView}>
        <View>
          <View style={styles.descriptionContainer}>
            <Typography color={theme.colors.white} style={styles.descriptionText}>
              {t('CartScreen.description')}
            </Typography>
          </View>
          <View style={styles.divider} />
          <CartCard
            cartData={staticCartData}
            onSelect={d => {
              console.log('Selected park data:');
            }}
            style={{gap: 20}}
          />
        </View>

        <View>
          <View style={styles.totalContainer}>
            <Typography variant="subTitle4" color={theme.colors.white}>
              {t('CartScreen.total')}
            </Typography>
            <Typography variant="subTitle4" color={theme.colors.white}>
              {`$${staticCartData.price}`}
            </Typography>
          </View>
          <View style={styles.buttonContainer}>
            <CButton
              variant="secondary"
              title={t('CartScreen.continueShopping')}
              onPress={() => {
                navigation.navigate('Drawer', {
                  screen: 'MainTabs',
                  params: {
                    screen: 'SEARCH',
                  },
                });
              }}
            />
            <CButton
              title={t('CartScreen.reserve')}
              onPress={() => {
                setTimeout(() => {
                  setIsModalVisible(false);
                  navigation.navigate('Drawer', {
                    screen: 'MainTabs',
                    params: {
                      screen: 'SEARCH',
                    },
                  });
                }, 2000);
                setIsModalVisible(true);
              }}
            />
          </View>
        </View>
      </ScrollView>
      <CustomModal
        visible={isModalVisible}
        onClose={() => {
          setIsModalVisible(false);
        }}
        modalContainerStyle={{minWidth: '90%', minHeight: 350}}>
        <View style={{alignItems: 'center', justifyContent: 'center', gap: 30, padding: 16}}>
          <View
            style={{
              borderWidth: 7,
              borderColor: theme.colors.activeColor,
              borderRadius: 100,
              padding: 10,
            }}>
            <Icon name="check" size={80} color={theme.colors.activeColor} />
          </View>
          <Typography variant="subtitle" style={{textAlign: 'center', color: theme.colors.text}}>
            {t('CartScreen.youAreAllSetToPlayNow')}
          </Typography>
        </View>
      </CustomModal>
    </SafeAreaView>
  );
};

export default CartScreen;
