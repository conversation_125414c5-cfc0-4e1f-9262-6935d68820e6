import api, {handleApiError, tokenStorage} from './api';

// Types
export interface UpdateUserRequest {
  name?: string;
  is_chat_on?: boolean;
  birthdate?: string;
  user_type?: 'player' | 'coach' | 'both';
  is_notification_on?: boolean;
  fitness_level?: 'slow_and_steady' | 'workout_warrior' | 'can_keep_rally' | 'play_competitively';
}

export interface UserResponse {
  status: boolean;
  message: string;
  data?: {
    user?: {
      id: string;
      name: string;
      email: string;
      is_chat_on?: boolean;
      birthdate?: string;
      user_type?: string;
      is_notification_on?: boolean;
      fitness_level?: string;
    };
  };
}

// API paths
const API_PATHS = {
  updateUser: 'update-user',
  getUser: 'user',
};

/**
 * Update user profile
 */
export const updateUserProfile = async (data: UpdateUserRequest): Promise<UserResponse> => {
  try {
    console.log('Updating user profile with data:', data);

    // Get the access token
    const accessToken = tokenStorage.getString('accessToken');

    if (!accessToken) {
      throw new Error('No access token available. Please login again.');
    }

    // Make the API call with the authorization header
    const response = await api.post(API_PATHS.updateUser, data, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    });

    console.log('Update user profile response:', response);

    // Check if the response is successful
    if (response.status !== 200) {
      console.log('API returned non-200 status:', response.status);
      throw new Error(
        `API error: ${response.status} - ${response.data?.message || 'Failed to update profile'}`,
      );
    }

    // Check if the response data indicates failure
    if (response.data.status === false) {
      console.log('API returned status=false in data');
      throw new Error(response.data.message || 'Failed to update profile');
    }

    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

/**
 * Get user profile
 */
export const getUserProfile = async (): Promise<UserResponse> => {
  try {
    // Get the access token
    const accessToken = tokenStorage.getString('accessToken');

    if (!accessToken) {
      throw new Error('No access token available. Please login again.');
    }

    // Make the API call with the authorization header
    const response = await api.get(API_PATHS.getUser, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    // Check if the response is successful
    if (response.status !== 200) {
      throw new Error(
        `API error: ${response.status} - ${response.data?.message || 'Failed to get profile'}`,
      );
    }

    // Check if the response data indicates failure
    if (response.data.status === false) {
      throw new Error(response.data.message || 'Failed to get profile');
    }

    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

/**
 * Calculate age from birthdate
 */
export const calculateAge = (birthdate: string): number => {
  const today = new Date();
  const birthDate = new Date(birthdate);
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();

  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  return age;
};

/**
 * Format birthdate from age
 */
export const formatBirthdate = (age: number): string => {
  const today = new Date();
  const birthYear = today.getFullYear() - age;
  return `${birthYear}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
};
