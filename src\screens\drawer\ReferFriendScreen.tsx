import React, {useState, useMemo} from 'react';
import {View, StyleSheet, TouchableOpacity, Dimensions, FlatList} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {useNavigation} from '@react-navigation/native';
import {CButton, CInput, Header, Icon, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import {OfferBanner} from '@/components/common/OfferBanner';
import SearchInput from '@/components/SearchInput';
import {useForm, Controller} from 'react-hook-form';
import * as yup from 'yup';
import {yupResolver} from '@hookform/resolvers/yup';
import PlayerCard from '@/components/PlayerCard';
import useTranslation from '@/hooks/useTranslation';

type Player = {
  id: string;
  name: string;
  rating: string;
  location: string;
  image: string;
  color?: string;
  isPremium?: boolean;
};

// Define the validation schema
const schema = yup.object().shape({
  name: yup.string().required('Name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup
    .string()
    .matches(/^\d{10,15}$/, 'Phone must be 10-15 digits')
    .required('Phone is required'),
  rating: yup
    .string()
    .required('Rating is required')
    .test('is-num', 'Rating must be a number', value => !value || !isNaN(Number(value)))
    .test('min', 'Minimum rating is 1', value => Number(value) >= 1)
    .test('max', 'Maximum rating is 5', value => Number(value) <= 5),
});

const ReferFriendScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [chatWidgetEnabled, setChatWidgetEnabled] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const {t} = useTranslation();

  const players: Player[] = [
    {
      id: '1',
      name: 'John Doe',
      rating: '3.5',
      location: 'Fort Greene',
      image: 'https://randomuser.me/api/portraits/men/1.jpg',
    },
    {
      id: '2',
      name: 'Jane Doe',
      rating: '3.5',
      location: 'Lincoln Terrace',
      image: 'https://randomuser.me/api/portraits/women/2.jpg',
    },
    {
      id: '3',
      name: 'Babolat',
      rating: '3.5',
      location: 'McCarren Park',
      image: 'https://randomuser.me/api/portraits/men/3.jpg',
      color: theme.colors.orange,
      isPremium: true,
    },
    {
      id: '4',
      name: 'John Doe',
      rating: '3.5',
      location: 'Fort Greene',
      image: 'https://randomuser.me/api/portraits/men/1.jpg',
    },
    {
      id: '5',
      name: 'Jane Doe',
      rating: '3.5',
      location: 'Lincoln Terrace',
      image: 'https://randomuser.me/api/portraits/women/2.jpg',
      isPremium: true,
    },
    {
      id: '6',
      name: 'Babolat',
      rating: '3.5',
      location: 'McCarren Park',
      image: 'https://randomuser.me/api/portraits/men/3.jpg',
      color: theme.colors.orange,
    },
    {
      id: '7',
      name: 'John Doe',
      rating: '3.5',
      location: 'Fort Greene',
      image: 'https://randomuser.me/api/portraits/men/1.jpg',
    },
    {
      id: '8',
      name: 'Jane Doe',
      rating: '3.5',
      location: 'Lincoln Terrace',
      image: 'https://randomuser.me/api/portraits/women/2.jpg',
    },
    {
      id: '9',
      name: 'Babolat',
      rating: '3.5',
      location: 'McCarren Park',
      image: 'https://randomuser.me/api/portraits/men/3.jpg',
      color: theme.colors.orange,
    },
  ];

  const filteredPlayers = useMemo(() => {
    if (!searchQuery.trim()) {
      return players;
    }

    const query = searchQuery.toLowerCase().trim();
    return players.filter(
      player =>
        player.name.toLowerCase().includes(query) ||
        player.location.toLowerCase().includes(query) ||
        player.rating.includes(query),
    );
  }, [searchQuery, players]);

  // Setup react-hook-form
  const {
    control,
    handleSubmit,
    formState: {errors},
    reset,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      rating: '',
    },
  });

  // Handle form submission
  const onSubmit = (data: any) => {
    // You can handle the invite logic here
    // For now, just reset the form
    reset();
  };

  const renderListHeader = () => {
    return (
      <>
        <Controller
          control={control}
          name="name"
          render={({field: {onChange, value}}) => (
            <CInput
              variant="dark"
              placeholder={t('common.enterName')}
              value={value}
              onChangeText={onChange}
              inputStyle={styles(theme).input}
              error={errors.name?.message}
            />
          )}
        />

        <Controller
          control={control}
          name="email"
          render={({field: {onChange, value}}) => (
            <CInput
              variant="dark"
              placeholder={t('common.enterEmail')}
              value={value}
              onChangeText={onChange}
              inputStyle={styles(theme).input}
              error={errors.email?.message}
            />
          )}
        />

        <Controller
          control={control}
          name="phone"
          render={({field: {onChange, value}}) => (
            <CInput
              variant="dark"
              placeholder={t('common.enterPhone')}
              value={value}
              onChangeText={onChange}
              inputStyle={styles(theme).input}
              error={errors.phone?.message}
              keyboardType="phone-pad"
            />
          )}
        />

        <Controller
          control={control}
          name="rating"
          render={({field: {onChange, value}}) => (
            <CInput
              variant="dark"
              placeholder={t('common.enterAtpRating')}
              value={value}
              onChangeText={text => {
                if (/^\d*$/.test(text) || text === '') {
                  onChange(text);
                }
              }}
              inputStyle={styles(theme).input}
              error={errors.rating?.message}
              keyboardType="numeric"
            />
          )}
        />

        <CButton
          variant="pill"
          title={t('common.invite')}
          onPress={handleSubmit(onSubmit)}
          containerStyle={styles(theme).button}
          textStyle={{color: theme.colors.background}}
        />

        <View style={styles(theme).uploadContainer}>
          <Typography variant="label" style={styles(theme).uploadLabel}>
            {t('common.uploadContacts')}
          </Typography>
          <CButton
            variant="pill"
            title={t('common.upload')}
            onPress={() => {}}
            containerStyle={styles(theme).uploadButton}
            textStyle={{color: theme.colors.background}}
          />
        </View>
      </>
    );
  };

  const renderItem = ({item}: {item: Player}) => (
    <PlayerCard
      key={item.id}
      playerData={item}
      // onSelect={() => handlePlayerSelect(item)}
      // isSelected={selectedPlayers.some(p => p.id === item.id)}
    />
  );

  return (
    <SafeAreaView style={styles(theme).container}>
      {/* Header with back button */}

      <Header
        leftComponent={
          <TouchableOpacity onPress={() => navigation.goBack()} activeOpacity={0.7}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        }
        rightIcons={[
          {name: 'notification', size: 24, color: theme.colors.activeColor},
          {name: 'chat', size: 24, badge: 14, color: theme.colors.activeColor},
        ]}
        pageTitle={t('drawerReferFriend.referFriend')}
        backgroundColor={'transparent'}
        transparent={false}
        showBack={false}
      />

      <View style={styles(theme).searchContainer}>
        <SearchInput
          placeholder={t('drawerReferFriend.searchReferrals')}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>
      <FlatList
        contentContainerStyle={styles(theme).contentContainer}
        ListHeaderComponent={renderListHeader}
        data={filteredPlayers}
        renderItem={renderItem}
        keyExtractor={(item, index) => index.toString()}
      />

      {/* Footer */}
      <View style={styles(theme).footer}>
        <OfferBanner text={t('drawerReferFriend.inviteFriendsGet10Off')} />
      </View>
    </SafeAreaView>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },

    contentContainer: {
      flexGrow: 1,
      paddingHorizontal: 16,
      paddingBottom: 30,
      gap: 16,
    },

    searchContainer: {
      paddingHorizontal: 16,
    },

    input: {
      marginTop: 16,
      borderRadius: 40,
    },

    button: {
      paddingVertical: 12,
      marginTop: 16,
      backgroundColor: theme.colors.activeColor,
    },

    uploadButton: {
      backgroundColor: theme.colors.activeColor,
      paddingVertical: 12,
      minWidth: Dimensions.get('window').width * 0.35,
    },

    uploadContainer: {
      marginTop: 32,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },

    uploadLabel: {
      color: theme.colors.text,
    },

    footer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
    },
  });

export default ReferFriendScreen;
