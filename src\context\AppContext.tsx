import React, {createContext, useContext, ReactNode} from 'react';
import {useMMKVStorage} from '@/services/storage';

// Types from previous Redux store
type BookingStep =
  | 'search_parks'
  | 'select_date_time'
  | 'purchase_equipments'
  | 'cart_view'
  | 'create_profile'
  | 'signup'
  | 'advertisement'
  | 'subscription'
  | '';

interface User {
  name: string;
  email: string;
}

// Theme types
interface ThemeState {
  fontSize: {
    small: number;
    medium: number;
    font14: number;
    large: number;
    default: 'small' | 'medium' | 'large' | 'xlarge';
    xlarge: number;
    xxlarge: number;
    xsmall: number;
  };
  colors: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
    activeColor: string;
    white: string;
    orange: string;
    error: string;
    black: string;
    gray: string;
    midGrey: string;
    silver: string;
    spanishGray: string;
    davyGrey: string;
    darkGray: string;
    divider: string;
    greyBackground: string;
    actionBackground: string;
    highlightColor: string;
    red: string;
    crimsonRed: string;
    inputLabel: string;
    placeholder: string;
    link: string;
    dotColor: string;
    mediumGray: string;
    coralRed: string;
    bottomSheetBackground: string;
    graniteGrey: string;
    Gainsboro: string;
    oceanBlue: string;
    Gunmetal: string;
    charcoal: string;
    offWhite: string;
    darkRed: string;
    jetBlack: string;
    lime: string;
    transparentWhite: string;
    transparentWhite1: string;
    semiTransparentWhite: string;
    semiTransparentBlack: string;
    blackBg: string;
    white1: string;
    white2: string;
    bgGrey: string;
    offWhite1: string;
    veryLightGray: string;
    shadowColor: string;
    likeRed: string;
    richSkyBlue: string;
    pinkishPurple: string;
    tangerine: string;
    TranslucentWhite: string;
    notificationBg: string;
    dimGray: string;
  };
}

// New interface for navigation options
export interface NavigationOption {
  id: number;
  icon: string;
  iconType: string;
  title: string;
  description: string;
  iconBackgroundColor: string;
  color?: string;
  navigate: string; // Store the navigation action as string identifier instead of function
}

// Config types
interface ConfigState {
  isFirstLaunch: boolean;
  language: 'en' | 'fr' | 'es' | 'de';
  notifications: boolean;
  permissions: {
    location: boolean;
    bluetooth: boolean;
    notification: boolean;
    termsAndConditions: boolean;
  };
  bookingStep: BookingStep;
  bookingStepIndex: number;
  bookingData: Record<string, any>;
  profileSetup: boolean;
  profileStep: string;
  communityOptions?: NavigationOption[]; // Add community options to store navigation order
}

// Auth types
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

// AppState combines all previous Redux state
interface AppState {
  theme: ThemeState;
  config: ConfigState;
  auth: AuthState;
}

// Default values
const defaultTheme: ThemeState = {
  fontSize: {
    xsmall: 10,
    small: 12,
    font14: 14,
    medium: 16,
    large: 20,
    default: 'medium',
    xlarge: 22,
    xxlarge: 24,
  },
  colors: {
    primary: '#288ECE',
    secondary: '#707070',
    background: '#2B2B2B',
    text: '#FFFFFF',
    activeColor: '#DFFF4F',
    white: '#FFFFFF',
    orange: '#FFAB4C',
    error: '#FF6B6B',
    black: '#000000',
    shadowColor: '#0000004D',
    gray: '#818181',
    midGrey: '#888888',
    graniteGrey: '#4A4A4A',
    silver: '#767577',
    spanishGray: '#939393',
    davyGrey: '#343A40',
    darkGray: '#333333',
    divider: '#707070',
    greyBackground: '#666766',
    actionBackground: '#3B3A3A',
    red: '#FF3B30',
    crimsonRed: '#E61A3B',
    inputLabel: '#DFFF4F',
    highlightColor: '#CCFF00',
    placeholder: '#888',
    link: '#3498DB',
    dotColor: '#e0e0e0',
    mediumGray: '#9e9e9e',
    coralRed: '#FF6B6B',
    bottomSheetBackground: '#939393',
    Gainsboro: '#ddd',
    oceanBlue: '#0077C2',
    Gunmetal: '#333542',
    charcoal: '#121212',
    offWhite: '#ADADAD',
    darkRed: '#d32f2f',
    jetBlack: '#2A2A2A',
    lime: '#D6FF00',
    transparentWhite: '#FFFFFFB2',
    transparentWhite1: '#FFFFFFcc',
    semiTransparentWhite: '#FFFFFF33',
    semiTransparentBlack: '#000000B3',
    blackBg: '#111',
    white1: '#FFFFFF1A',
    white2: '#FFFFFF4D',
    bgGrey: '#4E4E4E',
    offWhite1: '#ADADAD',
    veryLightGray: '#E0E1E0',
    likeRed: '#FF003D',
    richSkyBlue: '#298FCE',
    pinkishPurple: '#DFFF4F99',
    tangerine: '#FFAB4C99',
    TranslucentWhite: '#FFFFFF0D',
    notificationBg: '#1A1A1A',
    dimGray: '#616161',
  },
};

const defaultConfig: ConfigState = {
  isFirstLaunch: true,
  language: 'en',
  notifications: true,
  permissions: {
    location: false,
    bluetooth: false,
    notification: false,
    termsAndConditions: false,
  },
  bookingStep: 'search_parks',
  bookingStepIndex: 0,
  bookingData: {},
  profileSetup: false,
  profileStep: 'create_profile',
};

const defaultAuth: AuthState = {
  isAuthenticated: false,
  user: null,
  loading: false,
  error: null,
};

// Create context with default values
const AppContext = createContext<{
  state: AppState;
  setTheme: (theme: ThemeState | ((prev: ThemeState) => ThemeState)) => void;
  setConfig: (config: ConfigState | ((prev: ConfigState) => ConfigState)) => void;
  setAuth: (auth: AuthState | ((prev: AuthState) => AuthState)) => void;
  login: (user: User) => void;
  logout: () => void;
}>({
  state: {
    theme: defaultTheme,
    config: defaultConfig,
    auth: defaultAuth,
  },
  setTheme: () => {},
  setConfig: () => {},
  setAuth: () => {},
  login: () => {},
  logout: () => {},
});

// Provider component
export const AppProvider = ({children}: {children: ReactNode}) => {
  // Use MMKV storage for each slice
  const [theme, setTheme] = useMMKVStorage<ThemeState>('app-theme', defaultTheme);
  const [config, setConfig] = useMMKVStorage<ConfigState>('app-config', defaultConfig);
  const [auth, setAuth] = useMMKVStorage<AuthState>('app-auth', defaultAuth);
  console.log('config ===>', config);
  // Common auth actions
  const login = (user: User) => {
    setAuth({
      isAuthenticated: true,
      user,
      loading: false,
      error: null,
    });
  };

  const logout = () => {
    setAuth({
      isAuthenticated: false,
      user: null,
      loading: false,
      error: null,
    });
  };

  // Combined state
  const state = {
    theme,
    config,
    auth,
  };

  return (
    <AppContext.Provider value={{state, setTheme, setConfig, setAuth, login, logout}}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook for using context
export const useAppContext = () => useContext(AppContext);

// Utility hooks for specific slices
export const useTheme = () => {
  const {state} = useAppContext();
  return state.theme;
};

export const useConfig = () => {
  const {state, setConfig} = useAppContext();
  // Helper functions for config actions
  const setFirstLaunch = (isFirstLaunch: boolean) => {
    setConfig(prev => ({...prev, isFirstLaunch}));
  };

  const setLanguage = (language: 'en' | 'fr' | 'es' | 'de') => {
    setConfig(prev => ({...prev, language}));
  };

  const toggleNotifications = () => {
    setConfig(prev => ({...prev, notifications: !prev.notifications}));
  };

  const setNotifications = (notifications: boolean) => {
    setConfig(prev => ({...prev, notifications}));
  };

  const setBookingStep = (bookingStep: BookingStep) => {
    setConfig(prev => {
      // Define the booking flow steps
      const BOOKING_FLOW: BookingStep[] = [
        'search_parks',
        'select_date_time',
        'purchase_equipments',
        'cart_view',
        'create_profile',
        'signup',
        'advertisement',
        'subscription',
      ];

      // Update the step index based on the step name
      const stepIndex = BOOKING_FLOW.findIndex(step => step === bookingStep);
      return {
        ...prev,
        bookingStep,
        bookingStepIndex: stepIndex !== -1 ? stepIndex : prev.bookingStepIndex,
      };
    });
  };

  const nextBookingStep = (updated?: any, type?: string) => {
    setConfig(prev => {
      console.log('prev ===>', prev);
      const BOOKING_FLOW: BookingStep[] = [
        'search_parks',
        'select_date_time',
        'purchase_equipments',
        'cart_view',
        'create_profile',
        'signup',
        'advertisement',
        'subscription',
      ];

      const nextIndex = prev.bookingStepIndex + 1;
      if (nextIndex < BOOKING_FLOW.length) {
        if (updated && type === 'updateBookingData') {
          return {
            ...prev,
            bookingStepIndex: nextIndex,
            bookingStep: BOOKING_FLOW[nextIndex],
            bookingData: updated.bookingData,
          };
        }
        return {
          ...prev,
          bookingStepIndex: nextIndex,
          bookingStep: BOOKING_FLOW[nextIndex],
        };
      }
      return prev;
    });
  };

  const previousBookingStep = () => {
    setConfig(prev => {
      const BOOKING_FLOW: BookingStep[] = [
        'search_parks',
        'select_date_time',
        'purchase_equipments',
        'cart_view',
        'create_profile',
        'signup',
        'advertisement',
        'subscription',
      ];

      const prevIndex = Number(prev.bookingStepIndex || 2) - 1;
      if (prevIndex >= 0) {
        return {
          ...prev,
          bookingStepIndex: prevIndex,
          bookingStep: BOOKING_FLOW[prevIndex],
        };
      }
      return prev;
    });
  };

  const setPermissions = (permissions: {
    location: boolean;
    bluetooth: boolean;
    notification: boolean;
    termsAndConditions: boolean;
  }) => {
    setConfig(prev => ({...prev, permissions}));
  };

  const setProfileSetup = (profileSetup: boolean) => {
    setConfig(prev => ({...prev, profileSetup}));
  };

  const setProfileStep = (profileStep: string) => {
    setConfig(prev => ({...prev, profileStep}));
  };

  const updateBookingData = (data: object, type?: string) => {
    if (type === 'next') {
      setConfig(prev => {
        const newBookingData = {
          ...prev.bookingData,
          ...data,
        };
        const updated = {
          ...prev,
          bookingData: newBookingData,
        };

        // Call nextBookingStep after the state is updated
        setTimeout(() => {
          nextBookingStep(updated, 'updateBookingData');
        }, 0);

        return updated;
      });
    } else {
      setConfig(prev => {
        const newBookingData = {
          ...prev.bookingData,
          ...data,
        };
        return {
          ...prev,
          bookingData: newBookingData,
        };
      });
    }
  };

  const resetBooking = () => {
    console.log('resetBooking ===>');

    setConfig(prev => ({
      ...prev,
      bookingStep: 'search_parks',
      bookingStepIndex: 0,
      bookingData: {},
    }));
  };

  // Community options management
  const setCommunityOptions = (options: NavigationOption[]) => {
    setConfig(prev => ({...prev, communityOptions: options}));
  };

  const getCommunityOptions = () => {
    return state.config.communityOptions;
  };

  return {
    ...state.config,
    setFirstLaunch,
    setLanguage,
    toggleNotifications,
    setNotifications,
    setBookingStep,
    nextBookingStep,
    previousBookingStep,
    setPermissions,
    updateBookingData,
    resetBooking,
    setProfileSetup,
    setProfileStep,
    setCommunityOptions,
    getCommunityOptions,
  };
};

export const useAuth = () => {
  const {state, login, logout, setAuth} = useAppContext();

  // Helper functions for auth actions
  const loginStart = () => {
    setAuth(prev => ({...prev, loading: true, error: null}));
  };

  const loginFailure = (error: string) => {
    setAuth(prev => ({...prev, loading: false, error}));
  };

  return {
    ...state.auth,
    login,
    logout,
    loginStart,
    loginFailure,
  };
};
