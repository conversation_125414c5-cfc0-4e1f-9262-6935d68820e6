# React Query (TanStack Query) Integration Guide

This guide explains how to use React Query (TanStack Query) in the GoRaqt app for efficient data fetching and state management.

## Overview

React Query is a powerful data fetching and caching library that simplifies server state management in React applications. It provides hooks for fetching, caching, synchronizing, and updating server state.

## Project Setup

The React Query setup in this project includes:

1. **QueryClientProvider**: Wraps the application to provide the query client context
2. **API Service**: Uses Axios for making HTTP requests with interceptors for authentication and error handling
3. **Query Hooks**: Custom hooks for different resources (users, posts, etc.)
4. **Prefetching Utilities**: Functions to prefetch data for better user experience

## Basic Usage

### Fetching Data

To fetch data from an API, use the `useQuery` hook:

```tsx
import { useUsers } from '@/hooks/queries/useUsers';

function UserComponent() {
  const { data, isLoading, isError, error } = useUsers();

  if (isLoading) return <LoadingIndicator />;
  if (isError) return <ErrorMessage error={error} />;

  return (
    <View>
      {data.map(user => (
        <UserItem key={user.id} user={user} />
      ))}
    </View>
  );
}
```

### Creating, Updating, and Deleting Data

For mutations (creating, updating, or deleting data), use the `useMutation` hook:

```tsx
import { useCreateUser } from '@/hooks/queries/useUsers';

function CreateUserForm() {
  const createUserMutation = useCreateUser();

  const handleSubmit = (userData) => {
    createUserMutation.mutate(userData, {
      onSuccess: () => {
        // Handle success
      },
      onError: (error) => {
        // Handle error
      }
    });
  };

  return (
    <Form onSubmit={handleSubmit}>
      {/* Form fields */}
      <Button 
        title="Create User" 
        onPress={handleSubmit} 
        loading={createUserMutation.isPending} 
      />
    </Form>
  );
}
```

## Advanced Features

### Query Keys

Query keys are used to identify and manage queries. They are defined in each query hook file:

```tsx
// Example from useUsers.ts
export const userKeys = {
  all: ['users'] as const,
  lists: () => [...userKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...userKeys.lists(), { filters }] as const,
  details: () => [...userKeys.all, 'detail'] as const,
  detail: (id: string) => [...userKeys.details(), id] as const,
};
```

### Prefetching Data

Prefetching data can improve the user experience by loading data before it's needed:

```tsx
import { prefetchUser } from '@/utils/prefetchQueries';

// Prefetch user data when a list item is pressed
const handleUserPress = (userId) => {
  prefetchUser(userId);
  navigation.navigate('UserDetails', { userId });
};
```

### Infinite Queries

For pagination or infinite scrolling, use `useInfiniteQuery`:

```tsx
import { useInfinitePosts } from '@/hooks/queries/usePosts';

function PostsList() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfinitePosts();

  return (
    <FlatList
      data={data?.pages.flatMap(page => page.data) || []}
      renderItem={({ item }) => <PostItem post={item} />}
      onEndReached={() => hasNextPage && fetchNextPage()}
      ListFooterComponent={isFetchingNextPage ? <LoadingIndicator /> : null}
    />
  );
}
```

## Integration with Zustand

React Query works well alongside Zustand for managing client state:

- **React Query**: Use for server state (data fetching, caching, synchronizing with server)
- **Zustand**: Use for client state (UI state, user preferences, authentication state)

Example of using both together:

```tsx
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/store/authStore';

function ProfileScreen() {
  // Get user ID from Zustand store
  const { user } = useAuthStore();
  
  // Use React Query to fetch profile data
  const { data: profile } = useQuery({
    queryKey: ['profile', user?.id],
    queryFn: () => fetchProfileData(user?.id),
    enabled: !!user?.id,
  });

  return (
    <View>
      {/* Display profile data */}
    </View>
  );
}
```

## Best Practices

1. **Organize query hooks by resource**: Keep related queries in the same file (e.g., `useUsers.ts`, `usePosts.ts`)
2. **Use query keys consistently**: Follow a consistent pattern for query keys
3. **Handle loading and error states**: Always account for loading, error, and empty states
4. **Prefetch data when possible**: Improve UX by prefetching data that will likely be needed
5. **Use optimistic updates**: Update the UI immediately before the server confirms the change
6. **Invalidate queries appropriately**: Invalidate related queries when data changes

## Debugging

For debugging React Query:

1. **React Query Devtools**: Available in development mode on web platforms
2. **Logging**: Use `console.log` to debug query states and data
3. **Query Options**: Adjust `staleTime`, `gcTime`, and other options as needed

## Additional Resources

- [TanStack Query Documentation](https://tanstack.com/query/latest/docs/react/overview)
- [React Query Patterns](https://tkdodo.eu/blog/practical-react-query)
- [React Query with TypeScript](https://tanstack.com/query/latest/docs/react/typescript)
