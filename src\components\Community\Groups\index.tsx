import React from 'react';
import {View, ScrollView, TouchableOpacity, FlatList} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {createStyles} from './styles';
import Typography from '@/components/Typography';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import GroupCard from '@/components/GroupCard/index';
type NavigationProp = NativeStackNavigationProp<CommunityStackParamList>;
import {Icon} from '@/components';
import {Shadow} from 'react-native-shadow-2';
import useTranslation from '@/hooks/useTranslation';

const Groups: React.FC = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();
  // Combined data for FlatList with section headers
  const groupData = [
    {type: 'section', title: 'Favorite Groups', id: 'section-fav'},
    {id: '1', name: 'Sunday Funday - Fort Greene', members: 7, highlighted: false, type: 'group'},
    {id: '2', name: 'Troopers - SOHO', members: 7, highlighted: false, type: 'group'},
    {id: '3', name: 'Diadem at Central Park', members: 34, highlighted: true, type: 'group'},
    {type: 'section', title: 'All Groups', id: 'section-all'},
    {id: '4', name: 'Sunday Clinic - Fort Greene', members: 7, highlighted: false, type: 'group'},
  ];

  // Split groupData into sections for rendering with separate backgrounds
  const favoriteGroupItems = groupData.filter(
    item => item.type === 'group' && ['1', '2', '3'].includes(item.id),
  );
  const allGroupItems = groupData.filter(item => item.type === 'group' && item.id === '4');

  const renderItem = ({item}: any) => {
    if (item.type === 'section') {
      return (
        <Typography variant="sectionTitle" style={styles.sectionHeader}>
          {item.title}
        </Typography>
      );
    }
    return (
      <GroupCard
        name={item.name}
        members={item.members}
        highlighted={item.highlighted}
        onPress={() => navigation.navigate('Chat')}
      />
    );
  };

  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        {/* Restored Card Row */}
        <View style={styles.cardRow}>
          {[
            {
              id: 'create',
              name: t('groupsScreen.createGroup'),
              icon: 'create-group',
              onPress: () => navigation.navigate('NewGroup'),
            },
            {
              id: 'my',
              name: t('groupsScreen.myGroups'),
              icon: 'groupicon-1',
              onPress: () => navigation.navigate('MyGroups'),
            },
            {
              id: 'join',
              name: t('groupsScreen.joinGroup'),
              icon: 'join-group',
              onPress: () => navigation.navigate('JoinGroups'),
            },
            {
              id: 'scoreboard',
              name: t('groupsScreen.scoreBoard'),
              icon: 'scoreboard',
              onPress: () => {},
            },
          ].map(item => (
            <TouchableOpacity
              key={item.id}
              style={styles.cardItem}
              onPress={item.onPress}
              activeOpacity={0.8}>
              <Shadow
                distance={1}
                offset={[1, 0.5]}
                startColor="rgba(0,0,0,0.25)"
                containerStyle={styles.shadowWrap}>
                <View style={styles.cardIconContainer}>
                  <Icon name={item.icon} size={30} color={theme.colors.white} />
                </View>
              </Shadow>
              <Typography variant="body" style={styles.cardLabel} align="center">
                {item.name}
              </Typography>
            </TouchableOpacity>
          ))}
        </View>
        {/* Favorite Groups Section */}
        <Typography variant="groupType" style={styles.sectionHeader}>
          {t('groupsScreen.favoriteGroups')}
        </Typography>
        <View style={styles.groupListContainer}>
          <FlatList
            data={favoriteGroupItems}
            renderItem={renderItem}
            keyExtractor={item => item.id}
            scrollEnabled={false}
          />
        </View>
        <Typography variant="groupType" style={styles.sectionHeader1}>
          {t('groupsScreen.allGroups')}
        </Typography>
        <View style={styles.groupListContainer}>
          <FlatList
            data={allGroupItems}
            renderItem={renderItem}
            keyExtractor={item => item.id}
            scrollEnabled={false}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default Groups;
