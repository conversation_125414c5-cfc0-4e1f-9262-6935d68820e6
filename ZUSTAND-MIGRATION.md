# Migrating from AppContext to Zustand with MMKV

This document explains how to migrate from the current AppContext implementation to a more direct Zustand with MMKV approach for state management.

## What's Included

1. **Migration Script**: `update-appcontext-to-zustand.js` - A script to automatically update imports and usages of AppContext to Zustand stores
2. **Zustand Store Files**:
   - `src/store/themeStore.ts` - Theme state management
   - `src/store/configStore.ts` - Configuration state management
   - `src/store/authStore.ts` - Authentication state management
   - `src/store/index.ts` - Combined store exports

## Benefits of Zustand over Context API

- **Performance**: Zustand has better performance characteristics than React Context
- **Simplicity**: More straightforward API with less boilerplate
- **Selective Updates**: Components only re-render when their specific subscribed state changes
- **Middleware Support**: Built-in support for middleware like persistence, immer, etc.
- **DevTools Integration**: Better debugging with Redux DevTools

## How to Run the Migration

1. Make sure you have a backup of your code or a clean git state
2. Run the migration script:

```bash
node update-appcontext-to-zustand.js
```

3. The script will:
   - Find all files using AppContext
   - Replace imports from AppContext with imports from the appropriate Zustand stores
   - Update usage patterns to match Zustand's API

4. After running the script, you may need to manually fix some complex usages that the script couldn't handle automatically.

## How to Use the New Zustand Stores

### Theme Store

```typescript
import { useThemeStore } from '@/store/themeStore';

// In your component
const MyComponent = () => {
  const theme = useThemeStore();
  
  // Access theme properties
  const { colors, fontSize } = theme;
  
  // Update theme
  const updateTheme = () => {
    theme.setTheme(currentTheme => ({
      ...currentTheme,
      colors: {
        ...currentTheme.colors,
        primary: '#newColor'
      }
    }));
  };
  
  return (
    <View style={{ backgroundColor: colors.background }}>
      <Text style={{ color: colors.text, fontSize: fontSize.medium }}>
        Hello World
      </Text>
      <Button onPress={updateTheme} title="Update Theme" />
    </View>
  );
};
```

### Config Store

```typescript
import { useConfigStore } from '@/store/configStore';

// In your component
const MyComponent = () => {
  const config = useConfigStore();
  
  // Access config properties
  const { language, bookingStep, permissions } = config;
  
  // Use config actions
  const handleNextStep = () => {
    config.nextBookingStep();
  };
  
  const updatePermissions = () => {
    config.setPermissions({
      ...permissions,
      location: true
    });
  };
  
  return (
    <View>
      <Text>Current language: {language}</Text>
      <Text>Current booking step: {bookingStep}</Text>
      <Button onPress={handleNextStep} title="Next Step" />
      <Button onPress={updatePermissions} title="Allow Location" />
    </View>
  );
};
```

### Auth Store

```typescript
import { useAuthStore } from '@/store/authStore';

// In your component
const MyComponent = () => {
  const auth = useAuthStore();
  
  // Access auth properties
  const { isAuthenticated, user, loading } = auth;
  
  // Use auth actions
  const handleLogin = () => {
    auth.loginStart();
    // API call here
    try {
      // On success
      auth.login({ name: 'John Doe', email: '<EMAIL>' });
    } catch (error) {
      // On failure
      auth.loginFailure('Login failed');
    }
  };
  
  const handleLogout = () => {
    auth.logout();
  };
  
  if (loading) {
    return <LoadingSpinner />;
  }
  
  return (
    <View>
      {isAuthenticated ? (
        <>
          <Text>Welcome, {user?.name}</Text>
          <Button onPress={handleLogout} title="Logout" />
        </>
      ) : (
        <Button onPress={handleLogin} title="Login" />
      )}
    </View>
  );
};
```

## Accessing Multiple Stores

If you need to access multiple stores, you can import them individually or use the combined store:

```typescript
import { useStore } from '@/store';

// In your component
const MyComponent = () => {
  const store = useStore();
  
  // Access all stores
  const { theme, config, auth } = store;
  
  return (
    <View style={{ backgroundColor: theme.colors.background }}>
      <Text style={{ color: theme.colors.text }}>
        Hello, {auth.user?.name || 'Guest'}
      </Text>
      <Text>Language: {config.language}</Text>
    </View>
  );
};
```

## After Migration

After completing the migration:

1. Test your application thoroughly
2. Remove the old AppContext files once everything is working correctly
3. Update any documentation or onboarding materials to reflect the new state management approach

## Troubleshooting

If you encounter issues after migration:

1. Check the console for errors
2. Verify that all imports have been updated correctly
3. Make sure you're using the correct store methods
4. For complex state updates, you may need to adjust the implementation to match Zustand's patterns
