import React from 'react';
import {Text, TextStyle, StyleSheet} from 'react-native';
import {FONTS, FONT_SIZE} from '../utils/fonts';

/***
 * Typography Component
 * USAGE
 *
      <View style={styles.section}>
        <Typography variant="title">Font Variants</Typography>
        <Typography variant="heading">Heading - Helvetica Bold</Typography>
        <Typography variant="title">Title - Helvetica Bold</Typography>
        <Typography variant="subtitle">Subtitle - Helvetica Medium</Typography>
        <Typography variant="body">Body - Helvetica Regular</Typography>
        <Typography variant="caption">Caption - Helvetica Regular Small</Typography>
        <Typography variant="button">Button - Helvetica Medium Caps</Typography>
      </View>

      <View style={styles.section}>
        <Typography variant="title">Font Weights</Typography>
        <Typography style={{ fontFamily: FONTS.bold }}>
          Bold - {FONTS.bold}
        </Typography>
        <Typography style={{ fontFamily: FONTS.medium }}>
          Medium - {FONTS.medium}
        </Typography>
        <Typography style={{ fontFamily: FONTS.regular }}>
          Regular - {FONTS.regular}
        </Typography>
      </View>

      <View style={styles.section}>
        <Typography variant="title">Font Colors</Typography>
        <Typography color="#3F51B5">Primary Color</Typography>
        <Typography color="#FF9800">Secondary Color</Typography>
        <Typography color="#4CAF50">Success Color</Typography>
        <Typography color="#F44336">Danger Color</Typography>
        <Typography color="#FFEB3B">Warning Color</Typography>
      </View>

      <View style={styles.section}>
        <Typography variant="title">Text Alignment</Typography>
        <Typography align="left">Left Aligned Text</Typography>
        <Typography align="center">Center Aligned Text</Typography>
        <Typography align="right">Right Aligned Text</Typography>
        <Typography align="justify">
          Justified Text - This is a longer paragraph to demonstrate justified text alignment.
          The text will be stretched to fill the entire width of the container.
        </Typography>
      </View>
 */

interface TypographyProps {
  children: React.ReactNode;
  variant?:
    | 'heading'
    | 'question'
    | 'title'
    | 'title2'
    | 'sectionTitle'
    | 'subtitle'
    | 'body'
    | 'caption'
    | 'button'
    | 'permissionTitle'
    | 'badgeText'
    | 'subtitle2'
    | 'bodyMedium'
    | 'bodyMedium1'
    | 'parkTitle'
    | 'subTitle3'
    | 'subTitle4'
    | 'communitySubDetail'
    | 'moreText'
    | 'tagTitle'
    | 'squareTabTitle'
    | 'communityBtnText'
    | 'pageTitle'
    | 'errorText'
    | 'notificationText'
    | 'gameCardDescription'
    | 'gameCardTitle'
    | 'coachTitle'
    | 'coachType'
    | 'profileCardTitle'
    | 'court'
    | 'pillTitle'
    | 'playerTitle'
    | 'equipmentTitle'
    | 'tryNow'
    | 'invitePlayersTitle'
    | 'pushNotificationTitle'
    | 'userName'
    | 'dateTimeFrequency'
    | 'storageTag'
    | 'selectAll'
    | 'classCardTitle'
    | 'frequencyTitle'
    | 'openingTitle'
    | 'statsNumber'
    | 'status'
    | 'groupType'
    | 'description'
    | 'skip'
    | 'label'
    | 'qrName'
    | 'findTitle'
    | 'progressNumberText'
    | 'progressDescText'
    | 'progressHeadingText'
    | 'chatHeaderTitle'
    | 'chatHeaderSubtitle'
    | 'chatUserName'
    | 'emptyCourt';
  color?: string;
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify';
  style?: TextStyle | TextStyle[];
}

const Typography: React.FC<TypographyProps> = ({
  children,
  variant = 'body',
  color,
  align,
  style,
}) => {
  const getVariantStyle = () => {
    switch (variant) {
      case 'heading':
        return styles.heading;
      case 'question':
        return styles.question;
      case 'title':
        return styles.title;
      case 'title2':
        return styles.title2;
      case 'sectionTitle':
        return styles.sectionTitle;
      case 'subtitle':
        return styles.subtitle;
      case 'body':
        return styles.body;
      case 'caption':
        return styles.caption;
      case 'button':
        return styles.button;
      case 'badgeText':
        return styles.badgeText;
      case 'permissionTitle':
        return styles.permissionTitle;
      case 'subtitle2':
        return styles.subtitle2;
      case 'bodyMedium':
        return styles.bodyMedium;
      case 'bodyMedium1':
        return styles.bodyMedium1;
      case 'parkTitle':
        return styles.parkTitle;
      case 'subTitle3':
        return styles.subTitle3;
      case 'subTitle4':
        return styles.subTitle4;
      case 'communitySubDetail':
        return styles.communitySubDetail;
      case 'moreText':
        return styles.moreText;
      case 'tagTitle':
        return styles.tagTitle;
      case 'squareTabTitle':
        return styles.squareTabTitle;
      case 'communityBtnText':
        return styles.communityBtnText;
      case 'pageTitle':
        return styles.pageTitle;
      case 'notificationText':
        return styles.notificationText;
      case 'errorText':
        return styles.errorText;
      case 'gameCardDescription':
        return styles.gameCardDescription;
      case 'gameCardTitle':
        return styles.gameCardTitle;
      case 'coachTitle':
        return styles.coachTitle;
      case 'coachType':
        return styles.coachType;
      case 'profileCardTitle':
        return styles.profileCardTitle;
      case 'court':
        return styles.court;
      case 'pillTitle':
        return styles.pillTitle;
      case 'playerTitle':
        return styles.playerTitle;
      case 'equipmentTitle':
        return styles.equipmentTitle;
      case 'tryNow':
        return styles.tryNow;
      case 'invitePlayersTitle':
        return styles.invitePlayersTitle;
      case 'pushNotificationTitle':
        return styles.pushNotificationTitle;
      case 'userName':
        return styles.userName;
      case 'dateTimeFrequency':
        return styles.dateTimeFrequency;
      case 'storageTag':
        return styles.storageTag;
      case 'selectAll':
        return styles.selectAll;
      case 'classCardTitle':
        return styles.classCardTitle;
      case 'frequencyTitle':
        return styles.frequencyTitle;
      case 'openingTitle':
        return styles.openingTitle;
      case 'statsNumber':
        return styles.statsNumber;
      case 'status':
        return styles.status;
      case 'groupType':
        return styles.groupType;
      case 'description':
        return styles.description;
      case 'skip':
        return styles.skip;
      case 'label':
        return styles.label;
      case 'qrName':
        return styles.qrName;
      case 'findTitle':
        return styles.findTitle;
      case 'progressHeadingText':
        return styles.progressHeadingText;
      case 'progressNumberText':
        return styles.progressNumberText;
      case 'progressDescText':
        return styles.progressDescText;
      case 'chatHeaderTitle':
        return styles.chatHeaderTitle;
      case 'chatHeaderSubtitle':
        return styles.chatHeaderSubtitle;
      case 'chatUserName':
        return styles.chatUserName;
      case 'emptyCourt':
        return styles.emptyCourt;
      default:
        return styles.body;
    }
  };

  return (
    <Text style={[getVariantStyle(), align && {textAlign: align}, color && {color}, style]}>
      {children}
    </Text>
  );
};

const styles = StyleSheet.create({
  heading: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.heading,
    lineHeight: 40,
    letterSpacing: 0.25,
  },
  question: {
    fontFamily: FONTS.thin,
    fontSize: FONT_SIZE.heading,
    letterSpacing: -1.5,
  },
  title: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.title,
    lineHeight: 36,
    letterSpacing: 0.25,
  },
  title2: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.subTitle2,
    letterSpacing: 0,
  },
  sectionTitle: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.xxxl,
    lineHeight: 28,
    letterSpacing: 0.15,
  },
  subtitle: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.xl,
    lineHeight: 28,
    letterSpacing: 0.15,
  },
  body: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.md,
    lineHeight: 24,
    letterSpacing: 0.5,
  },
  caption: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.sm,
    lineHeight: 20,
    letterSpacing: 0.4,
  },
  button: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.md,
    lineHeight: 24,
    letterSpacing: 1.25,
    textTransform: 'uppercase',
  },
  permissionTitle: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.xxl,
    lineHeight: 28,
    letterSpacing: 0.15,
  },
  badgeText: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.md,
    lineHeight: 16,
  },
  subtitle2: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.subTitle,
    lineHeight: 30,
    letterSpacing: 0.25,
  },
  bodyMedium: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.lg,
    lineHeight: 24,
    letterSpacing: 0.5,
  },
  bodyMedium1: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.md,
    lineHeight: 16,
    letterSpacing: 0.5,
  },
  parkTitle: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.xxl,
    lineHeight: 28,
    letterSpacing: 0.15,
  },
  subTitle3: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.xxxl,
    lineHeight: 35,
    letterSpacing: 0.25,
  },
  subTitle4: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.xxl,
    lineHeight: 25,
    letterSpacing: 0.25,
  },
  communitySubDetail: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.md,
    lineHeight: 15,
    letterSpacing: 0.4,
    fontWeight: '400',
  },
  moreText: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.sm,
    lineHeight: 9,
    letterSpacing: 0.4,
  },
  tagTitle: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.xs,
    lineHeight: 20,
    letterSpacing: 0.4,
    fontWeight: 400,
  },
  squareTabTitle: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.sm,
    lineHeight: 28,
    letterSpacing: 0.15,
  },
  communityBtnText: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.md,
    lineHeight: 15,
    letterSpacing: 0.4,
    fontWeight: '400',
  },
  pageTitle: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.xxl,
    // lineHeight: 30,
    letterSpacing: 0,
  },
  errorText: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.sm,
  },
  notificationText: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.xl,
    lineHeight: 20,
    letterSpacing: 0,
    fontWeight: '400',
  },
  gameCardDescription: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.sm,
    lineHeight: 14,
    letterSpacing: 0,
    fontWeight: '400',
  },
  gameCardTitle: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.xxl,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
  coachTitle: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.xl,
    lineHeight: 20,
    fontWeight: '700',
  },
  coachType: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.sm1,
    lineHeight: 15,
    fontWeight: '700',
  },
  profileCardTitle: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.xxl,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
  court: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.xxl,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
  pillTitle: {
    fontSize: FONT_SIZE.heading,
    lineHeight: 36,
    letterSpacing: -0.03,
  },
  playerTitle: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.xl,
    lineHeight: 28,
    fontWeight: 700,
    letterSpacing: 0,
  },
  equipmentTitle: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.xxxl,
    lineHeight: 24,
    fontWeight: '300',
  },
  tryNow: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.lg,
    letterSpacing: 0,
    fontWeight: '700',
  },
  invitePlayersTitle: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.subTitle2,
    lineHeight: 30,
    fontWeight: '700',
  },
  pushNotificationTitle: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.xl,
    lineHeight: 30,
    fontWeight: '400',
  },
  userName: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.subTitle2,
    lineHeight: 26,
    fontWeight: '400',
  },
  dateTimeFrequency: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.lg,
    lineHeight: 16,
    letterSpacing: 0.5,
    fontWeight: '300',
  },
  storageTag: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.lg,
    lineHeight: 16,
    letterSpacing: 0.5,
    fontWeight: '400',
  },
  selectAll: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.sm2,
    lineHeight: 16,
    letterSpacing: 0.5,
    fontWeight: '400',
  },
  classCardTitle: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.subTitle,
    lineHeight: 28,
  },
  frequencyTitle: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.xs,
    lineHeight: 12,
    fontWeight: '300',
  },
  openingTitle: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.sm3,
    lineHeight: 12,
    fontWeight: '300',
  },
  statsNumber: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.headingLg,
    lineHeight: 40,
    fontWeight: '700',
  },
  status: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.lg,
    lineHeight: 20,
    fontWeight: '700',
  },
  groupType: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.md1,
    lineHeight: 20,
    fontWeight: '700',
  },
  description: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.lg,
    lineHeight: 20,
    letterSpacing: 0,
    fontWeight: '400',
  },
  skip: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.xl,
  },
  label: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.xl,
    fontWeight: '400',
  },
  qrName: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.heading,
    fontWeight: '700',
  },
  findTitle: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.subTitle3,
    lineHeight: 30,
    fontWeight: '700',
    letterSpacing: 0,
  },
  progressNumberText: {
    fontFamily: FONTS.bold,
    fontSize: FONT_SIZE.subTitle2,
    fontWeight: '700',
  },
  progressDescText: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.md1,
    fontWeight: '400',
  },
  progressHeadingText: {
    fontFamily: FONTS.regular,
    fontSize: FONT_SIZE.md,
    fontWeight: '700',
    letterSpacing: 0,
  },
  chatHeaderTitle: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.xl,
    fontWeight: '400',
    letterSpacing: 0,
  },
  chatHeaderSubtitle: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.sm,
    fontWeight: '400',
    letterSpacing: 0,
  },
  chatUserName: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.sm,
    fontWeight: '400',
    letterSpacing: 0,
  },
  emptyCourt: {
    fontFamily: FONTS.medium,
    fontSize: FONT_SIZE.xxl,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
});

export default Typography;
