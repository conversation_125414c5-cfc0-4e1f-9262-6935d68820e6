import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    scrollViewContainer: {
      flexGrow: 1,
      paddingHorizontal: 16,
    },
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    root: {
      flex: 1,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    coachProfileImage: {
      width: 90,
      height: 90,
      borderRadius: 100,
    },
    coachProfileContainer: {
      paddingHorizontal: 16,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: 10,
    },
    contentContainer: {
      paddingHorizontal: 16,
      marginVertical: 8,
    },
    ratingContainer: {
      flexDirection: 'row',
      borderRadius: 10,
      padding: 6,
      marginTop: 2,
      marginLeft: 5,
      marginBottom: 10,
    },
    coachType: {
      paddingLeft: 10,
    },
    coachNameContainer: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      gap: 10,
      marginTop: 15,
      paddingHorizontal: 16,
    },
    buttonContainer: {
      gap: 12,
      marginTop: 15,
      marginBottom: 15,
    },
    ratingBar: {
      marginLeft: 10,
      marginTop: 5,
      marginBottom: 20,
    },
  });
