import {Platform, StatusBar} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

/**
 * Hook to get safe area insets with additional utility functions
 */
export const useSafeArea = () => {
  const insets = useSafeAreaInsets();

  return {
    ...insets,
    // Add any additional utility functions here
    getTopPadding: (includeTop: boolean = true) => includeTop ? insets.top : 0,
    getBottomPadding: (includeBottom: boolean = true) => includeBottom ? insets.bottom : 0,
  };
};

/**
 * Configure status bar for Android
 *
 * @param backgroundColor - Background color of the status bar
 * @param barStyle - Style of the status bar text
 * @param translucent - Whether the status bar is translucent
 */
export const configureStatusBar = (
  backgroundColor: string,
  barStyle: 'light-content' | 'dark-content' | 'default' = 'light-content',
  translucent: boolean = true
) => {
  StatusBar.setBarStyle(barStyle);

  if (Platform.OS === 'android') {
    StatusBar.setBackgroundColor(backgroundColor);
    StatusBar.setTranslucent(translucent);
  }
};
