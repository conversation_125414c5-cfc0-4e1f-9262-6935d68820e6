import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, ActivityIndicator} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '@navigation/index';
import {useThemeStore} from '@/store/themeStore';
import {useAuthStore} from '@/store/authStore';
import {getGlobalStyles} from '@utils/styleUtils';
import {signInWithGoogle, signInWithFacebook} from '@utils/auth';
import {CImage, SafeAreaView} from '@/components';
import {Images} from '@/config';
import Typography from '@/components/Typography';
import {tokenStorage} from '@/services/api';
import {useTranslationContext} from '@/context/TranslationContext';

type SignupScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Signup'>;

const SignupScreen = () => {
  const navigation = useNavigation<SignupScreenNavigationProp>();
  const theme = useThemeStore();
  const {t} = useTranslationContext();
  const {login, loginStart, loginFailure} = useAuthStore();
  const globalStyles = getGlobalStyles({theme});
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isFacebookLoading, setIsFacebookLoading] = useState(false);

  const accessToken = tokenStorage.getString('accessToken');
  const refreshToken = tokenStorage.getString('refreshToken');
  const idToken = tokenStorage.getString('idToken');

  console.log('accessToken=====>>>>>', accessToken);
  console.log('refreshToken=====>>>>>', refreshToken);
  console.log('idToken=====>>>>>', idToken);
  const handleGoogleSignup = async () => {
    try {
      setIsGoogleLoading(true);
      loginStart();
      const result = await signInWithGoogle();

      if (result.success && result.user) {
        login(result.user);
        navigation.navigate('MainTabs');
      } else {
        loginFailure(result.error || 'Google sign in failed');
      }
      setIsGoogleLoading(false);
    } catch (error) {
      setIsGoogleLoading(false);
      const errorMessage = error instanceof Error ? error.message : 'Google signup error';
      loginFailure(errorMessage);
      console.error('Google signup error:', error);
    }
  };

  const handleFacebookSignup = async () => {
    try {
      setIsFacebookLoading(true);
      loginStart();
      const result = await signInWithFacebook();

      if (result.success && result.user) {
        login(result.user);
        navigation.navigate('MainTabs');
      } else {
        loginFailure(result.error || 'Facebook sign in failed');
      }
      setIsFacebookLoading(false);
    } catch (error) {
      setIsFacebookLoading(false);
      const errorMessage = error instanceof Error ? error.message : 'Facebook signup error';
      loginFailure(errorMessage);
      console.error('Facebook signup error:', error);
    }
  };

  const handleEmailSignup = () => {
    navigation.navigate('EmailSignup');
  };

  const handleTermsPress = () => {
    navigation.navigate('TermsAndConditions');
  };

  const renderButtonContent = (source: string, text: string, isLoading: boolean) => {
    return isLoading ? (
      <ActivityIndicator size="small" color={theme.colors.secondary} />
    ) : (
      <>
        <CImage source={source} style={{width: 30, height: 30}} resizeMode="contain" />
        <Text style={[styles(theme).socialButtonText, {color: theme.colors.black}]}>{text}</Text>
      </>
    );
  };

  return (
    <SafeAreaView
      includeTop={true}
      style={[styles(theme).container, {backgroundColor: theme.colors.background}]}>
      <View style={styles(theme).content}>
        <Typography variant="title" style={[styles(theme).title]}>
          {t('signupScreen.signUpWith')}
        </Typography>

        <View style={styles(theme).buttonsContainer}>
          <TouchableOpacity
            style={[
              styles(theme).socialButton,
              isFacebookLoading && styles(theme).loadingButton,
              {backgroundColor: theme.colors.white},
            ]}
            onPress={handleEmailSignup}
            disabled={isFacebookLoading}
            activeOpacity={0.7}>
            {renderButtonContent(Images.facebook, t('signupScreen.facebook'), isFacebookLoading)}
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles(theme).socialButton,
              isGoogleLoading && styles(theme).loadingButton,
              {backgroundColor: theme.colors.white},
            ]}
            onPress={handleEmailSignup}
            disabled={isGoogleLoading}
            activeOpacity={0.7}>
            {renderButtonContent(Images.google, t('signupScreen.google'), isGoogleLoading)}
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles(theme).socialButton, {backgroundColor: theme.colors.white}]}
            onPress={handleEmailSignup}
            activeOpacity={0.7}>
            {renderButtonContent(Images.mail, t('signupScreen.email'), isGoogleLoading)}
          </TouchableOpacity>
        </View>

        <View style={styles(theme).footer}>
          <Text style={[globalStyles.text, styles(theme).termsText, {color: theme.colors.text}]}>
            {t('signupScreen.bySigningUpYouAgreeToOur')}{' '}
            <Text
              style={[styles(theme).termsLink, {color: theme.colors.text}]}
              onPress={handleTermsPress}>
              {t('signupScreen.termsAndConditions')}
            </Text>
          </Text>
        </View>
      </View>

      <View style={styles(theme).loginContainer}>
        <Text style={[globalStyles.text, styles(theme).loginText, {color: theme.colors.text}]}>
          {t('signupScreen.alreadyHaveAnAccount')}{' '}
          <Text
            style={[styles(theme).loginLink, {color: theme.colors.text}]}
            onPress={() => navigation.navigate('Login')}>
            {t('signupScreen.signin')}
          </Text>
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    title: {
      color: theme.colors.text,
      marginBottom: 20,
    },
    buttonsContainer: {
      marginBottom: 20,
      width: '100%',
    },
    socialButton: {
      flexDirection: 'row',
      padding: 14,
      borderRadius: 8,
      alignItems: 'center',
      marginBottom: 16,
      width: '100%',
      elevation: 1,
      height: 52,
    },
    loadingButton: {
      opacity: 0.7,
    },
    socialButtonText: {
      fontWeight: '400',
      fontSize: 16,
      marginLeft: 10,
    },
    footer: {
      width: '100%',
      paddingHorizontal: 20,
      marginTop: 'auto',
    },
    termsText: {
      textAlign: 'center',
      fontSize: 12,
      lineHeight: 18,
    },
    termsLink: {
      fontWeight: 'bold',
      textDecorationLine: 'underline',
    },
    loginContainer: {
      paddingBottom: 24,
      alignItems: 'center',
    },
    loginText: {
      fontSize: 14,
    },
    loginLink: {
      fontWeight: 'bold',
      textDecorationLine: 'underline',
    },
  });

export default SignupScreen;
