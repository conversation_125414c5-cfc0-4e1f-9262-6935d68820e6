import React, {Component, ErrorInfo, ReactNode} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import * as Sentry from '@sentry/react-native';
import {useThemeStore} from '@/store/themeStore';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  componentName?: string; // For identifying which component caused the error
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

/**
 * A generic error boundary component that catches errors in its child component tree
 * and reports them to Sentry.
 */
class ErrorBoundaryClass extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return {hasError: true, error};
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Report the error to Sentry with component context
    const {componentName} = this.props;

    Sentry.withScope(scope => {
      // Add component info to help with debugging
      scope.setTag('component', componentName || 'unknown');
      scope.setExtra('componentStack', errorInfo.componentStack);

      // Capture the error
      Sentry.captureException(error);
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    console.error('Error caught by ErrorBoundary:', error, errorInfo);
  }

  resetError = (): void => {
    this.setState({hasError: false, error: null});
  };

  render(): ReactNode {
    const {hasError, error} = this.state;
    const {children, fallback} = this.props;

    if (hasError) {
      // If a custom fallback is provided, use it
      if (fallback) {
        return fallback;
      }

      // Default fallback UI
      return (
        <View style={styles.container}>
          <Text style={styles.title}>Something went wrong</Text>
          <Text style={styles.message}>{error?.message || 'An unexpected error occurred'}</Text>
          <TouchableOpacity style={styles.button} onPress={this.resetError}>
            <Text style={styles.buttonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return children;
  }
}

// Styles for the default error UI
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#121212',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#E0E0E0',
    marginBottom: 10,
  },
  message: {
    fontSize: 16,
    color: '#B0B0B0',
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#2B8ECE',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});

// Wrapper component to use theme
const ErrorBoundary: React.FC<ErrorBoundaryProps> = props => {
  const theme = useThemeStore();

  // Custom fallback UI with theme
  const themedFallback = props.fallback || (
    <View style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <Text style={[styles.title, {color: theme.colors.text}]}>Something went wrong</Text>
      <Text style={[styles.message, {color: theme.colors.secondary}]}>
        The app encountered an unexpected error
      </Text>
      <TouchableOpacity
        style={[styles.button, {backgroundColor: theme.colors.primary}]}
        onPress={() => {
          // Access the ErrorBoundaryClass instance and reset the error
          if (props.onError) {
            props.onError(new Error('Reset triggered'), {componentStack: ''});
          }
        }}>
        <Text style={styles.buttonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );

  return <ErrorBoundaryClass {...props} fallback={themedFallback} />;
};

export default ErrorBoundary;
