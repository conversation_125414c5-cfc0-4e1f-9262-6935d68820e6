import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    padding16: {
      paddingHorizontal: 16,
    },
    closeButton: {
      alignItems: 'flex-end',
      margin: 12,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingBottom: 16,
    },

    headerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: 5,
    },
    headerCol: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 5,
    },
    calendarContainer: {
      marginTop: 16,
      paddingHorizontal: 16,
    },
    pillContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
      marginTop: 16,
    },
    pill: {
      flex: 1,
      backgroundColor: theme.colors.dimGray,
      borderTopLeftRadius: 12,
      borderTopRightRadius: 12,
      borderBottomLeftRadius: 12,
      borderBottomRightRadius: 12,
      height: 60,
      paddingTop: 6,
      paddingLeft: 10,
      paddingRight: 10,
    },
    pillText: {
      fontSize: 20,
      letterSpacing: -0.5,
      fontWeight: 500,
      color: theme.colors.text,
    },
    divider: {
      width: '100%',
      height: 1,
      backgroundColor: theme.colors.secondary,
      marginVertical: 15,
      alignSelf: 'center',
    },
    collapsibleTitle: {
      color: theme.colors.text,
      fontSize: 14,
      flex: 1,
    },
    box: {
      flex: 1,
      flexDirection: 'row',
      padding: 16,
      backgroundColor: theme.colors.dimGray,
      borderRadius: 12,
      gap: 10,
      justifyContent: 'space-between',
    },
  });
