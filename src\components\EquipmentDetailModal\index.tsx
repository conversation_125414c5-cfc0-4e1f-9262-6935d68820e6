import React, {useEffect, useRef, useState} from 'react';
import {View, TouchableOpacity, ScrollView} from 'react-native';
import {styles as createStyles} from './styles';
import Typography from '../Typography';
import {CImage, CustomModal, Icon} from '@/components';
import VideoPlayer, {VideoPlayerRef} from 'react-native-video-player';
import {useThemeStore} from '@/store/themeStore';
import useTranslation from '@/hooks/useTranslation';

interface EquipmentSpec {
  label: string;
  value: string;
}

interface Review {
  name: string;
  text: string;
  rating: number;
}

interface EquipmentData {
  id: string;
  name: string;
  image: any;
  specs: EquipmentSpec[];
  averageRating: number | undefined;
  playerReviews: Review[];
  staffVideoThumbnail: any;
  staffVideo: any;
}

interface EquipmentDetailModalProps {
  visible: boolean;
  onClose: () => void;
  onAdd: () => void;
  equipment?: EquipmentData;
}

const EquipmentDetailModal: React.FC<EquipmentDetailModalProps> = ({
  visible,
  onClose,
  onAdd,
  equipment,
}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const scrollViewRef = React.useRef<ScrollView>(null);
  const playerRef = useRef<VideoPlayerRef>(null);
  const [showAllReviews, setShowAllReviews] = useState(false);
  const {t} = useTranslation();
  useEffect(() => {
    setShowAllReviews(false);
  }, [visible]);

  const scrollToBottom = () => {
    scrollViewRef.current?.scrollToEnd({animated: true});
  };

  const renderStars = (count: number, size: number) => {
    return Array.from({length: 5}, (_, i) => (
      <Icon
        key={i}
        name="ball2"
        size={size}
        color={i < count ? theme.colors.primary : theme.colors.text}
      />
    ));
  };

  const toggleReviews = () => {
    setShowAllReviews(prev => !prev);
  };

  const reviewsToShow = showAllReviews
    ? equipment?.playerReviews
    : equipment?.playerReviews?.slice(0, 1);

  return (
    <CustomModal
      visible={visible}
      modalContainerStyle={styles.modalContainer}
      onClose={onClose}
      animationType="fade">
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose}>
          <Icon name="close1-1" size={36} color={'red'} />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            onClose();
            onAdd();
          }}
          style={styles.tryNowContainer}>
          <Typography variant="subtitle" style={styles.tryText}>
            {t('EquipmentDetailModal.tryItNow')}
          </Typography>
          <Icon name="plus" style={styles.plusIcon} size={32} />
        </TouchableOpacity>
      </View>
      <ScrollView
        ref={scrollViewRef}
        contentContainerStyle={styles.scrollView}
        showsVerticalScrollIndicator={false}
        decelerationRate="fast"
        scrollEventThrottle={16}
        removeClippedSubviews={true}
        snapToAlignment="start">
        <TouchableOpacity activeOpacity={1} style={styles.scroll}>
          {/* Image */}
          <CImage
            source={{uri: equipment?.image}}
            style={styles.racketImage}
            resizeMode="contain"
          />

          {/* Title */}
          <Typography variant="subtitle" color={theme.colors.text}>
            {equipment?.name}
          </Typography>

          {/* Specs */}
          <Typography variant="sectionTitle" style={styles.specHeading}>
            {t('EquipmentDetailModal.specifications')}
          </Typography>
          {equipment?.specs.map(item => (
            <View key={item?.label} style={styles.specItem}>
              <Typography variant="sectionTitle" style={styles.label}>
                {item.label}
              </Typography>
              <Typography variant="sectionTitle" style={styles.label}>
                {item.value}
              </Typography>
            </View>
          ))}

          {/* Down arrow */}
          <TouchableOpacity onPress={scrollToBottom}>
            <Icon name="dropdown" style={styles.dropdownIcon} size={25} color={theme.colors.text} />
          </TouchableOpacity>

          {/* Average Rating */}
          <Typography variant="sectionTitle" style={styles.specHeading}>
            {t('EquipmentDetailModal.averageRating')}
          </Typography>
          <View style={styles.ratingRow}>{renderStars(equipment?.averageRating ?? 0, 30)}</View>

          {/* Player Reviews */}
          <Typography variant="sectionTitle" style={styles.specHeading}>
            {t('EquipmentDetailModal.playerReviews')}
          </Typography>
          {reviewsToShow?.map((review, index) => (
            <View key={review?.name} style={styles.reviewContainer}>
              <Typography variant="subtitle" style={styles.reviewName}>
                {review.name}
              </Typography>
              <Typography variant="caption" style={styles.reviewText}>
                "{review.text}"
              </Typography>
              <View style={styles.ratingRow2}>{renderStars(review.rating, 18)}</View>
              {index < reviewsToShow.length - 1 && <View style={styles.reviewDivider} />}
            </View>
          ))}
          {equipment?.playerReviews && equipment.playerReviews.length > 1 && (
            <TouchableOpacity onPress={toggleReviews}>
              <Typography style={styles.moreText}>
                {showAllReviews ? t('EquipmentDetailModal.less') : t('EquipmentDetailModal.more')}
              </Typography>
            </TouchableOpacity>
          )}

          {/* Staff Review Video */}
          <Typography variant="sectionTitle" style={styles.review}>
            {t('EquipmentDetailModal.staffReviews')}
          </Typography>
          <View style={styles.videoContainer}>
            <VideoPlayer
              ref={playerRef}
              endWithThumbnail
              thumbnail={{
                uri: equipment?.staffVideoThumbnail,
              }}
              customStyles={{
                playButton: styles.playButton,
                playArrow: styles.playArrow,
                video: styles.video,
                thumbnail: styles.thumbnail,
                wrapper: styles.wrapper,
              }}
              source={{
                uri: equipment?.staffVideo,
              }}
              onError={e => console.log('Video error:', e.error)}
              showDuration={true}
              style={styles.videoPlayer}
            />
          </View>
        </TouchableOpacity>
      </ScrollView>
    </CustomModal>
  );
};

export default EquipmentDetailModal;
