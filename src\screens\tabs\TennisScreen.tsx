import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {SafeAreaView} from '@/components';

const TennisScreen = () => {
  const theme = useThemeStore();

  return (
    <SafeAreaView style={[styles(theme).container, {backgroundColor: theme.colors.background}]}>
      <View style={styles(theme).content}>
        <Text style={[styles(theme).title]}>Tennis</Text>
        <Text style={[styles(theme).subtitle]}>Tennis information and updates</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      flex: 1,
      padding: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      textAlign: 'center',
      marginBottom: 8,
      color: theme.colors.white,
    },
    subtitle: {
      textAlign: 'center',
      marginBottom: 40,
      color: theme.colors.transparentWhite,
    },
  });

export default TennisScreen;
