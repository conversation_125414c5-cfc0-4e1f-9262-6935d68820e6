import {useEffect} from 'react';
import {useNavigation, useRoute} from '@react-navigation/native';

export const useNavigationDebug = () => {
  const navigation = useNavigation();
  const route = useRoute();

  useEffect(() => {
    console.log('🔍 Navigation Debug:', {
      routeName: route.name,
      canGoBack: navigation.canGoBack(),
      params: route.params,
    });
  }, [route.name, navigation, route.params]);

  return {
    routeName: route.name,
    canGoBack: navigation.canGoBack(),
    params: route.params,
  };
};

export default useNavigationDebug;
