import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    toastBox: {
      minHeight: 50,
      width: '90%',
      padding: 10,
      backgroundColor: theme.colors.white,
      borderRadius: 12,
      justifyContent: 'center',
      elevation: 20,
      shadowColor: '#000',
      shadowOffset: {width: 0, height: 20},
      shadowOpacity: 0.25,
      shadowRadius: 20,
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    iconCircle: {
      borderRadius: 100,
      marginHorizontal: 10,
      height: 25,
      width: 25,
      alignItems: 'center',
      justifyContent: 'center',
    },
    textContainer: {
      flex: 1,
      justifyContent: 'center',
    },
  });
