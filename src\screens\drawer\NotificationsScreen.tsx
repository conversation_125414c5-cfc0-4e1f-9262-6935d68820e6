import React, {useState} from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {useNavigation} from '@react-navigation/native';
import {Header, Icon, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import {OfferBanner} from '@/components/common/OfferBanner';
import CustomToggleSwitch from '@/components/CustomToggleSwitch';
import SearchInput from '@/components/SearchInput';
import useTranslation from '@/hooks/useTranslation';

const NotificationsScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [chatWidgetEnabled, setChatWidgetEnabled] = useState(true);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const {t} = useTranslation();

  const clearSearch = (): void => {
    setSearchQuery('');
  };

  return (
    <SafeAreaView style={styles(theme).container}>
      <Header
        leftComponent={
          <TouchableOpacity onPress={() => navigation.goBack()} activeOpacity={0.7}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        }
        rightIcons={[
          {name: 'notification', size: 24},
          {name: 'chat', size: 24, badge: 14},
        ]}
        pageTitle={t('drawerNotification.notifications')}
        backgroundColor={'transparent'}
        transparent={false}
        showBack={false}
      />
      <View style={styles(theme).contentContainer}>
        <SearchInput
          placeholder={t('drawerNotification.searchNotifications')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          onClear={clearSearch}
        />

        {/* Settings */}
        <View>
          <View style={styles(theme).settingItem}>
            <Typography variant="body" style={styles(theme).settingLabel}>
              {t('drawerNotification.notificationsWidget')}
            </Typography>

            <CustomToggleSwitch
              isEnabled={notificationsEnabled}
              onToggle={setNotificationsEnabled}
              activeText={t('common.on')}
              inactiveText={t('common.off')}
            />
          </View>

          <View style={styles(theme).divider} />

          <View style={styles(theme).settingItem}>
            <Typography variant="body" style={styles(theme).settingLabel}>
              {t('drawerNotification.chatWidget')}
            </Typography>
            <CustomToggleSwitch
              isEnabled={chatWidgetEnabled}
              onToggle={setChatWidgetEnabled}
              activeText={t('common.on')}
              inactiveText={t('common.off')}
            />
          </View>
        </View>
      </View>

      {/* Footer */}
      <View style={styles(theme).footer}>
        <OfferBanner text={t('drawerNotification.inviteFriendsGet10Off')} />
      </View>
    </SafeAreaView>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },

    contentContainer: {
      paddingHorizontal: 16,
    },
    settingItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 16,
    },
    settingLabel: {
      fontSize: 16,
      color: theme.colors.white,
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.secondary,
    },
    footer: {
      position: 'absolute',
      bottom: 15,
      left: 0,
      right: 0,
    },
    footerButton: {
      backgroundColor: theme.colors.oceanBlue,
      paddingVertical: 14,
      borderRadius: 0,
      alignItems: 'center',
    },
    footerButtonText: {
      color: theme.colors.white,
      fontSize: 16,
      fontWeight: '500',
    },
  });

export default NotificationsScreen;
