import {MMKV} from 'react-native-mmkv';
import {useState, useEffect} from 'react';
import {StateStorage} from 'zustand/middleware';

// Initialize MMKV
export const storage = new MMKV({
  id: 'goraqt-app-storage',
  encryptionKey: 'goraqt-secure-storage-key',
});

// Create a Zustand-compatible storage adapter
export const mmkvStorage: StateStorage = {
  getItem: (name: string) => {
    const value = storage.getString(name);
    return value ?? null;
  },
  setItem: (name: string, value: string) => {
    storage.set(name, value);
  },
  removeItem: (name: string) => {
    storage.delete(name);
  },
};

// Helper function to get a value from storage
export function getItem<T>(key: string): T | null {
  const value = storage.getString(key);
  if (!value) return null;
  try {
    return JSON.parse(value) as T;
  } catch (error) {
    console.error(`Error parsing item with key "${key}":`, error);
    return null;
  }
}

// Helper function to set a value in storage
export function setItem<T>(key: string, value: T): void {
  try {
    storage.set(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error setting item with key "${key}":`, error);
  }
}

// Helper function to remove an item from storage
export function removeItem(key: string): void {
  storage.delete(key);
}

// Helper function to clear all storage
export function clearAll(): void {
  storage.clearAll();
}

// Helper function to clear only auth-related storage
export function clearAuthStorage(): void {
  // Clear specific auth-related keys
  removeItem('app-auth');

  // If you have other auth-related items, clear them here
  console.log('Auth storage cleared');
}

// Custom hook for using MMKV storage with state management
export function useMMKVStorage<T>(
  key: string,
  initialValue: T,
): [T, (value: T | ((prevValue: T) => T)) => void] {
  // Get the stored value
  const readStoredValue = (): T => {
    const item = getItem<T>(key);
    return item !== null ? item : initialValue;
  };

  // State to hold the current value
  const [storedValue, setStoredValue] = useState<T>(readStoredValue);

  // Listen for changes to this key from other components
  useEffect(() => {
    const listener = storage.addOnValueChangedListener(changedKey => {
      if (changedKey === key) {
        setStoredValue(readStoredValue());
      }
    });

    return () => {
      listener.remove();
    };
  }, [key]);

  // Return a wrapped setter function that persists the new value to storage
  const setValue = (value: T | ((prevValue: T) => T)): void => {
    try {
      // If value is a function, call it with the previous state
      const valueToStore = value instanceof Function ? value(storedValue) : value;

      // Save state
      setStoredValue(valueToStore);

      // Save to storage
      setItem(key, valueToStore);
    } catch (error) {
      console.error(`Error setting MMKV value for key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}
