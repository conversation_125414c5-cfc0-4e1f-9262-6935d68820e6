import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import {styles as CreateStyles} from './styles';
import {useThemeStore} from '@/store';
import Typography from '../Typography';
import {Icon} from '@/components';

interface ClassCardProps {
  type: string;
  startTime: string;
  endTime: string;
  frequency: string;
  numberOfOpenings: number;
  editable: boolean;
}

const ClassCard = (props: ClassCardProps) => {
  const {type, startTime, endTime, frequency, numberOfOpenings, editable} = props;

  const theme = useThemeStore();
  const styles = CreateStyles(theme);
  return (
    <View style={styles.root}>
      <View>
        <Typography variant="classCardTitle" color={theme.colors.white}>
          {type}
        </Typography>
        {editable && (
          <TouchableOpacity activeOpacity={0.7} style={styles.editButton}>
            <Icon name="editpen" size={24} color={theme.colors.orange} />
          </TouchableOpacity>
        )}
      </View>
      <Typography variant="classCardTitle" color={theme.colors.white}>
        {startTime} - {endTime}
      </Typography>
      <Typography variant="frequencyTitle" color={theme.colors.white}>
        {frequency}
      </Typography>
      <Typography variant="openingTitle" color={theme.colors.white} style={styles.openingTitle}>
        {numberOfOpenings} openings
      </Typography>
    </View>
  );
};

export default ClassCard;
