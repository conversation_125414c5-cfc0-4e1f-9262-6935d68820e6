/* eslint-disable react/display-name */
import {NavigationContainerRef} from '@react-navigation/native';
import {createNativeStackNavigator, NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useState, forwardRef, useImperativeHandle} from 'react';
import {useConfigStore} from '@/store/configStore';
import TabNavigator from './TabNavigator';
import ExamplesStack from './ExamplesStack';
import PermissionsScreen from '@/screens/Permission/PermissionsScreen';
import TermsOfServiceScreen from '@/screens/TermsService/TermsOfServiceScreen';
import LoginScreen from '@/screens/auth/Login';
import SplashScreen from '@/screens/Splash/SplashScreen';
import {
  EmailLoginScreen,
  EmailSignupScreen,
  ForgotPasswordScreen,
  SignupScreen,
} from '@/screens/auth';
import SettingsScreen from '@/screens/settings/SettingsScreen';
import BiometricsScreen from '@/screens/settings/BiometricsScreen';
import {NotificationsScreen, ReferFriendScreen, MyMatchesScreen} from '@/screens/drawer';
import NotificationsListScreen from '@/screens/Notification';
import {useAuthStore} from '@/store/authStore';
import CartScreen from '@/screens/tabs/SearchScreen/CartScreen';
import GoFit from '@/screens/GoFit';
import SentryNavigationContainer from './SentryNavigationContainer';
import TermsAndConditionsScreen from '@/screens/TermsAndConditions';
import {useQuery} from '@tanstack/react-query';
import api from '@/services/api';
import Verification from '@/screens/auth/Verification';
import CToast from '@/components/CToast';
// Community screens for root level navigation
import MyGroups from '@/screens/MyGroups';
import NewGroup from '@/components/Community/NewGroup';
import JoinGroups from '@/screens/JoinGroups';
import JoinGroupDetails from '@/screens/JoinGroupDetails';
import AddMembers from '@/components/Community/AddMembers';
import CreateGroupMemberList from '@/screens/CreateGroupMemberList';
import ChatScreen from '@/screens/Chat';
import CommunityDetails from '@/screens/CommunityDetails';
import CommentScreen from '@/screens/CommentScreen';
import CoachProfile from '@/screens/UpYourGame/CoachProfile';
import FindCoach from '@/screens/UpYourGame/FindCoach';
import FindClass from '@/screens/UpYourGame/FindClass';
import PlayerConnectDateScreen from '@/screens/PlayerConnectDateScreen';

export type RootStackParamList = {
  Splash: undefined;
  Permissions: undefined;
  TermsOfService: undefined;
  Login: {email?: string};
  Signup: undefined;
  EmailLogin: undefined;
  Verification: {email: string; password?: string; type?: 'forgotPassword'};
  EmailSignup: undefined;
  ForgotPassword: {email?: string};
  MainTabs: undefined;
  Drawer: {
    screen?: string;
    params?: {
      screen?: string;
      params?: {
        screen?: string;
        params?: any;
      };
    };
  };
  // Community screens at root level for proper swipe-to-back
  MyGroups: undefined;
  NewGroup: {
    groupName?: string;
  };
  JoinGroups: undefined;
  JoinGroupDetails: undefined;
  AddMembers: {
    groupName: string;
  };
  CreateGroupMemberList: {
    groupImage?: string;
    groupName?: string;
    members: Array<{id: string; name: string; image: string}>;
  };
  Chat: undefined;
  CommunityDetails: {
    from?: string;
    title?: string;
    data?: any;
  };
  CommentScreen: {
    from?: string;
    title?: string;
    data?: any;
  };
  CoachProfile: undefined;
  FindCoach: undefined;
  FindClass: undefined;
  PlayerConnectDateScreen: undefined;

  Biometrics: undefined;
  Settings: undefined;
  Notifications: undefined;
  ReferFriend: undefined;
  MyMatches: undefined;
  NotificationsList: undefined;
  CartScreen: undefined;
  GoFit: undefined;
  Examples: undefined;
  TermsAndConditions: undefined;
};

export type RootStackScreenProps<T extends keyof RootStackParamList> = NativeStackScreenProps<
  RootStackParamList,
  T
>;

interface DrawerMenuItem {
  id: string;
  label: string;
  title: string;
  screen?: string;
  isVisible: boolean;
}

interface ApiResponse {
  status: boolean;
  data: {
    language: string;
    json_data: DrawerMenuItem[];
  };
}

interface CommunityFeaturesResponse {
  status: boolean;
  data: {
    language: string;
    json_data: DrawerMenuItem[];
  };
}

const Stack = createNativeStackNavigator<RootStackParamList>();

const AppNavigator = forwardRef<NavigationContainerRef<RootStackParamList>>((props, ref) => {
  const {isAuthenticated} = useAuthStore();
  const {permissions, setPermissions} = useConfigStore();

  const [showSplash, setShowSplash] = useState(true);
  const [showPermissions, setShowPermissions] = useState(false);
  const [showTerms, setShowTerms] = useState(false);

  // Expose navigation methods to parent
  // useImperativeHandle(ref, () => ({
  //   navigate: (name: keyof RootStackParamList, params?: any) => {
  //     if (navigationRef.current) {
  //       navigationRef.current.navigate(name, params);
  //     }
  //   },
  //   // Add other navigation methods as needed
  // }));

  // Internal navigation reference
  const navigationRef = React.useRef<NavigationContainerRef<RootStackParamList>>(null);

  const handleSplashComplete = () => {
    setShowSplash(false);
    setShowPermissions(true);
  };

  const handlePermissionsComplete = () => {
    setShowPermissions(false);
    if (!permissions?.termsAndConditions) {
      setShowTerms(true);
    }
  };

  const handleTermsAccepted = () => {
    setShowTerms(false);
    setPermissions({...permissions, termsAndConditions: true});
  };

  // Fetch app configurations
  useQuery<ApiResponse>({
    queryKey: ['app-config'],
    queryFn: async () => {
      try {
        const response = await api.get('/app-configuration/app_features');
        if (response.data.status) {
          useConfigStore.getState().setDrawerMenuItems(response.data.data.json_data);
          return response.data;
        }
      } catch (error) {
        console.error('Error fetching app config:', error);
        return null;
      }
    },
  });

  // Fetch community features
  useQuery<CommunityFeaturesResponse>({
    queryKey: ['community-features'],
    queryFn: async () => {
      try {
        const response = await api.get('/app-configuration/community_features');
        if (response.data.status) {
          console.log('response ===>', response);

          useConfigStore.getState().setCommunityFeatures(response.data.data.json_data);
          return response.data;
        }
      } catch (error) {
        console.error('Error fetching community features:', error);
        return null;
      }
    },
  });

  return (
    <SentryNavigationContainer ref={navigationRef}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: true, // Enable gesture navigation
          gestureDirection: 'horizontal', // iOS style (default)
        }}>
        {showSplash ? (
          <Stack.Screen name="Splash" options={{}}>
            {() => <SplashScreen onComplete={handleSplashComplete} />}
          </Stack.Screen>
        ) : showPermissions ? (
          <Stack.Screen name="Permissions" options={{}}>
            {() => <PermissionsScreen onComplete={handlePermissionsComplete} />}
          </Stack.Screen>
        ) : showTerms ? (
          <Stack.Screen name="TermsOfService" options={{}}>
            {() => <TermsOfServiceScreen onAccept={handleTermsAccepted} />}
          </Stack.Screen>
        ) : !isAuthenticated ? (
          <>
            <Stack.Screen name="Signup" component={SignupScreen} />
            <Stack.Screen name="Login" component={LoginScreen} />
            <Stack.Screen name="EmailLogin" component={EmailLoginScreen} />
            <Stack.Screen name="EmailSignup" component={EmailSignupScreen} />
            <Stack.Screen
              name="ForgotPassword"
              component={ForgotPasswordScreen}
              options={{
                animation: 'fade',
                // presentation: 'transparentModal',
              }}
            />
            <Stack.Screen
              name="Verification"
              component={Verification}
              options={{
                animation: 'fade',
                // presentation: 'transparentModal',
              }}
            />
            <Stack.Screen
              name="TermsAndConditions"
              component={TermsAndConditionsScreen}
              options={{
                animation: 'fade',
                //  presentation: 'transparentModal'
              }}
            />
          </>
        ) : (
          <>
            {/* Main app navigation - drawer commented out for swipe-to-back fix */}
            <Stack.Screen
              name="Drawer"
              component={TabNavigator}
              options={{
                gestureEnabled: true, // ✅ ENABLE GESTURES!
              }}
            />

            {/* Community screens moved to root level for proper swipe-to-back */}
            <Stack.Screen
              name="MyGroups"
              component={MyGroups}
              options={{
                gestureEnabled: true,
              }}
            />
            <Stack.Screen
              name="NewGroup"
              component={NewGroup}
              options={{
                gestureEnabled: true,
              }}
            />
            <Stack.Screen
              name="JoinGroups"
              component={JoinGroups}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="JoinGroupDetails"
              component={JoinGroupDetails}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="AddMembers"
              component={AddMembers}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="CreateGroupMemberList"
              component={CreateGroupMemberList}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="Chat"
              component={ChatScreen}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="CommunityDetails"
              component={CommunityDetails}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="CommentScreen"
              component={CommentScreen}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="CoachProfile"
              component={CoachProfile}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="FindCoach"
              component={FindCoach}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="FindClass"
              component={FindClass}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="PlayerConnectDateScreen"
              component={PlayerConnectDateScreen}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />

            {/* Other app screens */}
            <Stack.Screen
              name="GoFit"
              component={GoFit}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="Settings"
              component={SettingsScreen}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="Biometrics"
              component={BiometricsScreen}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="Notifications"
              component={NotificationsScreen}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="NotificationsList"
              component={NotificationsListScreen}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="CartScreen"
              component={CartScreen}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="ReferFriend"
              component={ReferFriendScreen}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="MyMatches"
              component={MyMatchesScreen}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="Examples"
              component={ExamplesStack}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
            <Stack.Screen
              name="TermsAndConditions"
              component={TermsAndConditionsScreen}
              options={{
                gestureEnabled: true,
                animation: 'slide_from_right',
              }}
            />
          </>
        )}
      </Stack.Navigator>
    </SentryNavigationContainer>
  );
});

export {AppNavigator};
