import React from 'react';
import {Text, TextStyle, StyleSheet, View} from 'react-native';
import {ICON_MAP, iconStyle} from '../utils/iconStyles';
import { useThemeStore } from '@/store/themeStore';;

interface IconProps {
  name: string;
  size?: number;
  color?: string;
  style?: TextStyle;
  debug?: boolean;
}

const CIcon: React.FC<IconProps> = ({name, size = 24, color, style, debug = false}) => {
  const theme = useThemeStore();
  const iconColor = color || theme.colors.black;

  const styles = StyleSheet.create({
    icon: {
      ...iconStyle,
    },
    debugContainer: {
      borderWidth: 1,
      borderColor: theme.colors.red,
      padding: 4,
      alignItems: 'center',
      justifyContent: 'center',
    },
    debugText: {
      fontSize: 8,
      color: theme.colors.red,
    },
  });

  // Check if the name already has the 'icon-' prefix, if not, add it
  const iconName = name.startsWith('icon-') ? name : `icon-${name}`;

  // Try both the original name and the name with icon- prefix
  const icon = ICON_MAP[iconName] || ICON_MAP[name] || '';

  if (debug) {
    return (
      <View style={[styles.debugContainer, {width: size * 1.5, height: size * 1.5}]}>
        <Text style={[styles.icon, {fontSize: size, color: iconColor}, style]}>{icon}</Text>
        <Text style={styles.debugText}>{`Unicode: ${icon}`}</Text>
        <Text style={styles.debugText}>{`Original Name: ${name}`}</Text>
        <Text style={styles.debugText}>{`Used Name: ${iconName}`}</Text>
      </View>
    );
  }

  return <Text style={[styles.icon, {fontSize: size, color: iconColor}, style]}>{icon}</Text>;
};

export default CIcon;
