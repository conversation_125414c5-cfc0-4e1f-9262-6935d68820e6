import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    datePickerView: {
      flexGrow: 1,
      paddingHorizontal: 16,
      paddingBottom: 40,
      justifyContent: 'space-between',
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.dimGray,
      marginVertical: 16,
    },
    descriptionContainer: {
      paddingHorizontal: 16,
      paddingVertical: 10,
      marginTop: 16,
      backgroundColor: theme.colors.darkGray,
    },
    descriptionText: {
      color: theme.colors.text,
      fontSize: 16,
    },
    titleContainer: {
      paddingHorizontal: 16,
    },
    titleStyle: {
      marginVertical: 10,
      fontSize: theme.fontSize.xlarge,
      fontWeight: 'bold',
    },

    backButton: {
      padding: 10,
      backgroundColor: theme.colors.dimGray,
      borderRadius: 10,
    },
    totalContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: 16,
    },
    buttonContainer: {
      gap: 16,
    },
  });
