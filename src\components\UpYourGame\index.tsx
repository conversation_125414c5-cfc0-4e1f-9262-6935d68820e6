import React from 'react';
import {FlatList, View, ImageBackground} from 'react-native';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import {Images} from '@/config';
import MediaCard from '@/components/MediaCard';
import GameCard from '@/components/GameCard';
import {SafeAreaView} from '@/components';
import useTranslation from '@/hooks/useTranslation';

type NavigationProp = NativeStackNavigationProp<CommunityStackParamList>;

interface upYourGameData {
  id: number;
  title: string;
  description: string;
  image: string;
  navigate: () => void;
}

const UpYourGame = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  const {t} = useTranslation();

  const navigation = useNavigation<NavigationProp>();

  const upYourGameData = [
    {
      id: 1,
      title: t('upYourGameScreen.findCoach'),
      description: t('upYourGameScreen.coachDescription'),
      navigate: () => navigation.navigate('FindCoach'),
    },
    {
      id: 2,
      title: t('upYourGameScreen.findClass'),
      description: t('upYourGameScreen.coachDescription'),
      navigate: () => navigation.navigate('FindClass'),
    },
    {
      id: 3,
      title: t('upYourGameScreen.shareCoach'),
      description: t('upYourGameScreen.coachDescription'),
      navigate: () => {},
    },
    {
      id: 4,
      title: t('upYourGameScreen.getUTC'),
      description: t('upYourGameScreen.coachDescription'),
      navigate: () => {},
    },
  ];

  const renderComponent = ({item}: {item: upYourGameData}) => {
    return <GameCard data={item} containerStyle={styles.cardContainer} onPress={item.navigate} />;
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <MediaCard
        source={Images.thumbnail}
        showActionBar={false}
        imageContainerStyle={styles.imageContainer}
      />
    </View>
  );

  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage}>
      <SafeAreaView includeTop={false} includeBottom={false} style={styles.root}>
        <FlatList
          data={upYourGameData}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item, index) => index.toString()}
          renderItem={renderComponent}
          contentContainerStyle={styles.content}
          onEndReachedThreshold={0.5}
          ListHeaderComponent={renderHeader}
        />
      </SafeAreaView>
    </ImageBackground>
  );
};

export default UpYourGame;
