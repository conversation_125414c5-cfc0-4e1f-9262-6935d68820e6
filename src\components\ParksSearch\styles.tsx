import {FONT_SIZE} from '@/utils/fonts';
import {Platform, StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'transparent',
      width: '100%',
      // marginTop: 10,
      paddingHorizontal: 20,
    },
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 15,
      marginBottom: 10,
    },
    flex1: {
      flex: 1,
    },
    headerText: {
      color: theme.colors.text,
      lineHeight: 28,
    },
    headerImage: {
      width: 30,
      height: 30,
      borderRadius: 100,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Platform.OS === 'ios' ? 15 : 4,
      paddingHorizontal: 12,
      marginBottom: 10,
    },

    searchBar: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      paddingHorizontal: 20,
      gap: 10,
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    searchIcon: {
      marginRight: 8,
      fontSize: 16,
    },
    searchInput: {
      flex: 1,
      color: theme.colors.primary,
      fontSize: 18,
      minHeight: 50,
    },
    parkList: {
      // flexGrow: 1,
      paddingBottom: 20,
    },

    loaderContainer: {
      padding: 16,
      alignItems: 'center',
    },
    loaderText: {
      color: theme.colors.text,
      marginTop: 8,
    },
    emptyContainer: {
      paddingVertical: 20,
      // alignItems: 'center',
    },
    emptyText: {
      color: theme.colors.text,
    },
    backButtonContainer: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: 8,
      marginBottom: 10,
    },
    backButton: {
      padding: 10,
      backgroundColor: theme.colors.dimGray,
      borderRadius: 10,
    },
    recommendInput: {
      backgroundColor: theme.colors.recommendBg,
      color: theme.colors.text,
      borderRadius: 10,
      padding: 10,
      fontSize: FONT_SIZE.lg,
      marginBottom: 16,
      height: 51,
      paddingLeft: 16,
      borderWidth: 1,
      borderColor: theme.colors.white,
    },
    recommendView: {
      backgroundColor: theme.colors.white1,
      borderRadius: 10,
      paddingHorizontal: 16,
      paddingVertical: 10,
      marginTop: 16,
      width: '100%',
      maxWidth: 400,
      alignSelf: 'center',
    },
    recommendInputContainer: {
      flexDirection: 'row',
      gap: 8,
    },
    recommendTitle: {
      // marginBottom: 8,
    },
    input: {
      height: 51,
      paddingHorizontal: 14,
      borderRadius: 8,
    },
    inputContainer: {
      marginBottom: 20,
    },
  });
