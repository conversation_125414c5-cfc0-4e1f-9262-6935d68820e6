import React, {memo} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {StyleProp, TouchableOpacity, View, ViewStyle} from 'react-native';
import {styles as createStyles} from './styles';
import Typography from '../Typography';
import {Icon} from '@/components';
import {MapPin} from '@/data';
interface ParkCardProps {
  variant?: 'equipment';
  parkData: MapPin;
  onSelect?: (parkData: ParkCardProps['parkData']) => void;
  onEditPress?: () => void;
  time?: string;
  courts?: number;
  surface?: string;
  date?: string;
  style?: StyleProp<ViewStyle>;
}
const ParkCard = (props: ParkCardProps) => {
  const {
    parkData,
    variant,
    onSelect = () => {},
    onEditPress = () => {},
    courts,
    surface,
    time,
    date,
    style,
  } = props;

  const formattedDate = (data: string) => {
    const newDate = new Date(data);

    const options = {month: 'long', day: 'numeric'};
    const formatDate = newDate.toLocaleDateString('en-US', options);
    return formatDate;
  };

  const theme = useThemeStore();
  const styles = createStyles(theme, variant);

  return (
    <TouchableOpacity
      style={[styles.parkItem, style]}
      onPress={() => {
        onSelect(parkData);
      }}>
      <View style={styles.pinContainer}>
        <View style={styles.iconContainer}>
          <Icon name="location3" size={26} color={theme.colors.activeColor} />
          <View style={{marginLeft: -5, marginTop: 2}}>
            <Icon name="location4" size={24} color={theme.colors.activeColor} />
          </View>
        </View>
      </View>
      <View style={styles.parkInfo}>
        <Typography
          variant="parkTitle"
          style={variant === 'equipment' ? styles.equipParkName : styles.parkName}>
          {parkData?.title}
        </Typography>
        {variant === 'equipment' && (
          <TouchableOpacity activeOpacity={0.7} style={styles.editButton} onPress={onEditPress}>
            <Icon name="editpen" size={20} color={theme.colors.orange} />
          </TouchableOpacity>
        )}
        <Typography variant="bodyMedium1" style={styles.parkLocation}>
          {parkData?.description}
        </Typography>
        {variant === 'equipment' && (
          <>
            {courts && (
              <Typography variant="caption" style={styles.courtsText}>
                {`Courts ${courts}`}
              </Typography>
            )}
            {surface && (
              <Typography variant="caption" style={styles.courtsText}>
                {`surface ${surface}`}
              </Typography>
            )}
            {date && time && (
              <Typography variant="parkTitle" style={styles.timeText}>
                {` ${formattedDate(date)} ${time}`}
              </Typography>
            )}
          </>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default memo(ParkCard);
