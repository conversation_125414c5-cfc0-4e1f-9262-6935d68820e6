import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {useNavigation} from '@react-navigation/native';
import {Header, Icon, SafeAreaView} from '@/components';
import {styles as createStyles} from './styles';
import {OfferBanner} from '@/components/common/OfferBanner';
import useTranslation from '@/hooks/useTranslation';

const RewardsScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation();
  const styles = createStyles(theme);

  const {t} = useTranslation();

  return (
    <SafeAreaView includeTop={false} style={styles.container}>
      <Header
        leftComponent={
          <TouchableOpacity onPress={() => navigation.goBack()} activeOpacity={0.7}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        }
        rightIcons={[
          {name: 'notification', size: 24, color: theme.colors.activeColor},
          {name: 'chat', size: 24, badge: 14, color: theme.colors.activeColor},
        ]}
        pageTitle={t('rewardsScreen.title')}
        backgroundColor={'transparent'}
        transparent={false}
        showBack={false}
      />
      <View style={styles.footer}>
        <OfferBanner text="Invite friends, get 10% off" />
      </View>
    </SafeAreaView>
  );
};

export default RewardsScreen;
