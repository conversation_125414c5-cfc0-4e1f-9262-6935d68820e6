import React from 'react';
import {View, StyleSheet, TouchableOpacity, StyleProp, ViewStyle} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import Typography from '../Typography';
import {Icon} from '@/components';

interface SportsCardProps {
  sportsTitle: string;
  onCardPress: () => void;
  icon: string;
  containerStyle?: StyleProp<ViewStyle>;
}

const SportsCard: React.FC<SportsCardProps> = ({
  sportsTitle,
  onCardPress,
  icon,
  containerStyle,
}) => {
  const theme = useThemeStore();
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      style={[styles(theme).card, containerStyle]}
      onPress={onCardPress}>
      <Typography variant="equipmentTitle" color={theme.colors.white} style={styles(theme).title}>
        {sportsTitle}
      </Typography>
      <View style={styles(theme).imageContainer}>
        <Icon name={icon} size={92} color={theme.colors.white} />
      </View>
    </TouchableOpacity>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    card: {
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      borderBottomLeftRadius: 10,
      padding: 13,
      justifyContent: 'center',
      minWidth: 148,
      minHeight: 251,
      backgroundColor: theme.colors.orange1,
    },
    imageContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 20,
    },
    title: {
      position: 'absolute',
      top: 13,
      left: 13,
    },
  });

export default SportsCard;
