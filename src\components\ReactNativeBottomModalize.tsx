import React, {useRef, useEffect} from 'react';
import {useTheme} from '@/context/AppContext';
import {KeyboardAvoidingView, Platform, View, StyleSheet} from 'react-native';
import {Modalize} from 'react-native-modalize';

const ReactNativeBottomModalize = ({
  children,
  withHandle = false,
  alwaysOpen = false,
}: {
  children: React.ReactNode;
  withHandle?: boolean;
  alwaysOpen?: boolean;
}) => {
  const modalizeRef = useRef<Modalize>(null);
  const theme = useTheme();

  useEffect(() => {
    const timer = setTimeout(() => {
      modalizeRef.current?.open();
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  return alwaysOpen ? (
    <KeyboardAvoidingView
      style={styles.flex}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}>
      <Modalize
        ref={modalizeRef}
        modalStyle={{
          backgroundColor: theme.colors.background,
          padding: 20,
        }}
        scrollViewProps={{
          showsVerticalScrollIndicator: true,
          scrollEnabled: true,
          keyboardShouldPersistTaps: 'handled',
        }}
        alwaysOpen={150}
        panGestureEnabled={false}
        withHandle={withHandle}
        handleStyle={{
          backgroundColor: theme.colors.bottomSheetBackground,
        }}
        handlePosition="inside"
        disableScrollIfPossible={false}>
        <View>{children}</View>
      </Modalize>
    </KeyboardAvoidingView>
  ) : (
    <KeyboardAvoidingView
      style={styles.flex}
      behavior={Platform.OS === 'ios' ? 'padding' : 'padding'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}>
      <Modalize
        ref={modalizeRef}
        modalStyle={{
          backgroundColor: theme.colors.background,
          padding: 20,
        }}
        panGestureEnabled={false}
        scrollViewProps={{
          showsVerticalScrollIndicator: true,
          scrollEnabled: true,
          keyboardShouldPersistTaps: 'handled',
        }}
        adjustToContentHeight
        closeOnOverlayTap={false}
        withHandle={withHandle}
        handleStyle={{
          backgroundColor: theme.colors.bottomSheetBackground,
        }}
        handlePosition="inside"
        disableScrollIfPossible={false}>
        <View>{children}</View>
      </Modalize>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  flex: {
    flex: 1,
  },
});

export default ReactNativeBottomModalize;
