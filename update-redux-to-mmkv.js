/**
 * Script to help update Redux imports to MMKV AppContext imports
 *
 * To run: node update-redux-to-mmkv.js
 */

const fs = require('fs');
const path = require('path');
const {execSync} = require('child_process');

// Find all files with useSelector or useDispatch
const findReduxFiles = () => {
  try {
    const grepCommand =
      "grep -r '@store\\|useAppSelector\\|useAppDispatch\\|rootState' --include='*.tsx' --include='*.ts' src/";
    const result = execSync(grepCommand, {encoding: 'utf-8'});

    // Parse grep results
    const files = new Set();
    result.split('\n').forEach(line => {
      if (!line) return;
      const filePath = line.split(':')[0];
      if (filePath) {
        files.add(filePath);
      }
    });

    return Array.from(files);
  } catch (error) {
    console.error('Error finding Redux files:', error.message);
    return [];
  }
};

// Process a file to update Redux imports to AppContext
const processFile = filePath => {
  try {
    console.log(`Processing ${filePath}...`);
    let content = fs.readFileSync(filePath, 'utf-8');

    // Check what is being used
    const usesAppSelector = content.includes('useAppSelector');
    const usesAppDispatch = content.includes('useAppDispatch');
    const usesRootState = content.includes('RootState');
    const usesTheme = content.includes('state.theme');
    const usesConfig = content.includes('state.config');
    const usesAuth = content.includes('state.auth');

    // Track if we made changes
    let modified = false;

    // Replace Redux imports
    if (usesAppSelector || usesAppDispatch) {
      content = content.replace(
        /import\s+[{](.*)useAppSelector(.*)useAppDispatch(.*)[}]\s+from\s+['"]@\/store\/hooks['"]/g,
        (match, before, mid, after) => {
          modified = true;
          return '';
        },
      );

      content = content.replace(
        /import\s+[{](.*)useAppSelector(.*)useAppDispatch(.*)[}]\s+from\s+['"]@store\/hooks['"]/g,
        (match, before, mid, after) => {
          modified = true;
          return '';
        },
      );

      content = content.replace(
        /import\s+[{](.*)useAppSelector(.*)[}]\s+from\s+['"]@\/store\/hooks['"]/g,
        (match, before, after) => {
          modified = true;
          return '';
        },
      );

      content = content.replace(
        /import\s+[{](.*)useAppSelector(.*)[}]\s+from\s+['"]@store\/hooks['"]/g,
        (match, before, after) => {
          modified = true;
          return '';
        },
      );

      content = content.replace(
        /import\s+[{](.*)useAppDispatch(.*)[}]\s+from\s+['"]@\/store\/hooks['"]/g,
        (match, before, after) => {
          modified = true;
          return '';
        },
      );

      content = content.replace(
        /import\s+[{](.*)useAppDispatch(.*)[}]\s+from\s+['"]@store\/hooks['"]/g,
        (match, before, after) => {
          modified = true;
          return '';
        },
      );
    }

    // Replace redux action imports
    content = content.replace(
      /import\s+[{](.*)loginSuccess(.*)loginFailure(.*)loginStart(.*)[}]\s+from\s+['"]@\/store\/slices\/authSlice['"]/g,
      match => {
        modified = true;
        return '';
      },
    );

    content = content.replace(
      /import\s+[{](.*)loginSuccess(.*)loginFailure(.*)loginStart(.*)[}]\s+from\s+['"]@store\/slices\/authSlice['"]/g,
      match => {
        modified = true;
        return '';
      },
    );

    content = content.replace(
      /import\s+[{](.*)logout(.*)[}]\s+from\s+['"]@\/store\/slices\/authSlice['"]/g,
      match => {
        modified = true;
        return '';
      },
    );

    content = content.replace(
      /import\s+[{](.*)logout(.*)[}]\s+from\s+['"]@store\/slices\/authSlice['"]/g,
      match => {
        modified = true;
        return '';
      },
    );

    content = content.replace(
      /import\s+[{](.*)setPermissions(.*)resetBooking(.*)[}]\s+from\s+['"]@\/store\/slices\/configSlice['"]/g,
      match => {
        modified = true;
        return '';
      },
    );

    content = content.replace(
      /import\s+[{](.*)setPermissions(.*)resetBooking(.*)[}]\s+from\s+['"]@store\/slices\/configSlice['"]/g,
      match => {
        modified = true;
        return '';
      },
    );

    content = content.replace(
      /import\s+[{](.*)setPermissions(.*)[}]\s+from\s+['"]@\/store\/slices\/configSlice['"]/g,
      match => {
        modified = true;
        return '';
      },
    );

    content = content.replace(
      /import\s+[{](.*)setPermissions(.*)[}]\s+from\s+['"]@store\/slices\/configSlice['"]/g,
      match => {
        modified = true;
        return '';
      },
    );

    content = content.replace(
      /import\s+[{](.*)resetBooking(.*)[}]\s+from\s+['"]@\/store\/slices\/configSlice['"]/g,
      match => {
        modified = true;
        return '';
      },
    );

    content = content.replace(
      /import\s+[{](.*)resetBooking(.*)[}]\s+from\s+['"]@store\/slices\/configSlice['"]/g,
      match => {
        modified = true;
        return '';
      },
    );

    // Replace RootState import
    if (usesRootState) {
      content = content.replace(
        /import\s+[{](.*)RootState(.*)[}]\s+from\s+['"]@\/store['"][\s;]*/g,
        (match, before, after) => {
          modified = true;
          return '';
        },
      );

      content = content.replace(
        /import\s+[{](.*)RootState(.*)[}]\s+from\s+['"]@\/store\/index['"][\s;]*/g,
        (match, before, after) => {
          modified = true;
          return '';
        },
      );

      content = content.replace(
        /import\s+[{](.*)RootState(.*)[}]\s+from\s+['"]@store['"][\s;]*/g,
        (match, before, after) => {
          modified = true;
          return '';
        },
      );

      content = content.replace(
        /import\s+[{](.*)RootState(.*)[}]\s+from\s+['"]@store\/index['"][\s;]*/g,
        (match, before, after) => {
          modified = true;
          return '';
        },
      );
    }

    // Add AppContext imports
    let contextImportsToAdd = [];

    if (usesTheme) {
      contextImportsToAdd.push('useTheme');
    }
    if (usesConfig) {
      contextImportsToAdd.push('useConfig');
    }
    if (usesAuth) {
      contextImportsToAdd.push('useAuth');
    }

    if (content.includes('dispatch(logout())')) {
      contextImportsToAdd.push('useAuth');
    }

    if (content.includes('dispatch(resetBooking())')) {
      contextImportsToAdd.push('useConfig');
    }

    if (contextImportsToAdd.length > 0) {
      const importStatement = `import {${contextImportsToAdd.join(', ')}} from '@/context/AppContext';`;

      // Check if we have React imports to add after
      if (content.includes('import React')) {
        content = content.replace(/(import React.*?;)/, `$1\n${importStatement}`);
      } else {
        // Add at the beginning of the file
        content = `${importStatement}\n${content}`;
      }

      modified = true;
    }

    // Replace useAppSelector(state => state.theme) with useTheme()
    if (usesTheme) {
      content = content.replace(
        /const\s+theme\s+=\s+useAppSelector\s*\(\s*(?:state\s*(?:[:=]\s*)?(?:any|RootState)?\)?\s*=>|s\s*=>)\s*s(?:tate)?\.theme\s*\)/g,
        'const theme = useTheme()',
      );

      content = content.replace(
        /const\s+{\s*theme\s*}\s+=\s+useAppSelector\s*\(\s*(?:state|s)\s*=>\s*(?:state|s)\s*\)/g,
        'const theme = useTheme()',
      );

      content = content.replace(
        /useAppSelector\s*\(\s*(?:state\s*(?:[:=]\s*)?(?:any|RootState)?\)?\s*=>|s\s*=>)\s*s?(?:tate)?\.theme\s*\)/g,
        'useTheme()',
      );

      modified = true;
    }

    // Replace useAppSelector(state => state.config) with useConfig()
    if (usesConfig) {
      content = content.replace(
        /const\s+config\s+=\s+useAppSelector\s*\(\s*(?:state\s*(?:[:=]\s*)?(?:any|RootState)?\)?\s*=>|s\s*=>)\s*s?(?:tate)?\.config\s*\)/g,
        'const { permissions, bookingStep, bookingData, bookingStepIndex } = useConfig()',
      );

      content = content.replace(
        /const\s+{\s*bookingStep,\s*bookingData,\s*bookingStepIndex\s*}\s+=\s+useAppSelector\s*\(\s*s\s*=>\s*s\.config\s*\)/g,
        'const { bookingStep, bookingData, bookingStepIndex } = useConfig()',
      );

      content = content.replace(
        /const\s+{\s*.*\s*}\s+=\s+useAppSelector\s*\(\s*s\s*=>\s*s\.config\s*\)/g,
        'const config = useConfig()',
      );

      content = content.replace(
        /useAppSelector\s*\(\s*(?:state\s*(?:[:=]\s*)?(?:any|RootState)?\)?\s*=>|s\s*=>)\s*s?(?:tate)?\.config\s*\)/g,
        'useConfig()',
      );

      modified = true;
    }

    // Replace useAppSelector(state => state.auth) with useAuth()
    if (usesAuth) {
      content = content.replace(
        /const\s+{\s*isAuthenticated\s*}\s+=\s+useAppSelector\s*\(\s*(?:state|s)\s*=>\s*(?:state|s)\.auth\s*\)/g,
        'const { isAuthenticated } = useAuth()',
      );

      content = content.replace(
        /const\s+auth\s+=\s+useAppSelector\s*\(\s*(?:state\s*(?:[:=]\s*)?(?:any|RootState)?\)?\s*=>|s\s*=>)\s*s?(?:tate)?\.auth\s*\)/g,
        'const auth = useAuth()',
      );

      content = content.replace(
        /useAppSelector\s*\(\s*(?:state\s*(?:[:=]\s*)?(?:any|RootState)?\)?\s*=>|s\s*=>)\s*s?(?:tate)?\.auth\s*\)/g,
        'useAuth()',
      );

      modified = true;
    }

    // Replace full state usage
    content = content.replace(
      /const\s+state\s+=\s+useAppSelector\s*\(\s*(?:state|s)\s*=>\s*(?:state|s)\s*\)/g,
      'const theme = useTheme()',
    );

    // Replace dispatch(logout()) with logout()
    if (content.includes('dispatch(logout())')) {
      content = content.replace(
        /const\s+dispatch\s+=\s+useAppDispatch\(\)/g,
        'const { logout } = useAuth()',
      );

      content = content.replace(/dispatch\(logout\(\)\)/g, 'logout()');

      modified = true;
    }

    // Replace dispatch(resetBooking()) with resetBooking()
    if (content.includes('dispatch(resetBooking())')) {
      content = content.replace(
        /const\s+dispatch\s+=\s+useAppDispatch\(\)/g,
        'const { resetBooking } = useConfig()',
      );

      content = content.replace(/dispatch\(resetBooking\(\)\)/g, 'resetBooking()');

      modified = true;
    }

    // Replace dispatch(setPermissions()) with setPermissions()
    if (content.includes('dispatch(setPermissions(')) {
      content = content.replace(
        /const\s+dispatch\s+=\s+useAppDispatch\(\)/g,
        'const { setPermissions } = useConfig()',
      );

      content = content.replace(/dispatch\(setPermissions\((.*?)\)\)/g, 'setPermissions($1)');

      modified = true;
    }

    // Replace dispatch(loginSuccess()) with login()
    if (content.includes('dispatch(loginSuccess(')) {
      content = content.replace(
        /const\s+dispatch\s+=\s+useAppDispatch\(\)/g,
        'const { login } = useAuth()',
      );

      content = content.replace(/dispatch\(loginSuccess\((.*?)\)\)/g, 'login($1)');

      modified = true;
    }

    // Update style files that use RootState
    if (filePath.includes('styles.tsx') && content.includes('RootState')) {
      content = content.replace(/RootState\['theme'\]/g, 'ThemeState');

      // Add ThemeState import
      if (!content.includes('ThemeState')) {
        content = content.replace(
          /(import.*?;)/,
          `import { ThemeState } from '@/context/AppContext';\n$1`,
        );
      }

      modified = true;
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Updated ${filePath}`);
    } else {
      console.log(`No changes needed for ${filePath}`);
    }

    return modified;
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error.message);
    return false;
  }
};

// Main function
const main = () => {
  console.log('Finding Redux files...');
  const files = findReduxFiles();
  console.log(`Found ${files.length} files using Redux`);

  let updatedCount = 0;

  files.forEach(file => {
    const wasUpdated = processFile(file);
    if (wasUpdated) {
      updatedCount++;
    }
  });

  console.log(`\nMigration summary:`);
  console.log(`Total files scanned: ${files.length}`);
  console.log(`Files updated: ${updatedCount}`);
  console.log(
    `\nNote: You may need to manually update complex Redux usages that the script couldn't handle automatically.`,
  );
  console.log(
    `Also remember to remove Redux packages after verifying the migration is complete and working correctly.`,
  );
};

// Run the script
main();
