import React, {useState} from 'react';
import {View, FlatList, TouchableOpacity} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {Icon} from '@/components';
import createStyles from './styles';
import Typography from '@/components/Typography';
import PillLabel from '@/components/PillLabel';
import {useNavigation} from '@react-navigation/native';
import {DrawerNavigationProp} from '@react-navigation/drawer';
import useTranslation from '@/hooks/useTranslation';

interface OptionItem {
  id: string;
  label: string;
}

interface SelectedOption {
  id: string;
  label: string;
}

type RootDrawerParamList = {
  EditCoachProfile: {from: string; data: SelectedOption[]};
  CoachOptions: undefined;
};

type NavigationProp = DrawerNavigationProp<RootDrawerParamList>;

const GetCertified = (props: any) => {
  const {onClose, getCertifications = () => {}, selectedCertifications = []} = props;
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const [selectedOptions, setSelectedOptions] = useState<SelectedOption[]>(selectedCertifications);
  const navigation = useNavigation<NavigationProp>();

  const {t} = useTranslation();

  // Update selectedOptions when selectedCertifications prop changes
  React.useEffect(() => {
    setSelectedOptions(selectedCertifications);
  }, [selectedCertifications]);

  const toggleOption = (option: OptionItem) => {
    setSelectedOptions(prev => {
      const newSelectedOptions = prev.some(item => item.id === option.id)
        ? prev.filter(item => item.id !== option.id)
        : [...prev, {id: option.id, label: option.label}];
      getCertifications(newSelectedOptions);
      return newSelectedOptions;
    });
  };

  const options: OptionItem[] = [
    {id: '1', label: t('getCertifiedScreen.recCoachWorkshop')},
    {id: '2', label: t('getCertifiedScreen.level1')},
    {id: '3', label: t('getCertifiedScreen.level2')},
    {id: '4', label: t('getCertifiedScreen.level3')},
    {id: '5', label: t('getCertifiedScreen.specialtyWorkshops')},
    {id: '6', label: t('getCertifiedScreen.ptrw')},
  ];

  const renderItem = ({item}: {item: OptionItem}) => {
    const isSelected = selectedOptions.some(selected => selected.id === item.id);
    return (
      <PillLabel
        textStyle={{fontWeight: isSelected ? '700' : '300'}}
        label={item.label}
        backgroundColor={isSelected ? theme.colors.activeColor : theme.colors.dimGray}
        textColor={isSelected ? theme.colors.black : theme.colors.text}
        onPress={() => toggleOption(item)}
        triangle={false}
        containerStyle={{
          height: 104,
          paddingRight: 10,
        }}
      />
    );
  };

  return (
    <View style={styles.contentInnerView}>
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => onClose(selectedOptions)}>
          <Icon name="Left-chevron" size={24} color={theme.colors.white} />
        </TouchableOpacity>
        <Typography variant="invitePlayersTitle" color={theme.colors.white}>
          {t('getCertifiedScreen.title')}
        </Typography>
      </View>
      <FlatList
        data={options}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.pillContainer}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default GetCertified;
