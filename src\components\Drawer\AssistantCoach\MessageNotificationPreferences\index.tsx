import React, {useState} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {Icon} from '@/components';
import {styles as createStyles} from './styles';
import Typography from '@/components/Typography';
import PillLabel from '@/components/PillLabel';
import {useNavigation} from '@react-navigation/native';
import useTranslation from '@/hooks/useTranslation';

interface OptionItem {
  id: string;
  label: string;
}

const MessageNotificationPreferences = (props: any) => {
  const {onClose} = props;
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const [selectedOptions, setSelectedOptions] = useState<string>();

  const navigation = useNavigation();

  const toggleOption = (option: string) => {
    setSelectedOptions(option);
  };

  const {t} = useTranslation();

  const options: OptionItem[] = [
    {id: '1', label: t('common.yes')},
    {id: '2', label: t('common.no')},
  ];

  return (
    <View style={styles.contentInnerView}>
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={onClose} style={{marginTop: 5}}>
          <Icon name="Left-chevron" size={24} color={theme.colors.white} />
        </TouchableOpacity>
        <Typography
          variant="invitePlayersTitle"
          color={theme.colors.white}
          style={{paddingRight: 20}}>
          {t('messageNotificationPreferencesScreen.title')}
        </Typography>
      </View>
      <Typography variant="pushNotificationTitle" color={theme.colors.white}>
        {t('messageNotificationPreferencesScreen.pushNotifications')}
      </Typography>
      <View style={styles.pushNotificationContainer}>
        {options?.map(data => (
          <PillLabel
            key={data.id}
            textStyle={{
              fontWeight: selectedOptions === data?.label ? '700' : '300',
              fontSize: 16,
            }}
            label={data.label}
            backgroundColor={
              selectedOptions === data?.label ? theme.colors.activeColor : theme.colors.dimGray
            }
            textColor={selectedOptions === data?.label ? theme.colors.black : theme.colors.text}
            onPress={() => toggleOption(data.label)}
            triangle={false}
            containerStyle={styles.pillContainer}
          />
        ))}
      </View>
    </View>
  );
};

export default MessageNotificationPreferences;
