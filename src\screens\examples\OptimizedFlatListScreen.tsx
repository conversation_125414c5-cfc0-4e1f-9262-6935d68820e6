import React, {useState, useCallback} from 'react';
import {View, StyleSheet, Switch, Text} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {SafeAreaView, Header} from '@/components';
import OptimizedFlatListExample from '@/components/examples/OptimizedFlatListExample';
import Typography from '@/components/Typography';

/**
 * Screen to demonstrate the optimized FlatList example
 * This screen includes toggles to enable/disable various optimizations
 * to demonstrate their impact on performance
 */
const OptimizedFlatListScreen = ({navigation}: any) => {
  const theme = useThemeStore();
  const [showPerformanceMetrics, setShowPerformanceMetrics] = useState(false);

  // Handle back button press
  const handleBackPress = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <Header
        leftIcon="back"
        onLeftPress={handleBackPress}
        title="Optimized FlatList Example"
        backgroundColor={theme.colors.background}
      />

      <View style={styles.optionsContainer}>
        <View style={styles.optionRow}>
          <Typography variant="body">Show Performance Metrics</Typography>
          <Switch
            value={showPerformanceMetrics}
            onValueChange={setShowPerformanceMetrics}
            trackColor={{false: theme.colors.divider, true: theme.colors.primary}}
            thumbColor={theme.colors.white}
          />
        </View>

        <Typography variant="caption" style={styles.note}>
          This example demonstrates best practices for optimizing FlatList performance:
        </Typography>

        <View style={styles.optimizationList}>
          <Typography variant="caption" style={styles.bulletPoint}>
            • Memoized components with React.memo()
          </Typography>
          <Typography variant="caption" style={styles.bulletPoint}>
            • getItemLayout for fixed height items
          </Typography>
          <Typography variant="caption" style={styles.bulletPoint}>
            • Optimized removeClippedSubviews, windowSize, etc.
          </Typography>
          <Typography variant="caption" style={styles.bulletPoint}>
            • Proper keyExtractor implementation
          </Typography>
          <Typography variant="caption" style={styles.bulletPoint}>
            • Memoized callback functions
          </Typography>
        </View>
      </View>

      {/* Performance metrics display */}
      {showPerformanceMetrics && (
        <View style={[styles.metricsContainer, {backgroundColor: theme.colors.white}]}>
          <Typography variant="caption" style={{color: theme.colors.textSecondary}}>
            Performance metrics would be displayed here in a real implementation. You could use
            libraries like react-native-performance or custom timing logic.
          </Typography>
        </View>
      )}

      {/* The optimized FlatList example */}
      <View style={styles.listContainer}>
        <OptimizedFlatListExample />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  optionsContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  optionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  note: {
    marginBottom: 8,
  },
  optimizationList: {
    marginLeft: 8,
  },
  bulletPoint: {
    marginBottom: 4,
  },
  metricsContainer: {
    padding: 12,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  listContainer: {
    flex: 1,
  },
});

export default OptimizedFlatListScreen;
