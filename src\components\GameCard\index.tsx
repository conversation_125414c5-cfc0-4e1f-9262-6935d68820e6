import React from 'react';
import {StyleProp, TouchableOpacity, View, ViewStyle, Image} from 'react-native';
import {styles as createStyles} from './styles';
import { useThemeStore } from '@/store/themeStore';;
import Typography from '../Typography';
import {Images} from '@/config';
interface CommunityCardProps {
  data: GoFitData;
  containerStyle?: StyleProp<ViewStyle>;
  key?: string;
  onPress?: () => void;
}

interface GoFitData {
  title: string;
  description: string;
  image: string;
}

const GameCard = (props: CommunityCardProps) => {
  const {data, containerStyle, key = '', onPress} = props;

  const theme = useThemeStore();
  const styles = createStyles(theme);

  return (
    <TouchableOpacity
      key={key}
      activeOpacity={0.8}
      style={[styles.root, containerStyle]}
      onPress={onPress}>
      <View style={styles.thumbnailContainer}>
        <Image
          source={data?.image ? {uri: data.image} : Images.thumbnail}
          style={styles.thumbnail}
          resizeMode="cover"
        />
      </View>
      <View style={styles.contentContainer}>
        <Typography variant="gameCardTitle" style={styles.title}>
          {data?.title}
        </Typography>
        <View style={styles.descriptionContainer}>
          <Typography variant="gameCardDescription" style={styles.description}>
            {data?.description}
          </Typography>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default GameCard;
