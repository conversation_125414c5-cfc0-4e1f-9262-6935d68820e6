import {useQuery, useMutation, useQueryClient, useInfiniteQuery} from '@tanstack/react-query';
import api, {handleApiError} from '../../services/api';

// Types
interface Post {
  id: string;
  title: string;
  body: string;
  userId: string;
  createdAt: string;
  // Add other post properties
}

interface CreatePostData {
  title: string;
  body: string;
  // Add other required fields
}

interface UpdatePostData {
  title?: string;
  body?: string;
  // Add other fields that can be updated
}

interface PostsResponse {
  data: Post[];
  meta: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

// Query keys
export const postKeys = {
  all: ['posts'] as const,
  lists: () => [...postKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...postKeys.lists(), {filters}] as const,
  details: () => [...postKeys.all, 'detail'] as const,
  detail: (id: string) => [...postKeys.details(), id] as const,
  infinite: () => [...postKeys.all, 'infinite'] as const,
};

// Get all posts with pagination
export const usePosts = (page = 1, limit = 10, filters: Record<string, unknown> = {}) => {
  return useQuery({
    queryKey: [...postKeys.list(filters), page, limit],
    queryFn: async () => {
      try {
        const response = await api.get('/posts', {
          params: {
            page,
            limit,
            ...filters,
          },
        });
        return response.data as PostsResponse;
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
  });
};

// Get posts with infinite loading
export const useInfinitePosts = (limit = 10, filters: Record<string, unknown> = {}) => {
  return useInfiniteQuery({
    queryKey: [...postKeys.infinite(), limit, filters],
    queryFn: async ({pageParam = 1}) => {
      try {
        const response = await api.get('/posts', {
          params: {
            page: pageParam,
            limit,
            ...filters,
          },
        });
        return response.data as PostsResponse;
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
    initialPageParam: 1,
    getNextPageParam: lastPage => {
      const {currentPage, totalPages} = lastPage.meta;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
  });
};

// Get a single post by ID
export const usePost = (id: string) => {
  return useQuery({
    queryKey: postKeys.detail(id),
    queryFn: async () => {
      try {
        const response = await api.get(`/posts/${id}`);
        return response.data as Post;
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
    // Enable the query only if we have an ID
    enabled: !!id,
  });
};

// Create a new post
export const useCreatePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (postData: CreatePostData) => {
      try {
        const response = await api.post('/posts', postData);
        return response.data as Post;
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
    // When a post is successfully created, invalidate the posts list query
    onSuccess: () => {
      queryClient.invalidateQueries({queryKey: postKeys.lists()});
    },
  });
};

// Update a post
export const useUpdatePost = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (postData: UpdatePostData) => {
      try {
        const response = await api.put(`/posts/${id}`, postData);
        return response.data as Post;
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
    // When a post is successfully updated, invalidate both the list and the specific post
    onSuccess: updatedPost => {
      queryClient.invalidateQueries({queryKey: postKeys.lists()});
      queryClient.invalidateQueries({queryKey: postKeys.detail(id)});

      // Optionally update the cache directly for immediate UI updates
      queryClient.setQueryData(postKeys.detail(id), updatedPost);
    },
  });
};

// Delete a post
export const useDeletePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        await api.delete(`/posts/${id}`);
        return id;
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
    // When a post is successfully deleted, invalidate the posts list query
    onSuccess: deletedId => {
      queryClient.invalidateQueries({queryKey: postKeys.lists()});
      // Remove the deleted post from the cache
      queryClient.removeQueries({queryKey: postKeys.detail(deletedId)});
    },
  });
};
