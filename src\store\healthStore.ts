import {create} from 'zustand';
import {persist, createJSONStorage} from 'zustand/middleware';
import {mmkvStorage} from '@/services/storage';
import {HealthData} from '@/services/health/types';
import {healthService} from '@/services/health';
import {format} from 'date-fns';

// Default health data state
const defaultHealthData: HealthData = {
  steps: {
    count: 0,
    goal: 10000,
    date: format(new Date(), 'yyyy-MM-dd'),
    loading: false,
    error: null,
  },
  heartRate: {
    value: 0,
    unit: 'bpm',
    date: format(new Date(), 'yyyy-MM-dd'),
    loading: false,
    error: null,
  },
};

// Health store interface
interface HealthStore extends HealthData {
  // Actions
  initialize: () => Promise<boolean>;
  fetchStepCount: (date?: Date) => Promise<void>;
  fetchHeartRate: (date?: Date) => Promise<void>;
  fetchAllHealthData: (date?: Date) => Promise<void>;
  setStepGoal: (goal: number) => Promise<void>;
  hasPermissions: () => Promise<boolean>;
  requestPermissions: () => Promise<boolean>;
}

// Create the health store with persistence
export const useHealthStore = create<HealthStore>()(
  persist(
    (set, get) => ({
      ...defaultHealthData,

      // Initialize health service
      initialize: async () => {
        try {
          const initialized = await healthService.initialize();
          if (initialized) {
            // Get step goal
            const goal = await healthService.getStepCountGoal();
            console.log('🚀 ~ initialize: ~ goal:', goal);
            set(state => ({
              steps: {
                ...state.steps,
                goal,
              },
            }));
          }
          return initialized;
        } catch (error) {
          console.error('Error initializing health service:', error);
          return false;
        }
      },

      // Fetch step count
      fetchStepCount: async (date = new Date()) => {
        try {
          set(state => ({
            steps: {
              ...state.steps,
              loading: true,
              error: null,
            },
          }));

          const stepData = await healthService.getStepCount(date);
          console.log('🚀 ~ fetchStepCount: ~ stepData:', stepData);

          set(state => ({
            steps: {
              ...state.steps,
              count: stepData.count,
              date: stepData.date,
              loading: false,
            },
          }));
        } catch (error) {
          console.error('Error fetching step count:', error);
          set(state => ({
            steps: {
              ...state.steps,
              loading: false,
              error: 'Failed to fetch step count',
            },
          }));
        }
      },

      // Fetch heart rate
      fetchHeartRate: async (date = new Date()) => {
        try {
          set(state => ({
            heartRate: {
              ...state.heartRate,
              loading: true,
              error: null,
            },
          }));

          const heartRateData = await healthService.getHeartRate(date);
          console.log('🚀 ~ fetchHeartRate: ~ heartRateData:', heartRateData);

          set(state => ({
            heartRate: {
              ...state.heartRate,
              value: heartRateData.value,
              unit: heartRateData.unit,
              date: heartRateData.date,
              loading: false,
            },
          }));
        } catch (error) {
          console.error('Error fetching heart rate:', error);
          set(state => ({
            heartRate: {
              ...state.heartRate,
              loading: false,
              error: 'Failed to fetch heart rate',
            },
          }));
        }
      },

      // Fetch all health data
      fetchAllHealthData: async (date = new Date()) => {
        await Promise.all([get().fetchStepCount(date), get().fetchHeartRate(date)]);
      },

      // Set step goal
      setStepGoal: async (goal: number) => {
        try {
          const success = await healthService.setStepCountGoal(goal);
          if (success) {
            set(state => ({
              steps: {
                ...state.steps,
                goal,
              },
            }));
          }
        } catch (error) {
          console.error('Error setting step goal:', error);
        }
      },

      // Check if health permissions are granted
      hasPermissions: async () => {
        try {
          return await healthService.hasPermissions();
        } catch (error) {
          console.error('Error checking health permissions:', error);
          return false;
        }
      },

      // Request health permissions
      requestPermissions: async () => {
        try {
          return await healthService.requestPermissions();
        } catch (error) {
          console.error('Error requesting health permissions:', error);
          return false;
        }
      },
    }),
    {
      name: 'app-health',
      storage: createJSONStorage(() => mmkvStorage),
    },
  ),
);

// Export the store
export default useHealthStore;
