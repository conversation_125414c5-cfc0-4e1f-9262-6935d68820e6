import {useQuery, useMutation, useQueryClient} from '@tanstack/react-query';
import api, {handleApiError} from '../../services/api';

// Types
interface User {
  id: string;
  name: string;
  email: string;
  // Add other user properties
}

interface CreateUserData {
  name: string;
  email: string;
  password: string;
  // Add other required fields
}

interface UpdateUserData {
  name?: string;
  email?: string;
  // Add other fields that can be updated
}

// Query keys
export const userKeys = {
  all: ['users'] as const,
  lists: () => [...userKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...userKeys.lists(), {filters}] as const,
  details: () => [...userKeys.all, 'detail'] as const,
  detail: (id: string) => [...userKeys.details(), id] as const,
};

// Get all users
export const useUsers = (filters: Record<string, unknown> = {}) => {
  return useQuery({
    queryKey: userKeys.list(filters),
    queryFn: async () => {
      try {
        const response = await api.get('/users', {params: filters});
        return response.data as User[];
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
  });
};

// Get a single user by ID
export const useUser = (id: string) => {
  return useQuery({
    queryKey: userKeys.detail(id),
    queryFn: async () => {
      try {
        const response = await api.get(`/users/${id}`);
        return response.data as User;
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
    // Enable the query only if we have an ID
    enabled: !!id,
  });
};

// Create a new user
export const useCreateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userData: CreateUserData) => {
      try {
        const response = await api.post('/users', userData);
        return response.data as User;
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
    // When a user is successfully created, invalidate the users list query
    onSuccess: () => {
      queryClient.invalidateQueries({queryKey: userKeys.lists()});
    },
  });
};

// Update a user
export const useUpdateUser = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userData: UpdateUserData) => {
      try {
        const response = await api.put(`/users/${id}`, userData);
        return response.data as User;
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
    // When a user is successfully updated, invalidate both the list and the specific user
    onSuccess: updatedUser => {
      queryClient.invalidateQueries({queryKey: userKeys.lists()});
      queryClient.invalidateQueries({queryKey: userKeys.detail(id)});

      // Optionally update the cache directly for immediate UI updates
      queryClient.setQueryData(userKeys.detail(id), updatedUser);
    },
  });
};

// Delete a user
export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        await api.delete(`/users/${id}`);
        return id;
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
    // When a user is successfully deleted, invalidate the users list query
    onSuccess: deletedId => {
      queryClient.invalidateQueries({queryKey: userKeys.lists()});
      // Remove the deleted user from the cache
      queryClient.removeQueries({queryKey: userKeys.detail(deletedId)});
    },
  });
};

export const useRandomName = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: object) => {
      try {
        const response = await api.post('/get-random-names', data);
        return response.data;
      } catch (error) {
        throw new Error(handleApiError(error));
      }
    },
    // When a user is successfully deleted, invalidate the users list query
    onSuccess: responseData => {
      queryClient.setQueryData(['randomName'], responseData);
    },
    onError: (error: Error) => {
      throw new Error(handleApiError(error));
    },
  });
};
