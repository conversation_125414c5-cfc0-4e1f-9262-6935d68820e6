import {useThemeStore} from '@/store/themeStore';
import React, {useRef, useState} from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {createStyles} from './styles';
import {Controller, SubmitHandler, useForm} from 'react-hook-form';
import {CInput, Icon} from '@/components';
import {useNavigation} from '@react-navigation/native';
import Typography from '@/components/Typography';
import * as yup from 'yup';
import {yupResolver} from '@hookform/resolvers/yup';
import {FONT_SIZE} from '@/utils/fonts';
import PillLabel from '@/components/PillLabel';
import useTranslation from '@/hooks/useTranslation';

interface FormData {
  className: string;
  classDescription: string;
  NumberOfStudents: string;
}

const PostAnAd = (props: any) => {
  const {onClose} = props;
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation();

  const classNameInputRef = useRef<TextInput>(null);
  const classDescriptionInputRef = useRef<TextInput>(null);
  const numberOfStudentsInputRef = useRef<TextInput>(null);

  const {t} = useTranslation();

  const options = [
    {id: '1', label: t('postAnAdScreen.preview')},
    {id: '2', label: t('postAnAdScreen.post')},
  ];

  const [selectedOptions, setSelectedOptions] = useState<string>();

  const schema = yup.object({
    className: yup.string().required(t('postAnAdScreen.classRequired')),
    classDescription: yup.string().required(t('postAnAdScreen.classDescriptionRequired')),
    NumberOfStudents: yup.number().required(t('postAnAdScreen.classRequired')),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm<FormData>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      className: '',
      classDescription: '',
      NumberOfStudents: '',
    },
  });

  const onSubmit: SubmitHandler<FormData> = data => {
    console.log('Form data:', data);
  };

  const toggleOption = (option: string) => {
    console.log('option=====>>>>>', option);
    setSelectedOptions(option);
    handleSubmit(onSubmit);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardAvoidingView}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}>
        <View style={styles.headerContainer}>
          <TouchableOpacity onPress={onClose}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
          <Typography variant="invitePlayersTitle" color={theme.colors.white}>
            {t('postAnAdScreen.title')}
          </Typography>
        </View>
        <View style={styles.titleContainer}>
          <Typography variant="invitePlayersTitle" color={theme.colors.white}>
            {t('postAnAdScreen.classListing')}
          </Typography>
          <TouchableOpacity activeOpacity={0.8}>
            <Typography
              variant="invitePlayersTitle"
              color={theme.colors.white}
              style={{fontSize: FONT_SIZE.xxl}}>
              {t('postAnAdScreen.saveDraft')}
            </Typography>
          </TouchableOpacity>
        </View>
        <View style={styles.inputContainer}>
          <Controller
            control={control}
            name="className"
            render={({field: {onChange, onBlur, value}}) => (
              <CInput
                label={t('postAnAdScreen.className')}
                showLabel={true}
                labelStyle={styles.label}
                placeholder={t('postAnAdScreen.classNamePlaceholder')}
                placeholderTextColor={theme.colors.placeholder}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                hasError={!!errors.className}
                error={errors.className?.message}
                inputStyle={styles.input}
                ref={classNameInputRef}
                returnKeyType="next"
                onSubmitEditing={() => classNameInputRef.current?.focus()}
                blurOnSubmit={false}
                cursorColor={theme.colors.black}
              />
            )}
          />
        </View>

        <View style={styles.inputContainer}>
          <Controller
            control={control}
            name="classDescription"
            render={({field: {onChange, onBlur, value}}) => (
              <View style={styles.birthYearWrapper}>
                <CInput
                  multiline={true}
                  style={styles.textArea}
                  numberOfLines={10}
                  label={t('postAnAdScreen.classDescription')}
                  showLabel={true}
                  labelStyle={styles.label}
                  placeholder={t('postAnAdScreen.classDescriptionPlaceholder')}
                  placeholderTextColor={theme.colors.placeholder}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  hasError={!!errors.classDescription}
                  error={errors.classDescription?.message}
                  inputStyle={{...styles.input, textAlignVertical: 'top'}}
                  containerStyle={{flex: 1}}
                  ref={classDescriptionInputRef}
                  returnKeyType="next"
                  onSubmitEditing={() => numberOfStudentsInputRef.current?.focus()}
                  blurOnSubmit={false}
                  cursorColor={theme.colors.black}
                />
              </View>
            )}
          />
          <TouchableOpacity activeOpacity={0.8} style={styles.selectDateContainer}>
            <Typography variant="dateTimeFrequency" color={theme.colors.white}>
              {t('postAnAdScreen.selectDate')}
            </Typography>
          </TouchableOpacity>
        </View>

        <View style={styles.inputContainer}>
          <Controller
            control={control}
            name="NumberOfStudents"
            render={({field: {onChange, onBlur, value}}) => (
              <CInput
                label={t('postAnAdScreen.maximumStudents')}
                showLabel={true}
                labelStyle={styles.label}
                placeholder={t('postAnAdScreen.numberPlaceholder')}
                placeholderTextColor={theme.colors.placeholder}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                hasError={!!errors.NumberOfStudents}
                error={errors.NumberOfStudents?.message}
                keyboardType="numeric"
                autoCapitalize="none"
                inputStyle={styles.input}
                ref={numberOfStudentsInputRef}
                returnKeyType="done"
                blurOnSubmit={false}
                cursorColor={theme.colors.black}
                onSubmitEditing={handleSubmit(onSubmit)}
              />
            )}
          />
        </View>

        <View style={styles.btnMain}>
          {options?.map(data => (
            <PillLabel
              key={data.id}
              textStyle={{
                fontWeight: '700',
                fontSize: FONT_SIZE.xxl,
              }}
              label={data.label}
              backgroundColor={
                selectedOptions === data?.label ? theme.colors.activeColor : theme.colors.dimGray
              }
              textColor={selectedOptions === data?.label ? theme.colors.black : theme.colors.text}
              onPress={handleSubmit(onSubmit)}
              triangle={false}
              containerStyle={styles.pillContainer}
            />
          ))}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default PostAnAd;
