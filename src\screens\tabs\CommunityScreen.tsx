import React, {useState, useCallback, useEffect, lazy, Suspense, useMemo, useRef} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View, StyleSheet, TouchableOpacity, ImageBackground, ActivityIndicator} from 'react-native';
import {Images} from '@/config/images';
import DraggableFlatList, {
  ScaleDecorator,
  RenderItemParams,
  OpacityDecorator,
} from 'react-native-draggable-flatlist';
import Animated, {Layout} from 'react-native-reanimated';
import {Header, SafeAreaView} from '@/components/common';
import Typography from '@/components/Typography';
import {Icons} from '@/config/icons';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import Tabs from '@/components/Tabs';
import {CImage, CustomMapView} from '@/components';
import PlayerListFilter from '@/components/PlayerListFilter';
import {clearImageCache} from '@/components/CImage';
import {handleTextureError, cleanMemoryIfNeeded} from '@/utils/memoryManager';
import {mapPins} from '@/data';
import {DEFAULT_REGION, getCurrentRegion} from '@/utils/locationService';
import {communityHomeList, communityTabs} from '@/config/staticData';
import {OfferBanner} from '@/components/common/OfferBanner';
import PlayerConnectScreen, {PlayerConnectScreenRef} from './PlayerConnectScreen';
import {useTranslationContext} from '@/context/TranslationContext';
import {useConfigStore} from '@/store';

// Lazy load components to improve performance
const GoLife = lazy(() => import('@/components/Community/GoLife'));
const Reviews = lazy(() => import('@/components/Community/Reviews'));
const GoStream = lazy(() => import('@/components/Community/GoStream'));
const UpYourGame = lazy(() => import('@/components/UpYourGame'));
const PlayerList = lazy(() => import('@/components/PlayerList'));
const Groups = lazy(() => import('@/components/Community/Groups'));

// Define valid icon type names from the Icons object
type IconType = keyof typeof Icons;

// Define the navigation param list to match CommunityStackParamList
type CommunityStackParamList = {
  CommunityHome: undefined;
  PlayerConnectScreen: undefined;
};

// Create a type for the navigation prop
type NavigationProp = NativeStackNavigationProp<CommunityStackParamList>;

interface NavigationOption {
  id: number;
  icon: string;
  iconType: IconType;
  title: string;
  description: string;
  color?: string;
  navigate: () => void;
  source?: any;
  screenName?: string;
}

interface FilterState {
  sport: string;
  dayType: string;
  showCertified: boolean;
  bookingData: {
    startDate: Date | null;
    endDate: Date | null;
    startTime: string | null;
    endTime: string | null;
  };
}

const CommunityScreen = ({route}: {route: any}) => {
  const {t} = useTranslationContext();
  const params = route?.params || {};
  const theme = useThemeStore();
  const {communityFeatures} = useConfigStore();

  const navigation = useNavigation<NavigationProp>();
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    sport: '',
    dayType: '',
    showCertified: false,
    bookingData: {
      startDate: null,
      endDate: null,
      startTime: null,
      endTime: null,
    },
  });
  const [initialRegion, setInitialRegion] = useState(DEFAULT_REGION);
  const [routeType, setRouteType] = useState<string | null>(params?.type || null);

  // Constants
  const [activeTab, setActiveTab] = useState(
    communityFeatures && communityFeatures.length > 0
      ? t(communityFeatures[0].label)
      : t(communityTabs[0].label),
  );

  // Navigation options configuration
  const [navigationOptions, setNavigationOptions] = useState<NavigationOption[]>(
    communityHomeList as NavigationOption[],
  );

  const communityTabsArray = useMemo(
    () => (communityFeatures && communityFeatures.length > 0 ? communityFeatures : communityTabs),
    [],
  );

  // Create a ref for PlayerConnectScreen
  const playerConnectRef = useRef<PlayerConnectScreenRef>(null);

  // Effects
  useEffect(() => {
    if (params?.type) {
      setRouteType(params.type);
    }
  }, [params]);

  useEffect(() => {
    if (routeType === 'playerConnect') {
      // Find the UpYourGame tab
      const upYourGameTab = communityTabsArray.find(
        tab => t(tab.label) === t('CommunityScreen.upYourGame'),
      );
      if (upYourGameTab) {
        setActiveTab(t(upYourGameTab.label));
      }
    } else if (routeType === 'groups') {
      // Find the Groups tab
      const groupsTab = communityTabsArray.find(
        tab => t(tab.label) === t('CommunityScreen.groups'),
      );
      if (groupsTab) {
        setActiveTab(t(groupsTab.label));
      }
    }
  }, [routeType, communityTabsArray, t]);

  // Event handlers
  // Add loading state for tab content
  const [isTabContentLoading, setIsTabContentLoading] = useState(false);

  // Handle tab switching with memory management
  const handleTabPress = (tab: string) => {
    console.log('tab', tab);
    try {
      if (routeType) {
        setRouteType(null);
      }

      // If switching to GoStream (which is texture-intensive), clean memory first
      if (tab === t('CommunityScreen.goStream')) {
        setIsTabContentLoading(true);

        // Clean memory before loading GoStream
        cleanMemoryIfNeeded(true);

        // Small delay to ensure memory is cleaned before loading the component
        setTimeout(() => {
          setActiveTab(tab);
          setIsTabContentLoading(false);
        }, 100);
      } else {
        // For other tabs, just switch normally
        setActiveTab(tab);
      }
    } catch (error) {
      console.error('Error switching tabs:', error);
      handleTextureError();
      setIsTabContentLoading(false);
    }
  };

  const handleDragEnd = useCallback(({data}: {data: NavigationOption[]}) => {
    setNavigationOptions(data);
  }, []);

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const handleApplyFilters = (newFilters: FilterState) => {
    setFilters(newFilters);
  };

  // Clean up resources when component unmounts
  useEffect(() => {
    return () => {
      // Clear image cache when leaving the screen
      clearImageCache();
      // Force clean memory
      cleanMemoryIfNeeded(true);
    };
  }, []);
  // Get user's current location when component mounts
  useEffect(() => {
    const fetchUserLocation = async () => {
      try {
        const region = await getCurrentRegion();
        console.log('Initial region:', region);
        setInitialRegion(region);
      } catch (error) {
        console.error('Error getting user location:', error);
        // Keep the default region if there's an error
      }
    };

    fetchUserLocation();
  }, []);

  const renderItem = useCallback(
    ({item, drag, isActive}: RenderItemParams<NavigationOption>) => {
      const IconComponent = Icons[item.iconType];

      return (
        <ScaleDecorator activeScale={1.02}>
          <OpacityDecorator activeOpacity={0.7}>
            <Animated.View
              style={[
                styles(theme).draggableContainer,
                isActive && {
                  shadowColor: '#000',
                  shadowOpacity: 0.3,
                  shadowRadius: 5,
                  shadowOffset: {width: 0, height: 2},
                },
              ]}
              layout={Layout.springify().damping(20).stiffness(200)}>
              <TouchableOpacity onLongPress={drag} disabled={isActive} activeOpacity={1}>
                <View style={styles(theme).dragHandle}>
                  <View style={styles(theme).dragBar} />
                </View>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onLongPress={drag}
                  onPress={() => {
                    if (item.screenName) {
                      setActiveTab(t(item.screenName));
                    } else {
                      item.navigate();
                    }
                  }}
                  style={styles(theme).navigationOption}
                  disabled={isActive}>
                  {item?.source ? (
                    <CImage
                      source={item.source}
                      style={styles(theme).iconContainer}
                      resizeMode="contain"
                    />
                  ) : (
                    <View style={[styles(theme).iconContainer]}>
                      <IconComponent
                        name={item.icon}
                        size={24}
                        color={item.color || theme.colors.text}
                      />
                    </View>
                  )}
                  <View style={styles(theme).textContainer}>
                    <Typography
                      variant="subtitle"
                      color={item.color || theme.colors.white}
                      style={styles(theme).title}>
                      {item.title}
                    </Typography>
                    <Typography
                      variant="bodyMedium1"
                      color={theme.colors.white}
                      style={styles(theme).description}>
                      {item.description}
                    </Typography>
                  </View>
                </TouchableOpacity>
              </TouchableOpacity>
            </Animated.View>
          </OpacityDecorator>
        </ScaleDecorator>
      );
    },
    [theme],
  );

  // Handle marker press on the map
  const handleMarkerPress = useCallback((marker: any) => {
    console.log('marker', marker);
    // Call the handleMarkerPress method exposed by the PlayerConnectScreen ref
    if (playerConnectRef.current) {
      playerConnectRef.current.handleMarkerPress(marker);
    }
  }, []);

  // This function is not used, but we'll keep it updated for reference
  const renderContent = () => {
    switch (activeTab) {
      case 'GoLife':
        return <GoLife />;
      case 'Reviews':
        return <Reviews />;
      case 'GoStream':
        return <GoStream />;
      case 'UpYourGame':
        return routeType === 'playerConnect' ? (
          <PlayerList onFilterPress={() => setIsFilterVisible(true)} />
        ) : (
          <UpYourGame />
        );
      case 'Groups':
        return <Groups />;
      default:
        return (
          <View style={styles(theme).listContainer}>
            <DraggableFlatList
              data={navigationOptions}
              onDragEnd={handleDragEnd}
              keyExtractor={item => item.id.toString()}
              renderItem={renderItem}
              contentContainerStyle={styles(theme).scrollContent}
              autoscrollSpeed={100}
              activationDistance={10}
              dragHitSlop={{top: 10, bottom: 10, left: 0, right: 0}}
            />

            <OfferBanner text="Invite friends, get 10% off" style={styles(theme).footer} />
          </View>
        );
    }
  };

  const getTitle = () => {
    // Find the tab object that matches the active tab title
    const activeTabObj = communityTabsArray.find(tab => t(tab.label) === activeTab);
    // Return the title, or default to 'Home' if not found
    return activeTabObj ? t(activeTabObj.label) : t('CommunityScreen.home');
  };

  return (
    <ImageBackground source={Images.gradientBg} style={styles(theme).backgroundImage}>
      <SafeAreaView includeBottom={false} style={styles(theme).container}>
        {activeTab === t('CommunityScreen.playerConnect') && (
          <View style={styles(theme).mapContainer}>
            <CustomMapView
              pins={mapPins}
              initialRegion={initialRegion}
              showUserLocation={true}
              markerType="player"
              onMarkerPress={handleMarkerPress}
            />
          </View>
        )}
        <Header
          leftIcon={{
            name: 'side-menu',
            size: 24,
            color: theme.colors.white,
            containerStyle: {backgroundColor: theme.colors.primary},
          }}
          leftIconButtonStyle={styles(theme).menuButton}
          onLeftPress={openDrawer}
          rightIcons={[
            {name: 'notification', size: 24, badge: 0},
            {name: 'chat', size: 24, badge: 14},
          ]}
          title={getTitle()}
          backgroundColor="transparent"
        />

        <View style={styles(theme).contentContainer}>
          <Tabs
            variant="square"
            type="inner"
            tabs={communityFeatures?.filter(tab => tab.isVisible).map(tab => t(tab.label)) ?? []}
            activeTab={activeTab}
            onTabPress={handleTabPress}
            tabStyle={{paddingVertical: 4, minWidth: 140, alignItems: 'center'}}
          />
          {isTabContentLoading ? (
            <View style={styles(theme).loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Typography variant="body" style={{marginTop: 10}} color={theme.colors.white}>
                Loading content...
              </Typography>
            </View>
          ) : activeTab === t('CommunityScreen.playerConnect') ? (
            <PlayerConnectScreen ref={playerConnectRef} />
          ) : activeTab === t('CommunityScreen.goLife') ? (
            <Suspense
              fallback={
                <View style={styles(theme).loadingContainer}>
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                </View>
              }>
              <GoLife />
            </Suspense>
          ) : activeTab === t('CommunityScreen.reviews') ? (
            <Suspense
              fallback={
                <View style={styles(theme).loadingContainer}>
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                </View>
              }>
              <Reviews />
            </Suspense>
          ) : activeTab === t('CommunityScreen.goStream') ? (
            <Suspense
              fallback={
                <View style={styles(theme).loadingContainer}>
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                </View>
              }>
              <GoStream />
            </Suspense>
          ) : activeTab === t('CommunityScreen.upYourGame') ? (
            routeType === 'playerConnect' ? (
              <Suspense
                fallback={
                  <View style={styles(theme).loadingContainer}>
                    <ActivityIndicator size="large" color={theme.colors.primary} />
                  </View>
                }>
                <PlayerList onFilterPress={() => setIsFilterVisible(true)} />
                <PlayerListFilter
                  isVisible={isFilterVisible}
                  onClose={() => setIsFilterVisible(false)}
                  onApply={handleApplyFilters}
                  initialFilters={filters}
                />
              </Suspense>
            ) : (
              <Suspense
                fallback={
                  <View style={styles(theme).loadingContainer}>
                    <ActivityIndicator size="large" color={theme.colors.primary} />
                  </View>
                }>
                <UpYourGame />
              </Suspense>
            )
          ) : activeTab === t('CommunityScreen.groups') ? (
            <Suspense
              fallback={
                <View style={styles(theme).loadingContainer}>
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                </View>
              }>
              <Groups />
            </Suspense>
          ) : (
            <View style={styles(theme).listContainer}>
              <DraggableFlatList
                data={navigationOptions}
                onDragEnd={handleDragEnd}
                keyExtractor={item => item.id.toString()}
                renderItem={renderItem}
                contentContainerStyle={styles(theme).scrollContent}
                autoscrollSpeed={100}
                activationDistance={10}
                dragHitSlop={{top: 10, bottom: 10, left: 0, right: 0}}
              />
              <OfferBanner text="Invite friends, get 10% off" style={styles(theme).footer} />
            </View>
          )}
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    mapContainer: {
      flex: 1,
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },

    container: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    contentContainer: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: 'transparent',
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },

    listContainer: {
      flex: 1,
      zIndex: 0,
      backgroundColor: 'transparent',
    },
    scrollContent: {
      padding: 16,
      paddingBottom: 60,
      backgroundColor: 'transparent',
    },
    draggableContainer: {
      marginBottom: 14,
      backgroundColor: theme.colors.TranslucentWhite,
      borderRadius: 10,
      overflow: 'hidden',
    },
    dragHandle: {
      height: 5,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
    },
    dragBar: {
      width: 40,
      height: 4,
      borderRadius: 2,
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
    },
    navigationOption: {
      flexDirection: 'row',
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderTopRightRadius: 0,
      borderTopLeftRadius: 0,
      borderRadius: 4,
      paddingHorizontal: 10,
      paddingTop: 10,
      paddingBottom: 12,
      alignItems: 'center',
    },
    iconContainer: {
      width: 50,
      height: 50,
      borderRadius: 10,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 16,
    },
    textContainer: {
      flex: 1,
    },
    title: {
      marginBottom: 4,
    },
    description: {
      opacity: 0.8,
    },
    footer: {
      paddingVertical: 4,
      backgroundColor: theme.colors.primary,
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      zIndex: 1,
    },

    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'transparent',
    },
  });

export default CommunityScreen;
