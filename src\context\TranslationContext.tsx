import React, {createContext, useContext, ReactNode, useMemo, useEffect} from 'react';
import {useTranslation} from '@/hooks/useTranslation';
import {SupportedLanguage} from '@/store/i18nStore';

// Define the context type
interface TranslationContextType {
  t: (key: string, params?: Record<string, string | number>) => string;
  currentLanguage: SupportedLanguage;
  switchLanguage: (lang: SupportedLanguage) => Promise<boolean>;
  availableLanguages: {code: string; name: string}[];
  isRTL: boolean;
}

// Create the context with default values
const TranslationContext = createContext<TranslationContextType | undefined>(undefined);

// Provider component
export const TranslationProvider: React.FC<{children: ReactNode}> = ({children}) => {
  const {t, currentLanguage, switchLanguage, availableLanguages, isRTL} = useTranslation();

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      t,
      currentLanguage,
      switchLanguage,
      availableLanguages,
      isRTL,
    }),
    [t, currentLanguage, switchLanguage, availableLanguages, isRTL],
  );

  return <TranslationContext.Provider value={contextValue}>{children}</TranslationContext.Provider>;
};

// Custom hook to use the translation context
export const useTranslationContext = () => {
  const context = useContext(TranslationContext);
  if (context === undefined) {
    throw new Error('useTranslationContext must be used within a TranslationProvider');
  }
  return context;
};

export default TranslationProvider;
