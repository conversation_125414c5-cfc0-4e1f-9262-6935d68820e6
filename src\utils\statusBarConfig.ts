import {StatusBar, Platform} from 'react-native';

/**
 * Configure status bar for dark screens (black background)
 */
export const setDarkStatusBar = () => {
  StatusBar.setBarStyle('light-content');
  if (Platform.OS === 'android') {
    StatusBar.setBackgroundColor('#000000');
  }
};

/**
 * Configure status bar for light screens (white/light background)
 */
export const setLightStatusBar = () => {
  StatusBar.setBarStyle('dark-content');
  if (Platform.OS === 'android') {
    StatusBar.setBackgroundColor('#FFFFFF');
  }
};

/**
 * Configure status bar with custom color
 */
export const setCustomStatusBar = (
  backgroundColor: string,
  barStyle: 'light-content' | 'dark-content' | 'default',
) => {
  StatusBar.setBarStyle(barStyle);
  if (Platform.OS === 'android') {
    StatusBar.setBackgroundColor(backgroundColor);
  }
};

/**
 * Hide the status bar
 * @param hidden - Whether to hide the status bar
 */
export const setStatusBarHidden = (hidden: boolean) => {
  StatusBar.setHidden(hidden);
};
