import React, {useState, useCallback, useRef, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StatusBar,
  Platform,
  Alert,
  PermissionsAndroid,
  Linking,
  ActivityIndicator,
} from 'react-native';
import {
  GiftedChat,
  Bubble,
  InputToolbar,
  BubbleProps,
  InputToolbarProps,
  IMessage,
  MessageProps,
  Send,
  SendProps,
} from 'react-native-gifted-chat';
import {CustomModal, Icon, SafeAreaView} from '@/components';
import {useThemeStore} from '@/store';
import createStyles from './styles';
import {useNavigation} from '@react-navigation/native';
import Typography from '@/components/Typography';
import {Icons} from '@/config/icons';
import {launchCamera, launchImageLibrary, CameraOptions} from 'react-native-image-picker';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
const audioRecorderPlayer = new AudioRecorderPlayer();

const userTom = {
  _id: 1,
  name: '<PERSON>',
  avatar: require('@/assets/images/profile2.webp'),
};
const userChandra = {
  _id: 2,
  name: '<PERSON>',
  avatar: require('@/assets/images/profile3.webp'),
};
const userRose = {
  _id: 3,
  name: 'Alex',
  avatar: require('@/assets/images/profile1.webp'),
};

// Extend IMessage type to include audio properties
interface AudioMessage extends IMessage {
  audio?: string;
  audioLength?: number;
}

const initialMessages = [
  {
    _id: 1,
    text: 'Hey everyone! Check out this photo from our last game.',
    createdAt: new Date(),
    user: userTom,
    image: 'https://picsum.photos/400/300',
  },
  {
    _id: 2,
    text: "Great shot! Here's a quick voice message about our next practice.",
    createdAt: new Date(),
    user: userRose,
    audioLength: 60,
  },
  {
    _id: 3,
    text: 'Aliquam elit augue, tincidunt vitae orci eu, ornare vehicula felis. Morbi scelerisque diam at eros semper consectetur. Phasellus porttitor nisl erat.',
    createdAt: new Date(),
    user: userChandra,
  },
  {
    _id: 4,
    text: '',
    createdAt: new Date(),
    user: userTom,
    image: 'https://picsum.photos/400/400',
  },
  {
    _id: 5,
    text: "Here's a quick update about the tournament schedule.",
    createdAt: new Date(),
    user: userRose,
    audioLength: 45,
  },
  {
    _id: 6,
    text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    createdAt: new Date(),
    user: userChandra,
  },
  {
    _id: 7,
    text: '',
    createdAt: new Date(),
    user: userTom,
    image: 'https://picsum.photos/400/500',
  },
  {
    _id: 8,
    text: 'Aliquam elit augue, tincidunt vitae orci eu, ornare vehicula felis.',
    createdAt: new Date(),
    user: userChandra,
  },
  {
    _id: 9,
    text: "Quick reminder about tomorrow's practice.",
    createdAt: new Date(),
    user: userRose,
    audioLength: 30,
  },
];

// AudioMessagePlayer component for audio message UI and playback
interface AudioMessagePlayerProps {
  audio: string;
  audioLength?: number;
  currentlyPlayingAudio: string | null;
  setCurrentlyPlayingAudio: (audio: string | null) => void;
}

const AudioMessagePlayer = ({
  audio,
  audioLength,
  currentlyPlayingAudio,
  setCurrentlyPlayingAudio,
}: AudioMessagePlayerProps) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const audioRecorderPlayer = useRef<AudioRecorderPlayer | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackTime, setPlaybackTime] = useState(0);
  const [duration, setDuration] = useState(0);

  useEffect(() => {
    if (!audioRecorderPlayer.current) {
      audioRecorderPlayer.current = new AudioRecorderPlayer();
    }
    return () => {
      if (audioRecorderPlayer.current) {
        audioRecorderPlayer.current.stopPlayer();
        audioRecorderPlayer.current.removePlayBackListener();
      }
    };
  }, []);

  // Stop playback if another audio starts
  useEffect(() => {
    if (currentlyPlayingAudio !== audio && isPlaying) {
      // Stop this audio
      if (audioRecorderPlayer.current) {
        audioRecorderPlayer.current.stopPlayer();
        audioRecorderPlayer.current.removePlayBackListener();
      }
      setIsPlaying(false);
      setPlaybackTime(0);
    }
  }, [currentlyPlayingAudio]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const onPlayPausePress = async () => {
    if (!audio) return;
    if (!audioRecorderPlayer.current) return;
    if (isPlaying) {
      await audioRecorderPlayer.current.stopPlayer();
      audioRecorderPlayer.current.removePlayBackListener();
      setIsPlaying(false);
      setPlaybackTime(0);
      setCurrentlyPlayingAudio(null);
      return;
    }
    setCurrentlyPlayingAudio(audio);
    setIsPlaying(true);
    await audioRecorderPlayer.current.startPlayer(audio);
    audioRecorderPlayer.current.addPlayBackListener((e: any) => {
      setPlaybackTime(Math.floor(e.currentPosition / 1000));
      setDuration(Math.floor(e.duration / 1000));
      if (e.currentPosition >= e.duration) {
        setIsPlaying(false);
        setPlaybackTime(0);
        audioRecorderPlayer.current?.stopPlayer();
        audioRecorderPlayer.current?.removePlayBackListener();
        setCurrentlyPlayingAudio(null);
      }
    });
  };

  const totalDurationSec = audioLength && audioLength > 0 ? Math.floor(audioLength) : duration;
  const currentTime = isPlaying ? playbackTime : 0;

  return (
    <View style={[styles.audioContainer, {flexDirection: 'row', alignItems: 'center'}]}>
      <TouchableOpacity onPress={onPlayPausePress} style={{marginRight: 8}}>
        <Icons.Feather name={isPlaying ? 'pause' : 'play'} size={28} color={theme.colors.white} />
      </TouchableOpacity>
      <Text style={styles.audioDuration}>
        {formatTime(currentTime)} / {formatTime(totalDurationSec)}
      </Text>
    </View>
  );
};

const ChatScreen = () => {
  const [messages, setMessages] = useState<AudioMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pendingImage, setPendingImage] = useState<string | null>(null);
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<any>();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null);
  const recordingTimer = useRef<NodeJS.Timeout | null>(null);
  const lastSecondRef = useRef<number>(0);
  const [selectedMessageId, setSelectedMessageId] = useState<string | number | null>(null);
  const [reactions, setReactions] = useState<{[key: string]: string}>({});

  useEffect(() => {
    // Simulate loading messages
    const loadMessages = async () => {
      try {
        // Add a small delay to simulate loading
        await new Promise(resolve => setTimeout(resolve, 500));
        setMessages(initialMessages);
      } catch (error) {
        console.error('Error loading messages:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadMessages();
  }, []);

  useEffect(() => {
    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
      stopRecording();
      stopPlaying();
    };
  }, []);

  const handleCameraPress = () => {
    setIsModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };

  const requestAndroidPermissions = async () => {
    try {
      const cameraGranted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.CAMERA,
        {
          title: 'Camera Permission',
          message: 'App needs access to your camera to take photos',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );

      const storageGranted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: 'Storage Permission',
          message: 'App needs access to your storage to save photos',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );

      if (
        cameraGranted === PermissionsAndroid.RESULTS.GRANTED &&
        storageGranted === PermissionsAndroid.RESULTS.GRANTED
      ) {
        return true;
      }

      if (
        cameraGranted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN ||
        storageGranted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN
      ) {
        Alert.alert(
          'Permission Required',
          'Camera and storage permissions are required. Please enable them in settings.',
          [
            {text: 'Cancel', style: 'cancel'},
            {
              text: 'Open Settings',
              onPress: () => {
                Linking.openSettings();
              },
            },
          ],
        );
        return false;
      }

      Alert.alert(
        'Permission Required',
        'Camera and storage permissions are required to take photos',
        [{text: 'OK'}],
      );
      return false;
    } catch (err) {
      console.warn('Permission request error:', err);
      return false;
    }
  };

  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      return requestAndroidPermissions();
    }
    return true;
  };

  const handleTakePhoto = async () => {
    handleCloseModal();

    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      return;
    }

    const options: CameraOptions = {
      mediaType: 'photo',
      quality: 0.8,
      includeBase64: true,
      saveToPhotos: true,
      cameraType: 'back',
      presentationStyle: 'fullScreen',
      includeExtra: true,
      maxHeight: 1920,
      maxWidth: 1080,
    };

    try {
      const result = await launchCamera(options);

      if (result.didCancel) {
        console.log('User cancelled camera');
        return;
      }

      if (result.errorCode) {
        let errorMessage = 'Failed to take photo. ';
        switch (result.errorCode) {
          case 'camera_unavailable':
            errorMessage += 'Camera is not available on this device.';
            break;
          case 'permission':
            errorMessage += 'Camera permission was denied.';
            break;
          case 'others':
            errorMessage += result.errorMessage || 'Please try again.';
            break;
          default:
            errorMessage += 'Please try again.';
        }

        Alert.alert('Error', errorMessage);
        return;
      }

      if (!result.assets || result.assets.length === 0) {
        console.error('No assets in camera result');
        Alert.alert('Error', 'No photo was captured. Please try again.');
        return;
      }

      const asset = result.assets[0];
      if (!asset.uri) {
        console.error('No URI in camera result asset');
        Alert.alert('Error', 'Failed to save photo. Please try again.');
        return;
      }

      // Set the pending image instead of sending immediately
      setPendingImage(asset.uri);
    } catch (err) {
      const error = err as Error;
      console.error('Camera error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });

      if (error.message?.includes('permission')) {
        Alert.alert(
          'Permission Required',
          'Camera permission is required. Please enable it in settings.',
          [
            {text: 'Cancel', style: 'cancel'},
            {
              text: 'Open Settings',
              onPress: () => {
                Linking.openSettings();
              },
            },
          ],
        );
      } else {
        Alert.alert('Error', 'Failed to take photo. Please try again.');
      }
    }
  };

  const handleChoosePhoto = async () => {
    handleCloseModal();
    try {
      const result = await launchImageLibrary({
        mediaType: 'photo',
        quality: 0.8,
        includeBase64: true,
      });

      if (result.assets && result.assets[0] && result.assets[0].uri) {
        // Set the pending image instead of sending immediately
        setPendingImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error choosing photo:', error);
    }
  };

  const requestAudioPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          {
            title: 'Microphone Permission',
            message: 'App needs access to your microphone to record audio',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn('Microphone permission error:', err);
        return false;
      }
    }
    return true;
  };

  const startRecording = async () => {
    const hasPermission = await requestAudioPermission();
    if (!hasPermission) {
      Alert.alert('Permission Required', 'Microphone permission is required to record audio', [
        {text: 'OK'},
      ]);
      return;
    }

    try {
      setRecordingTime(0); // Reset before starting
      lastSecondRef.current = 0;
      const result = await audioRecorderPlayer.startRecorder();
      audioRecorderPlayer.addRecordBackListener(e => {
        const currentSec = Math.floor(e.currentPosition / 1000);
        if (currentSec !== lastSecondRef.current) {
          setRecordingTime(currentSec);
          lastSecondRef.current = currentSec;
        }
      });
      setIsRecording(true);
      // Start timer
      recordingTimer.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } catch (error) {
      console.error('Failed to start recording:', error);
      Alert.alert('Error', 'Failed to start recording');
    }
  };

  const stopRecording = async () => {
    if (!isRecording) return;

    try {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
      const result = await audioRecorderPlayer.stopRecorder();
      audioRecorderPlayer.removeRecordBackListener();
      setIsRecording(false);

      // Create audio message
      const newMessage: AudioMessage = {
        _id: Date.now(),
        text: '',
        createdAt: new Date(),
        user: userTom,
        audio: result,
        audioLength: recordingTime,
      };
      onSend([newMessage]);
      setRecordingTime(0);
    } catch (error) {
      console.error('Failed to stop recording:', error);
      Alert.alert('Error', 'Failed to stop recording');
    }
  };

  const stopPlaying = async () => {
    try {
      await audioRecorderPlayer.stopPlayer();
      audioRecorderPlayer.removePlayBackListener();
      setCurrentlyPlaying(null);
    } catch (error) {
      console.error('Failed to stop playing:', error);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const onSend = useCallback(
    (newMessages: any[] = []) => {
      if (pendingImage) {
        // If there's a pending image, create a new message with it
        const imageMessage: AudioMessage = {
          _id: Date.now(),
          text: newMessages[0]?.text || '',
          createdAt: new Date(),
          user: userTom,
          image: pendingImage,
        };
        setMessages(previousMessages => GiftedChat.append(previousMessages, [imageMessage]));
        setPendingImage(null); // Clear the pending image
      } else {
        // Normal text message handling
        setMessages(previousMessages => GiftedChat.append(previousMessages, newMessages));
      }
    },
    [pendingImage],
  );

  const renderBubble = (props: BubbleProps<AudioMessage>) => {
    const {currentMessage, position} = props;
    return (
      <View>
        {currentMessage?.user?.name && position === 'left' && (
          <Typography
            variant="chatUserName"
            style={{
              color: theme.colors.primary,
              marginBottom: 4,
            }}>
            {currentMessage.user.name}
          </Typography>
        )}
        <Bubble
          {...props}
          wrapperStyle={{
            right: styles.bubbleRight,
            left: styles.bubbleLeft,
          }}
          textStyle={{
            right: styles.bubbleTextRight,
            left: styles.bubbleTextLeft,
          }}
          renderTime={() => null}
          renderCustomView={() => null}
        />
        {currentMessage?.createdAt && (
          <Text
            style={{
              color: theme.colors.text,
              fontSize: 11,
              marginTop: 2,
              alignSelf: position === 'right' ? 'flex-end' : 'flex-start',
              marginRight: position === 'right' ? 4 : 0,
              marginLeft: position === 'left' ? 4 : 0,
            }}>
            {new Date(currentMessage.createdAt).toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Text>
        )}
      </View>
    );
  };

  const ReactionBar = ({onSelect}: {onSelect: (emoji: string) => void}) => (
    <View
      style={{
        flexDirection: 'row',
        backgroundColor: '#2196F3',
        borderRadius: 20,
        padding: 6,
        marginBottom: 4,
        alignSelf: 'flex-end',
        zIndex: 10,
        position: 'absolute',
        bottom: 0,
        right: 40,
      }}>
      {['😂', '❤️', '🥰', '❗'].map(emoji => (
        <TouchableOpacity key={emoji} onPress={() => onSelect(emoji)} style={{marginHorizontal: 4}}>
          <Text style={{fontSize: 24}}>{emoji}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderMessage = (props: MessageProps<AudioMessage>) => {
    const {currentMessage, position} = props;
    const avatarSource = currentMessage?.user?.avatar;
    let validAvatarSource: number | {uri: string} | undefined;
    if (typeof avatarSource === 'number') {
      validAvatarSource = avatarSource;
    } else if (typeof avatarSource === 'string') {
      validAvatarSource = {uri: avatarSource};
    } else if (
      avatarSource &&
      typeof avatarSource === 'object' &&
      avatarSource !== null &&
      Object.prototype.hasOwnProperty.call(avatarSource, 'uri') &&
      typeof (avatarSource as {uri: unknown}).uri === 'string'
    ) {
      validAvatarSource = {uri: (avatarSource as {uri: string}).uri};
    }

    return (
      <View style={{flexDirection: 'row', alignItems: 'flex-start', padding: 16}}>
        {validAvatarSource && position === 'left' && (
          <Image
            source={validAvatarSource}
            style={{width: 36, height: 36, borderRadius: 18, marginHorizontal: 6, marginTop: 2}}
          />
        )}
        <View style={{flex: 1}}>
          {selectedMessageId === currentMessage?._id && (
            <ReactionBar
              onSelect={emoji => {
                setReactions(prev => ({...prev, [currentMessage._id]: emoji}));
                setSelectedMessageId(null);
              }}
            />
          )}
          {renderBubble(props)}
          {/* Show reaction below the message */}
          {reactions[currentMessage?._id] && (
            <Text
              style={{
                fontSize: 18,
                marginTop: 2,
                alignSelf: position === 'right' ? 'flex-end' : 'flex-start',
              }}>
              {reactions[currentMessage._id]}
            </Text>
          )}
        </View>
      </View>
    );
  };

  const renderAvatar = () => null;

  const renderInputToolbar = (props: InputToolbarProps<AudioMessage>) => (
    <View style={styles.inputToolbarContainer}>
      <View style={{flex: 1}}>
        {pendingImage && (
          <View style={styles.pendingImageContainer}>
            <Image source={{uri: pendingImage}} style={styles.pendingImage} />
            <TouchableOpacity
              style={styles.removeImageButton}
              onPress={() => setPendingImage(null)}>
              <Icons.AntDesign name="closecircle" size={20} color={theme.colors.white} />
            </TouchableOpacity>
          </View>
        )}
        <InputToolbar {...props} containerStyle={styles.inputToolbar} />
      </View>
      <TouchableOpacity
        onPress={isRecording ? stopRecording : startRecording}
        style={[styles.micButton]}>
        {isRecording ? (
          <Icons.AntDesign name="pause" size={30} color={theme.colors.coralRed} />
        ) : (
          <Icon name="mic" size={30} color={theme.colors.white} />
        )}
        {isRecording && <Text style={styles.recordingTime}>{formatTime(recordingTime)}</Text>}
      </TouchableOpacity>
      <TouchableOpacity onPress={handleCameraPress}>
        <Icon name="camera2" size={30} color={theme.colors.white} />
      </TouchableOpacity>
    </View>
  );

  const renderSend = (props: SendProps<AudioMessage>) => {
    return (
      <Send
        {...props}
        containerStyle={{
          justifyContent: 'center',
          alignItems: 'center',
          alignSelf: 'center',
          marginRight: 8,
          marginBottom: 4,
        }}>
        <Icons.Ionicons name="send-sharp" size={25} color={theme.colors.primary} />
      </Send>
    );
  };

  const renderMessageAudio = (
    props: any,
    currentlyPlayingAudio: string | null,
    setCurrentlyPlayingAudio: (audio: string | null) => void,
  ) => {
    const {currentMessage} = props;
    if (!currentMessage.audio) return null;
    return (
      <AudioMessagePlayer
        audio={currentMessage.audio}
        audioLength={currentMessage.audioLength}
        currentlyPlayingAudio={currentlyPlayingAudio}
        setCurrentlyPlayingAudio={setCurrentlyPlayingAudio}
      />
    );
  };

  const renderMessageImage = (props: any) => (
    <Image
      source={{uri: props.currentMessage?.image}}
      style={{width: 150, height: 150, borderRadius: 8, margin: 4}}
      resizeMode="cover"
    />
  );

  return (
    <SafeAreaView
      style={{flex: 1, backgroundColor: theme.colors.background}}
      includeBottom={true}
      includeTop={true}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={theme.colors.background}
        translucent={true}
      />
      <View style={styles.container}>
        {/* Custom Header */}
        <View style={styles.header}>
          <TouchableOpacity
            activeOpacity={0.8}
            style={styles.backButton}
            onPress={() => navigation.goBack()}>
            <Icon name="back" size={24} color="white" />
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.8}
            style={styles.cameraButton}
            onPress={handleCameraPress}>
            <Icon name="camera-1" size={24} color={theme.colors.black} />
          </TouchableOpacity>
          <View style={styles.headerTitleContainer}>
            <Typography variant="chatHeaderTitle" style={styles.headerTitle}>
              Sunset Park Duos
            </Typography>
            <TouchableOpacity activeOpacity={0.8} onPress={() => navigation.navigate('MyGroups')}>
              <Typography variant="chatHeaderSubtitle" style={styles.headerSubtitle}>
                7 members
              </Typography>
            </TouchableOpacity>
          </View>
        </View>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
          </View>
        ) : (
          <GiftedChat
            messages={messages}
            onSend={(messages: any[]) => onSend(messages)}
            user={userTom}
            renderAvatar={renderAvatar}
            renderMessage={renderMessage}
            renderBubble={renderBubble}
            renderInputToolbar={renderInputToolbar}
            renderSend={renderSend}
            renderMessageAudio={props =>
              renderMessageAudio(props, currentlyPlaying, setCurrentlyPlaying)
            }
            renderMessageImage={renderMessageImage}
            placeholder="Type a message..."
            onLongPress={(context, message) => setSelectedMessageId(message._id)}
          />
        )}

        <CustomModal
          variant="bottom"
          visible={isModalVisible}
          onClose={handleCloseModal}
          title="Choose an option"
          showCloseButton={true}>
          <View style={styles.modalContent}>
            <View style={styles.modalButtons}>
              <TouchableOpacity style={styles.modalButton} onPress={handleTakePhoto}>
                <Icon name="camera-1" size={24} color={theme.colors.white} />
                <Typography variant="chatHeaderTitle" style={styles.modalButtonText}>
                  Take Photo
                </Typography>
              </TouchableOpacity>
              <TouchableOpacity style={styles.modalButton} onPress={handleChoosePhoto}>
                <Icons.Entypo name="images" size={24} color={theme.colors.white} />
                <Typography variant="chatHeaderTitle" style={styles.modalButtonText}>
                  Choose from Gallery
                </Typography>
              </TouchableOpacity>
            </View>
          </View>
        </CustomModal>
      </View>
    </SafeAreaView>
  );
};

export default ChatScreen;
