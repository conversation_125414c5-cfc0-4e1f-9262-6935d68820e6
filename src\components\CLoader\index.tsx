import {useThemeStore} from '@/store';
import React from 'react';
import {View, ActivityIndicator, StyleProp, ViewStyle, StyleSheet} from 'react-native';

interface CLoaderProps {
  size?: 'small' | 'large';
  color?: string;
  style?: StyleProp<ViewStyle>;
}

const CLoader: React.FC<CLoaderProps> = ({size = 'large', color = '#288ECE', style}) => {
  const theme = useThemeStore();
  return (
    <View style={[styles(theme).container, style]}>
      <ActivityIndicator size={size} color={color} />
    </View>
  );
};

export default CLoader;

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });
