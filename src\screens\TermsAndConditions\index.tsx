import React, {useRef} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View, Text, StyleSheet, ScrollView, Dimensions, useWindowDimensions} from 'react-native';
import {CButton, SafeAreaView} from '@/components';
import {useQuery} from '@tanstack/react-query';
import api from '@/services/api';
import RenderHtml from 'react-native-render-html';
import CLoader from '@/components/CLoader';
import {useNavigation} from '@react-navigation/native';
import {useTranslationContext} from '@/context/TranslationContext';

interface TermsOfServiceScreenProps {
  onAccept?: () => void;
}

interface ApiResponse {
  data: {
    html_body: string;
    title: string;
  };
}

const TermsAndConditionsScreen: React.FC<TermsOfServiceScreenProps> = ({onAccept}) => {
  const theme = useThemeStore();
  const {width} = useWindowDimensions();
  const scrollViewRef = useRef<ScrollView>(null);
  const navigation = useNavigation();
  const {t} = useTranslationContext();

  const {data, isLoading} = useQuery<ApiResponse>({
    queryKey: ['terms-of-service'],
    queryFn: async () => {
      try {
        const response = await api.get('/terms-of-services');
        if (response.status === 200) {
          return response.data;
        }
      } catch (error) {
        console.error('Error fetching terms of service:', error);
        return null;
      }
    },
  });
  console.log('data', data);

  return (
    <SafeAreaView includeTop style={[styles(theme).container]}>
      <View style={styles(theme).termsContainer}>
        <Text style={styles(theme).title}>{data?.data?.title || t('terms.title')}</Text>

        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={styles(theme).scrollView}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}>
          {isLoading ? (
            <CLoader />
          ) : (
            <RenderHtml contentWidth={width} source={{html: data?.data?.html_body || ''}} />
          )}
        </ScrollView>
      </View>

      <CButton
        title={t('common.close')}
        onPress={() => navigation.goBack()}
        containerStyle={styles(theme).acceptButton}
      />
    </SafeAreaView>
  );
};

const {height} = Dimensions.get('window');

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.background,
    },
    termsContainer: {
      width: '100%',
      height: height * 0.82,
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      overflow: 'hidden',
      paddingHorizontal: 16,
    },
    title: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.black,
      paddingVertical: 15,
    },
    scrollView: {
      flexGrow: 1,
    },
    termsText: {
      fontSize: 14,
      lineHeight: 20,
      color: theme.colors.black,
      paddingBottom: 20,
    },
    boldText: {
      fontWeight: 'bold',
      fontSize: 14,
      color: theme.colors.black,
    },
    acceptButton: {
      width: '100%',
      marginTop: 20,
      marginBottom: 10,
    },
    acceptButtonText: {
      color: theme.colors.white,
      fontWeight: 'bold',
      fontSize: 16,
    },
  });

export default TermsAndConditionsScreen;
