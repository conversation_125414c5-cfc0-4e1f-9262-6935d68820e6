import React, {useState} from 'react';
import {View, Text, StyleSheet, ScrollView, TouchableOpacity} from 'react-native';
import { useThemeStore } from '@/store/themeStore';;
import {Header} from '@/components/common';
import {getGlobalStyles} from '@/utils/styleUtils';

const ExampleWithHeader = () => {
  const theme = useThemeStore();
  const globalStyles = getGlobalStyles({theme});
  const [headerType, setHeaderType] = useState<number>(0);

  // Function to rotate through header examples
  const changeHeaderType = () => {
    setHeaderType(prev => (prev + 1) % 4);
  };

  // Different header configurations based on the images
  const renderHeader = () => {
    switch (headerType) {
      case 0:
        // First header style with hamburger menu and notification + chat icons
        return (
          <Header
            leftIcon="side-menu"
            rightIcons={[
              {name: 'notification', badge: true, badgeColor: theme.colors.activeColor},
              {name: 'chat', badge: 1, badgeColor: theme.colors.activeColor},
            ]}
            onLeftPress={() => console.log('Menu pressed')}
            onRightPress={index => console.log(`Right icon ${index} pressed`)}
          />
        );
      case 1:
        // Second header style with hamburger menu only on dark background
        return (
          <Header
            leftIcon="side-menu"
            rightIcons={[
              {name: 'notification', badge: false},
              {name: 'chat', badge: 1, badgeColor: theme.colors.activeColor},
            ]}
            onLeftPress={() => console.log('Menu pressed')}
            backgroundColor={theme.colors.charcoal}
          />
        );
      case 2:
        // Third header style with X close button only
        return (
          <Header
            leftIcon={{name: 'close', color: theme.colors.error}}
            onLeftPress={() => console.log('Close pressed')}
            backgroundColor={theme.colors.jetBlack}
          />
        );
      case 3:
        // Standard header with title and back button
        return (
          <Header
            title="Example Screen"
            showBackButton
            rightIcons={['settings']}
            onRightPress={() => console.log('Settings pressed')}
          />
        );
      default:
        return null;
    }
  };

  return (
    <View style={[styles.container, {backgroundColor: theme.colors.background}]}>
      {renderHeader()}

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <Text style={[globalStyles.title, styles.title]}>Header Example #{headerType + 1}</Text>
        <Text style={[globalStyles.text, styles.description]}>
          This screen demonstrates how to use the reusable Header component with different
          configurations. The header is flexible enough to support multiple icon styles, badges, and
          layouts.
        </Text>

        <TouchableOpacity
          style={[styles.button, {backgroundColor: theme.colors.primary}]}
          onPress={changeHeaderType}>
          <Text style={[styles.buttonText, {color: theme.colors.white}]}>
            Show Next Header Style
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingTop: 24,
  },
  title: {
    marginBottom: 16,
  },
  description: {
    opacity: 0.8,
    lineHeight: 22,
    marginBottom: 24,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginVertical: 20,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ExampleWithHeader;
