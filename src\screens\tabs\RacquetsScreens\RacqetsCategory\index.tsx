import React from 'react';
import {ScrollView, View} from 'react-native';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {useThemeStore} from '@/store/themeStore';
import {createStyles} from './styles';
import {<PERSON><PERSON><PERSON>, Header, SafeAreaView} from '@/components';
import PillLabel from '@/components/PillLabel';
import {NativeStackNavigationProp, NativeStackScreenProps} from '@react-navigation/native-stack';
import {RacquetStackParamList} from '@/navigation/RacquetStack';
import {Images} from '@/config';
import useTranslation from '@/hooks/useTranslation';

type Props = NativeStackScreenProps<RacquetStackParamList, 'RacquetCategory'>;
type NavigationProp = NativeStackNavigationProp<RacquetStackParamList>;

const RacquetCategoryScreen = ({route}: Props) => {
  const navigation = useNavigation<NavigationProp>();
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const {sportsTitle} = route.params;
  const {t} = useTranslation();
  return (
    <SafeAreaView includeBottom={false} style={styles.container}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: {backgroundColor: theme.colors.primary},
        }}
        leftIconButtonStyle={styles.menuButton}
        onLeftPress={() => navigation.dispatch(DrawerActions.openDrawer())}
        rightIcons={[{name: 'cart', size: 32, color: theme.colors.activeColor}]}
        pageTitle={sportsTitle || ''}
        backgroundColor="transparent"
      />
      <ScrollView contentContainerStyle={styles.brandContainer}>
        <View style={styles.topBrandContainer}>
          <CImage source={Images.topBrand} style={styles.topBrandImage} resizeMode="cover" />
        </View>
        <View style={styles.optionsContainer}>
          <PillLabel
            backgroundColor={theme.colors.orange1}
            textColor={theme.colors.white}
            label={t('RacquetCategoryScreen.byBrand')}
            containerStyle={styles.pillLabel}
            triangle={true}
            onPress={() => {
              navigation.navigate('RacquetBrands', {
                sportsTitle: sportsTitle,
                category: 'brand',
              });
            }}
          />
          <PillLabel
            backgroundColor={theme.colors.orange1}
            textColor={theme.colors.white}
            label={t('RacquetCategoryScreen.bestForMyGame')}
            containerStyle={styles.pillLabel}
            triangle={true}
            onPress={() => {
              navigation.navigate('RacquetSelector');
            }}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default RacquetCategoryScreen;
