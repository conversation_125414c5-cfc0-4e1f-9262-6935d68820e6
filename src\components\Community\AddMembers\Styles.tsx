import FONTS, {FONT_SIZE} from '@/utils/fonts';
import {Dimensions, StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    content: {
      flex: 1,
      padding: 16,
      backgroundColor: theme.colors.background,
    },
    headerRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 10,
    },
    headerCancel: {
      fontSize: 18,
      fontWeight: 'bold',
    },
    headerNext: {
      fontSize: 18,
      fontWeight: 'bold',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      textAlign: 'center',
      flex: 1,
      color: theme.colors.white,
    },
    memberCount: {
      color: theme.colors.white,
      textAlign: 'center',
      marginBottom: 20,
    },
    searchContainer: {
      borderWidth: 1,
      borderColor: theme.colors.secondary,
      borderRadius: 20,
      marginHorizontal: 0,
      marginBottom: 16,
      paddingHorizontal: 16,
      paddingVertical: 4,
      backgroundColor: theme.colors.background,
    },
    searchInput: {
      // color: theme.colors.white,
      borderRadius: 30,
      height: 47,
      fontSize: 16,
      paddingVertical: 8,
    },
    resultsHeaderRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: 16,
      marginBottom: 16,
    },
    resultsHeader: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.white,
    },
    membersList: {
      paddingBottom: 20,
      gap: 15,
    },
    memberRow: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.placeholder,
      marginBottom: 12,
      padding: 12,
      minHeight: 80,
    },
    memberRowSelected: {
      borderColor: theme.colors.secondary,
      borderWidth: 2,
      backgroundColor: theme.colors.background,
    },
    memberRowPartner: {
      borderColor: theme.colors.secondary,
      borderWidth: 2,
      backgroundColor: theme.colors.background,
    },
    avatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      marginRight: 12,
      borderWidth: 2,
      borderColor: theme.colors.background,
    },
    memberInfo: {
      flex: 1,
      justifyContent: 'center',
    },
    memberName: {
      color: theme.colors.white,
      fontWeight: 'bold',
      fontSize: 18,
    },
    memberRating: {
      color: theme.colors.white,
      fontWeight: 'bold',
      fontSize: 16,
      marginLeft: 4,
    },
    memberCoach: {
      color: theme.colors.white,
      fontWeight: 'bold',
      fontSize: 14,
      marginLeft: 4,
      letterSpacing: 1,
    },
    memberLocation: {
      color: theme.colors.white,
      fontSize: 14,
      marginLeft: 4,
    },
    memberAction: {
      marginLeft: 10,
      alignItems: 'center',
      justifyContent: 'center',
    },
    selectedCircle: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: theme.colors.background,
      borderWidth: 2,
      borderColor: theme.colors.secondary,
      alignItems: 'center',
      justifyContent: 'center',
    },
    addCircle: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
    },
    partnerCircle: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: theme.colors.orange,
      alignItems: 'center',
      justifyContent: 'center',
    },
    partnerText: {
      color: theme.colors.white,
      fontWeight: 'bold',
      fontSize: 18,
    },
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    container: {
      flex: 1,
    },
    filterInput: {
      borderRadius: 8,
      // height: 70,
      marginVertical: 15,
    },
  });
