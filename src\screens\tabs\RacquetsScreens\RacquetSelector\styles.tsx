import {Dimensions, StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme?.colors?.background,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    carousel: {
      flexGrow: 1,
    },
    question: {
      color: theme?.colors?.white,
      textAlign: 'center',
    },
    page: {
      flex: 1,
      alignItems: 'center',
      paddingTop: 8,
      paddingBottom: 20,
      gap: 18,
    },
    optionsContainer: {
      flex: 1,
      gap: 24,
    },
    pillLabel: {
      width: Dimensions.get('window').width - 32,
      flex: 1,
    },
    pagination: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginBottom: 16,
    },
    dotActive: {
      width: 10,
      height: 10,
      borderRadius: 5,
      backgroundColor: theme.colors.activeColor,
      marginHorizontal: 4,
    },
    dot: {
      width: 10,
      height: 10,
      borderRadius: 5,
      backgroundColor: theme.colors.dimGray,
      marginHorizontal: 4,
    },
  });
