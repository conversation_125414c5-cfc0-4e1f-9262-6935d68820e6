import React, {forwardRef, useState} from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
  ColorValue,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import {BottomSheetTextInput} from '@gorhom/bottom-sheet';
import {useThemeStore} from '@/store/themeStore';
import Icon from 'react-native-vector-icons/Ionicons';

interface CInputProps extends TextInputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  showLabel?: boolean;
  leftText?: string;
  error?: string;
  hasError?: boolean;
  containerStyle?: ViewStyle;
  inputStyle?: ViewStyle;
  labelStyle?: TextStyle;
  placeholderTextColorStyle?: ColorValue;
  onBlur?: () => void;
  cursorColor?: ColorValue;
  useBottomSheetInput?: boolean;
  isLoading?: boolean;
  variant?: 'dark' | 'light';
}

const CInput = forwardRef<TextInput, CInputProps>(
  (
    {
      label,
      placeholder,
      value,
      onChangeText,
      showLabel = false,
      leftText,
      error,
      hasError,
      containerStyle,
      inputStyle,
      labelStyle,
      placeholderTextColorStyle,
      onBlur,
      cursorColor = '',
      useBottomSheetInput = false,
      isLoading = false,
      variant = '',
      secureTextEntry,
      ...rest
    },
    ref,
  ) => {
    const theme = useThemeStore();
    const [showPassword, setShowPassword] = useState(false);

    const InputComponent = useBottomSheetInput ? BottomSheetTextInput : TextInput;

    return (
      <View style={[styles(theme).container, containerStyle]}>
        {showLabel && label && (
          <Text
            style={[
              styles(theme).label,
              {color: variant === 'light' ? theme.colors.white : theme.colors.inputLabel},
              labelStyle,
            ]}>
            {label}
          </Text>
        )}
        <View style={styles(theme).inputContainer}>
          {leftText && (
            <View style={styles(theme).leftTextContainer}>
              <Text style={[styles(theme).leftText, {color: theme.colors.transparentWhite}]}>
                {leftText}
              </Text>
            </View>
          )}
          <InputComponent
            style={[
              styles(theme).input,
              {
                color:
                  hasError || error
                    ? theme.colors.coralRed
                    : variant === 'light'
                      ? theme.colors.primary
                      : theme.colors.activeColor,
                borderColor:
                  hasError || error
                    ? theme.colors.coralRed
                    : variant === 'light'
                      ? theme.colors.primary
                      : theme.colors.inputLabel,
                backgroundColor: variant === 'light' ? theme.colors.white : theme.colors.background,
              },
              leftText && styles(theme).inputWithLeftText,
              secureTextEntry && value && styles(theme).inputWithIcon,
              variant && !hasError && !error && styles(theme)[variant as keyof typeof styles],
              inputStyle,
            ]}
            placeholder={placeholder}
            placeholderTextColor={
              variant === 'light' ? theme.colors.primary : theme.colors.placeholder
            }
            value={value}
            onChangeText={onChangeText}
            onBlur={onBlur}
            cursorColor={variant === 'light' ? theme.colors.primary : theme.colors.activeColor}
            selectionColor={theme.colors.activeColor}
            secureTextEntry={secureTextEntry ? !showPassword : false}
            ref={ref as any}
            {...rest}
          />
          {secureTextEntry && value && (
            <TouchableOpacity
              style={styles(theme).iconContainer}
              onPress={() => setShowPassword(!showPassword)}>
              <Icon
                name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                size={20}
                color={variant === 'light' ? theme.colors.primary : theme.colors.activeColor}
              />
            </TouchableOpacity>
          )}
          {isLoading && (
            <View style={styles(theme).loadingContainer}>
              <ActivityIndicator size="small" color={theme.colors.activeColor} />
            </View>
          )}
        </View>
        {error && <Text style={styles(theme).errorText}>{error}</Text>}
      </View>
    );
  },
);

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      marginBottom: 12,
    },
    label: {
      marginBottom: 6,
      fontSize: theme.fontSize.font14,
      fontWeight: '500',
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      position: 'relative',
    },
    input: {
      flex: 1,
      height: 45,
      borderRadius: 9,
      paddingHorizontal: 12,
      borderWidth: 1,
      fontSize: theme.fontSize.medium,
    },
    dark: {
      backgroundColor: theme.colors.background,
      color: theme.colors.activeColor,
    },
    light: {
      backgroundColor: theme.colors.white,
      color: theme.colors.primary,
    },
    leftTextContainer: {
      position: 'absolute',
      left: 12,
      zIndex: 1,
    },
    leftText: {
      fontSize: theme.fontSize.medium,
    },
    inputWithLeftText: {
      paddingLeft: 50,
    },
    inputWithIcon: {
      paddingRight: 45,
    },
    iconContainer: {
      position: 'absolute',
      right: 12,
      height: '100%',
      justifyContent: 'center',
    },
    errorText: {
      color: theme.colors.coralRed,
      fontSize: theme.fontSize.small,
      marginTop: 4,
    },
    loadingContainer: {
      position: 'absolute',
      right: 12,
      top: '50%',
      transform: [{translateY: -8}],
    },
  });

export default CInput;
