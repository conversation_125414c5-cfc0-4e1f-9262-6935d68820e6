import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      borderRadius: 8,
      paddingHorizontal: 16,
    },
    formContainer: {
      flex: 1,
      justifyContent: 'center',
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    label: {
      color: theme.colors.white,
      fontSize: 18,
      fontWeight: '400',
    },
    inputContainer: {
      marginBottom: 5,
    },
    input: {
      backgroundColor: theme.colors.white,
      color: theme.colors.black,
      borderWidth: 0,
      borderRadius: 8,
      height: 70,
      fontWeight: '400',
      fontSize: 16,
    },
    birthYearWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      position: 'relative',
    },
    ageTag: {
      position: 'absolute',
      right: 20,
      bottom: 33,
    },
    ageTagText: {
      color: theme.colors.white,
      fontWeight: '700',
      fontSize: 14,
    },

    btnMain: {
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 'auto',
      paddingBottom: 10,
    },
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 5,
      marginBottom: 15,
    },
    titleContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    selectDateContainer: {
      backgroundColor: theme.colors.dimGray,
      borderRadius: 11,
      paddingHorizontal: 10,
      paddingTop: 10,
      minHeight: 57,
      marginBottom: 10,
    },
    textArea: {
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      color: theme.colors.black,
      width: '100%',
      minHeight: 150,
      justifyContent: 'flex-start',
      textAlignVertical: 'top',
      paddingLeft: 10,
      paddingTop: 15,
    },
    rowBetween: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginVertical: 8,
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.secondary,
      marginTop: 10,
    },
    createGroupContainer: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    classCardContainer: {
      marginBottom: 10,
      flexGrow: 1,
    },
    flatListContainer: {
      flex: 1,
      minHeight: 300,
    },
    flatListContent: {
      paddingBottom: 20,
    },
  });
