import React from 'react';
import {useThemeStore} from '@/store/themeStore';
import {useAuthStore} from '@/store/authStore';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '@navigation/index';
import {getGlobalStyles} from '@utils/styleUtils';
import {SafeAreaView} from '@/components';

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'MainTabs'>;

const HomeScreen = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const {logout} = useAuthStore();
  const theme = useThemeStore();
  const auth = useAuthStore();
  const globalStyles = getGlobalStyles({theme});

  const handleSettings = () => {
    navigation.navigate('Settings');
  };

  const handleLogout = () => {
    logout();
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.semiTransparentWhite,
    },
    title: {
      marginBottom: 0,
      color: theme.colors.white,
    },
    settingsButton: {
      padding: 8,
    },
    content: {
      flex: 1,
      padding: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    welcomeText: {
      textAlign: 'center',
      marginBottom: 8,
      color: theme.colors.white,
    },
    messageText: {
      textAlign: 'center',
      marginBottom: 40,
      color: theme.colors.transparentWhite,
    },
    logoutButton: {
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8,
      alignItems: 'center',
    },
    logoutButtonText: {
      color: theme.colors.white,
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <SafeAreaView
      includeBottom={false}
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <View style={styles.header}>
        <Text style={[globalStyles.title, styles.title]}>Home</Text>
        <TouchableOpacity style={styles.settingsButton} onPress={handleSettings}>
          <Text style={[globalStyles.text, {color: theme.colors.white}]}>Settings</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <Text style={[globalStyles.title, styles.welcomeText]}>
          Welcome, {auth.user?.name || 'User'}!
        </Text>
        <Text style={[globalStyles.text, styles.messageText]}>
          You have successfully logged in to the app.
        </Text>

        <TouchableOpacity
          style={[styles.logoutButton, {backgroundColor: theme.colors.primary}]}
          onPress={handleLogout}>
          <Text style={styles.logoutButtonText}>Log Out</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default HomeScreen;
