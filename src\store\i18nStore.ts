import {create} from 'zustand';
import {persist} from 'zustand/middleware';

export type SupportedLanguage = 'en' | 'fr' | 'es' | 'de';

type ConfigState = {
  language: SupportedLanguage;
  setLanguage: (lang: SupportedLanguage) => void;
  translationVersion: number;
  bumpTranslationVersion: () => void;
};

export const usei18nStore = create<ConfigState>()(
  persist(
    set => ({
      language: 'en',
      setLanguage: lang => set({language: lang}),
      translationVersion: 0,
      bumpTranslationVersion: () =>
        set(state => ({
          translationVersion: state.translationVersion + 1,
        })),
    }),
    {
      name: 'i18n-store', // localStorage key or AsyncStorage key
    },
  ),
);
