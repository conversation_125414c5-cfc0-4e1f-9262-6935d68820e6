// context/SocketContext.tsx
import {tokenStorage} from '@/services/api';
import React, {createContext, useContext, useEffect, useRef, useState} from 'react';
import io, {Socket} from 'socket.io-client';

type SocketContextType = {
  socket: Socket | null;
  isConnected: boolean;
};

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
});

const SOCKET_URL = 'http://your-server-url'; // Replace with your backend URL

export const SocketProvider: React.FC<{children: React.ReactNode}> = ({children}) => {
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const accessToken = tokenStorage.getString('accessToken');

  useEffect(() => {
    if (!accessToken) {
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
      return;
    }
    const socket = io(SOCKET_URL, {
      transports: ['websocket'],
      reconnectionAttempts: Infinity, // Retry infinite times
      reconnectionDelay: 5000, // 5 second delay
    });

    socketRef.current = socket;

    socket.on('connect', () => {
      console.log('Socket connected');
      setIsConnected(true);
    });

    socket.on('disconnect', () => {
      console.log('Socket disconnected');
      setIsConnected(false);
    });

    socket.on('reconnect_attempt', attempt => {
      console.log(`Reconnection attempt ${attempt}`);
    });

    socket.on('reconnect', () => {
      console.log('Reconnected');
    });

    socket.on('reconnect_failed', () => {
      console.log('Reconnection failed');
    });

    return () => {
      socket.disconnect();
      socketRef.current = null;
    };
  }, [accessToken]);

  return (
    <SocketContext.Provider value={{socket: socketRef.current, isConnected}}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = () => useContext(SocketContext);
