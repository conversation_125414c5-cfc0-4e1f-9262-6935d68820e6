import React from 'react';
import {useThemeStore} from '@/store/themeStore';
import {Image, StyleProp, TouchableOpacity, View, ViewStyle} from 'react-native';
import {styles as createStyles} from './styles';
import Typography from '../Typography';
import {Icon} from '@/components';
interface CartCardProps {
  variant?: 'equipment';
  cartData: {
    name: string;
    location: string;
    image: any;
    duration?: string;
    size?: string;
    hour?: string;
    string?: string;
  };
  onSelect?: (cartData: CartCardProps['cartData']) => void;
  onEditPress?: () => void;
  time?: string;
  courts?: number;
  surface?: string;
  date?: string;
  style?: StyleProp<ViewStyle>;
}
const CartCard = (props: CartCardProps) => {
  const {cartData, variant, onSelect = () => {}, onEditPress = () => {}, hour, style} = props;
  console.log('cartData', cartData);

  const formattedDate = (data: string) => {
    const newDate = new Date(data);

    const options: Intl.DateTimeFormatOptions = {month: 'long', day: 'numeric'};
    const formatDate = newDate.toLocaleDateString('en-US', options);
    return formatDate;
  };

  const theme = useThemeStore();
  const styles = createStyles(theme, variant);

  return (
    <TouchableOpacity
      style={[styles.parkItem, style]}
      onPress={() => {
        onSelect(cartData);
      }}>
      <View style={styles.pinContainer}>
        <Image source={cartData?.image} style={{width: 55, height: 78}} />
      </View>
      <View style={styles.parkInfo}>
        <Typography
          variant="subtitle"
          style={variant === 'equipment' ? styles.equipParkName : styles.parkName}>
          {cartData?.name}
        </Typography>
        {variant === 'equipment' && (
          <TouchableOpacity activeOpacity={0.7} style={styles.editButton} onPress={onEditPress}>
            <Icon name="editpen" size={20} color={theme.colors.orange} />
          </TouchableOpacity>
        )}

        <>
          {cartData?.duration && (
            <Typography variant="bodyMedium1" style={styles.parkLocation}>
              {cartData.duration}
            </Typography>
          )}
          {cartData?.hour && (
            <Typography variant="caption" style={styles.detailText}>
              {`${cartData.hour}`}
            </Typography>
          )}
          {cartData?.size && (
            <Typography variant="caption" style={styles.detailText}>
              {`Size: ${cartData.size}`}
            </Typography>
          )}
          {cartData?.string && (
            <Typography variant="caption" style={styles.detailText}>
              {`String: ${cartData.string}`}
            </Typography>
          )}
        </>
      </View>
    </TouchableOpacity>
  );
};

export default CartCard;
