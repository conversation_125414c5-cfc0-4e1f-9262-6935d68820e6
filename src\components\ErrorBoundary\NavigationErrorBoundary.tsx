import React, {Component, ErrorInfo, ReactNode} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import * as Sentry from '@sentry/react-native';
import {useThemeStore} from '@/store/themeStore';
import {useNavigation, NavigationProp} from '@react-navigation/native';

interface NavigationErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  routeName?: string; // Current route name
}

interface NavigationErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

/**
 * A specialized error boundary for navigation components that provides
 * context about navigation errors and reports them to Sentry.
 */
class NavigationErrorBoundaryClass extends Component<
  NavigationErrorBoundaryProps,
  NavigationErrorBoundaryState
> {
  constructor(props: NavigationErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): NavigationErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return {hasError: true, error};
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Report the error to Sentry with navigation context
    const {routeName} = this.props;

    Sentry.withScope(scope => {
      // Add navigation info to help with debugging
      scope.setTag('error_type', 'navigation_error');
      scope.setTag('route', routeName || 'unknown');
      scope.setExtra('componentStack', errorInfo.componentStack);

      // Capture the error
      Sentry.captureException(error);
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    console.error('Navigation Error caught by NavigationErrorBoundary:', error);
  }

  resetError = (): void => {
    this.setState({hasError: false, error: null});
  };

  render(): ReactNode {
    const {hasError, error} = this.state;
    const {children, fallback} = this.props;

    if (hasError) {
      // If a custom fallback is provided, use it
      if (fallback) {
        return fallback;
      }

      // Default fallback UI
      return (
        <View style={styles.container}>
          <Text style={styles.title}>Navigation Error</Text>
          <Text style={styles.message}>
            {error?.message || 'An unexpected error occurred while navigating.'}
          </Text>
          <TouchableOpacity style={styles.button} onPress={this.resetError}>
            <Text style={styles.buttonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return children;
  }
}

// Styles for the default error UI
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#121212',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#E0E0E0',
    marginBottom: 10,
  },
  message: {
    fontSize: 16,
    color: '#B0B0B0',
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#2B8ECE',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});

// Wrapper component to use theme and navigation
const NavigationErrorBoundary: React.FC<NavigationErrorBoundaryProps> = props => {
  const theme = useThemeStore();

  // We need to handle the case where this component might be used outside of a navigation context
  let hasNavigation = false;
  let navigation: any = null;

  try {
    navigation = useNavigation();
    hasNavigation = true;
  } catch (error) {
    // If useNavigation fails, we'll handle it gracefully
    console.warn(
      'NavigationErrorBoundary: useNavigation hook failed, navigation context may be missing',
    );
  }

  return (
    <NavigationErrorBoundaryClass
      {...props}
      fallback={
        props.fallback || (
          <View style={[styles.container, {backgroundColor: theme.colors.background}]}>
            <Text style={[styles.title, {color: theme.colors.text}]}>Navigation Error</Text>
            <Text style={[styles.message, {color: theme.colors.secondary}]}>
              There was a problem navigating to this screen.
            </Text>
            <TouchableOpacity
              style={[styles.button, {backgroundColor: theme.colors.primary}]}
              onPress={() => {
                // Go back to the previous screen if navigation is available
                if (hasNavigation && navigation) {
                  navigation.goBack();
                } else {
                  // Otherwise just reset the error state
                  if (props.onError) {
                    props.onError(new Error('Navigation reset'), {componentStack: ''});
                  }
                }
              }}>
              <Text style={styles.buttonText}>Try Again</Text>
            </TouchableOpacity>
          </View>
        )
      }
    />
  );
};

export default NavigationErrorBoundary;
