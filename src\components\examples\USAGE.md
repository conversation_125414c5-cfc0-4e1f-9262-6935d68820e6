# How to Use the Optimized FlatList Example

This document provides instructions on how to access and use the optimized FlatList example in your app.

## Accessing the Example

There are two ways to access the optimized FlatList example:

### 1. Using the Navigation Utility

```javascript
import { navigateToOptimizedFlatListExample } from '@/utils/examplesNavigator';

// In your component:
const handleOpenExample = () => {
  navigateToOptimizedFlatListExample(navigation);
};

// Then use this in a button or other UI element
<Button title="Open FlatList Example" onPress={handleOpenExample} />
```

### 2. Direct Navigation

```javascript
// Navigate to the Examples stack with the OptimizedFlatList screen
navigation.navigate('Examples', {
  screen: 'OptimizedFlatList'
});
```

## Implementing Optimized FlatLists in Your App

To implement the optimizations demonstrated in the example in your own app:

1. **Use memoization for list items**:
   ```javascript
   const MyListItem = memo(({ item, onPress }) => {
     // Component implementation
   }, (prevProps, nextProps) => {
     // Custom comparison function
     return prevProps.item.id === nextProps.item.id;
   });
   ```

2. **Optimize FlatList props**:
   ```javascript
   <FlatList
     data={data}
     renderItem={renderItem}
     keyExtractor={keyExtractor}
     getItemLayout={getItemLayout}
     removeClippedSubviews={Platform.OS === 'android'}
     maxToRenderPerBatch={10}
     updateCellsBatchingPeriod={50}
     initialNumToRender={10}
     windowSize={21}
   />
   ```

3. **Use getItemLayout for fixed height items**:
   ```javascript
   const getItemLayout = useCallback(
     (_, index) => ({
       length: ITEM_HEIGHT,
       offset: ITEM_HEIGHT * index,
       index,
     }),
     []
   );
   ```

4. **Memoize callback functions**:
   ```javascript
   const handleItemPress = useCallback((id) => {
     // Handle press
   }, []);
   
   const renderItem = useCallback(
     ({ item }) => <MyListItem item={item} onPress={handleItemPress} />,
     [handleItemPress]
   );
   ```

## Performance Monitoring

To monitor the performance impact of these optimizations:

1. Use the React DevTools Profiler to measure render times
2. Monitor JavaScript thread frame drops in the Performance Monitor
3. Test on lower-end devices to see the most significant improvements

## References

- [React Native Documentation - Optimizing FlatList Configuration](https://reactnative.dev/docs/optimizing-flatlist-configuration)
- [React Native Documentation - FlatList](https://reactnative.dev/docs/flatlist)
