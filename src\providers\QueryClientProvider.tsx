import React from 'react';
import {
  QueryClient,
  QueryClientProvider as TanstackQueryClientProvider,
} from '@tanstack/react-query';
import {Platform} from 'react-native';

// Optional: Import devtools for development
// Note: React Query Devtools are primarily designed for web,
// so we conditionally import them only for web platforms
let ReactQueryDevtools: React.FC<{initialIsOpen?: boolean}> | null = null;
if (__DEV__ && Platform.OS === 'web') {
  // This is a dynamic import that will only be executed on web platforms
  import('@tanstack/react-query-devtools').then(module => {
    ReactQueryDevtools = module.ReactQueryDevtools;
  });
}

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Global default options for queries
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes
      retry: 1, // Retry failed requests once
      refetchOnWindowFocus: false, // Don't refetch when app regains focus (mobile-friendly)
      refetchOnReconnect: true, // Refetch when reconnecting to network
    },
    mutations: {
      // Global default options for mutations
      retry: 1, // Retry failed mutations once
    },
  },
});

interface QueryClientProviderProps {
  children: React.ReactNode;
}

export const QueryClientProvider: React.FC<QueryClientProviderProps> = ({children}) => {
  return (
    <TanstackQueryClientProvider client={queryClient}>
      {children}
      {/* Render devtools only in development and on web */}
      {__DEV__ && Platform.OS === 'web' && ReactQueryDevtools && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </TanstackQueryClientProvider>
  );
};

export {queryClient};
