/* eslint-disable react/display-name */
import React, {
  useCallback,
  useMemo,
  useRef,
  forwardRef,
  useImperativeHandle,
  useState,
} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {useAuthStore} from '@/store/authStore';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import BottomSheet, {BottomSheetView} from '@gorhom/bottom-sheet';
import {CImage, Icon} from '@/components';
import Typography from '@/components/Typography';
import {Images} from '@/config';
import useTranslation from '@/hooks/useTranslation';

export interface BottomSheetCompProps {
  children?: React.ReactNode;
  customSnapPoints?: string[];
  initialIndex?: number;
  showCloseIcon?: boolean;
  showHeader?: boolean;
  enablePanDownToClose?: boolean;
}

export interface BottomSheetCompHandles {
  snapToIndex: (index: number) => void;
  close: () => void;
  expand: () => void;
}

const BottomSheetComp = forwardRef<BottomSheetCompHandles, BottomSheetCompProps>(
  (
    {
      children,
      customSnapPoints,
      initialIndex,
      showCloseIcon = false,
      showHeader = false,
      enablePanDownToClose = false,
    },
    ref,
  ) => {
    const theme = useThemeStore();
    const {user} = useAuthStore();
    const {t} = useTranslation();
    // ref
    const bottomSheetRef = useRef<BottomSheet>(null);
    // Track the current sheet index to control close icon visibility
    const [currentIndex, setCurrentIndex] = useState<number>(initialIndex || 0);

    // variables
    const defaultSnapPoints = useMemo(() => ['20%', '50%', '100%'], []);
    const snapPoints = customSnapPoints || defaultSnapPoints; // Check if bottom sheet is fully expanded (at 100%)
    const isFullyExpanded = useMemo(() => {
      const lastSnapPoint = snapPoints[snapPoints.length - 1];
      const isLastIndex = currentIndex === snapPoints.length - 1;

      if (typeof lastSnapPoint === 'string') {
        const percentage = parseInt(lastSnapPoint, 10);
        return isLastIndex && percentage >= 50; // Only show close icon for heights >= 50%
      }

      return isLastIndex && snapPoints.length > 1; // For numeric values or single snap points
    }, [currentIndex, snapPoints]);

    // Expose methods to parent components
    useImperativeHandle(ref, () => ({
      snapToIndex: (index: number) => {
        bottomSheetRef.current?.snapToIndex(index);
      },
      close: () => {
        bottomSheetRef.current?.close();
      },
      expand: () => {
        bottomSheetRef.current?.expand();
      },
    }));

    // callbacks
    const handleSheetChanges = useCallback((index: number) => {
      console.log('handleSheetChanges', index);
      setCurrentIndex(index);
    }, []);

    // Handle close button press
    const handleClosePress = useCallback(() => {
      bottomSheetRef.current?.snapToIndex(0);
    }, []);

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={initialIndex || 0}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        enablePanDownToClose={enablePanDownToClose}
        enableOverDrag={false}
        enableContentPanningGesture={false} // ✅ DISABLE to prevent gesture conflicts
        backgroundStyle={{backgroundColor: theme.colors.background}}
        handleIndicatorStyle={styles(theme).handleIndicator}>
        <BottomSheetView style={styles(theme).contentContainer}>
          {showCloseIcon && isFullyExpanded && (
            <View style={styles(theme).closeButtonContainer}>
              <TouchableOpacity style={styles(theme).closeButton} onPress={handleClosePress}>
                <Icon name="close" size={18} color="red" />
              </TouchableOpacity>
            </View>
          )}
          {showHeader && !isFullyExpanded && (
            <View style={styles(theme).headerContainer}>
              <CImage
                source={Images.coachProfile}
                style={styles(theme).profileImage}
                imageStyle={styles(theme).roundedImage}
                resizeMode="contain"
              />
              <Typography variant="title" style={styles(theme).title}>
                {t('BottomSheetComp.niceToSeeYou', {
                  name: user?.name || t('BottomSheetComp.Guest'),
                })}
              </Typography>
            </View>
          )}
          {children}
        </BottomSheetView>
      </BottomSheet>
    );
  },
);

// Define the theme type
interface Theme {
  colors: {
    background: string;
    bottomSheetBackground: string;
    text: string;
    red: string;
  };
}

const styles = (theme: Theme) =>
  StyleSheet.create({
    contentContainer: {
      flex: 1,
      padding: 20,
      // alignItems: 'center',
    },
    handleIndicator: {
      backgroundColor: theme.colors.bottomSheetBackground,
    },
    closeButtonContainer: {
      justifyContent: 'flex-start',
      width: '100%',
      marginBottom: 10,
    },
    closeButton: {
      width: 30,
      height: 30,
      borderRadius: 15,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.red,
    },
    headerContainer: {
      paddingTop: 10,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 15,
      marginBottom: 10,
    },
    profileImage: {
      width: 30,
      height: 30,
      borderRadius: 100,
    },
    roundedImage: {
      borderRadius: 100,
    },
    title: {
      color: theme.colors.text,
    },
  });

export default BottomSheetComp;
