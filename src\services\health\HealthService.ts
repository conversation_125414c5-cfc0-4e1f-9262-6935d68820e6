import {Platform} from 'react-native';
import {HealthServiceInterface} from './types';
import AndroidHealthService from './AndroidHealthService';
import IOSHealthService from './IOSHealthService';

/**
 * Factory function to create the appropriate health service based on platform
 */
export const createHealthService = (): HealthServiceInterface => {
  if (Platform.OS === 'android') {
    return new AndroidHealthService();
  } else if (Platform.OS === 'ios') {
    return new IOSHealthService();
  } else {
    throw new Error('Unsupported platform for health services');
  }
};

// Create a singleton instance of the health service
const healthService = createHealthService();

export default healthService;
