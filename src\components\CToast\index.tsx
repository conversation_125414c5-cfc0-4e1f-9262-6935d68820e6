import React from 'react';
import {View, Text} from 'react-native';
import Toast, {BaseToastProps} from 'react-native-toast-message';
import Icon, {Icons} from '@config/icons';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store';
import Typography from '../Typography';

// 🍞 Reusable Toast UI
const ToastContainer = ({text1, text2, type}: BaseToastProps & {type?: string}) => {
  const theme = useThemeStore();

  const styles = createStyles(theme);

  return (
    <View style={styles.toastBox}>
      <View style={styles.row}>
        <View
          style={[
            styles.iconCircle,
            {
              backgroundColor:
                type === 'success'
                  ? theme.colors.green
                  : type === 'error'
                    ? theme.colors.red
                    : theme.colors.primary,
            },
          ]}>
          {type === 'success' ? (
            <Icon type={Icons.MaterialIcons} name="done" color={theme.colors.white} size={20} />
          ) : (
            <Icon
              type={Icons.FontAwesome}
              name={type === 'error' ? 'remove' : type === 'info' ? 'info' : ''}
              color={theme.colors.white}
              size={15}
            />
          )}
        </View>

        <View style={styles.textContainer}>
          {text1 ? (
            <Typography variant="bodyMedium" style={{lineHeight: 18}}>
              {text1}
            </Typography>
          ) : null}
          {text2 ? <Typography variant="body">{text2}</Typography> : null}
        </View>
      </View>
    </View>
  );
};

// 🍞 Config for each toast type
const toastConfig = {
  success: (props: BaseToastProps) => <ToastContainer {...props} type="success" />,
  error: (props: BaseToastProps) => <ToastContainer {...props} type="error" />,
  info: (props: BaseToastProps) => <ToastContainer {...props} type="info" />,
  comingSoon: () => <ToastContainer text1="Coming soon..." type="comingSoon" />,
};

// 🔥 Main Toast component (import & render once in root)
export default function CToast() {
  return <Toast config={toastConfig} visibilityTime={3000} autoHide onPress={() => Toast.hide()} />;
}
