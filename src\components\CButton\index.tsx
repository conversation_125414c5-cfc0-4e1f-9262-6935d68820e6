import React from 'react';
import {useThemeStore} from '@/store/themeStore';
import {
  TouchableOpacity,
  Text,
  GestureResponderEvent,
  ActivityIndicator,
  StyleProp,
  ViewStyle,
  TextStyle,
  View,
} from 'react-native';
import {styles as createStyles} from './styles';
type Variant = 'primary' | 'secondary' | 'pill' | 'square' | 'dark' | 'outline' | 'active';

interface CButtonProps {
  title: string;
  variant?: Variant;
  isDisabled?: boolean;
  loading?: boolean;
  onPress?: (event: GestureResponderEvent) => void;
  containerStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  theme?: {
    colors: {
      primary: string;
      secondary: string;
      dark: string;
    };
  };
}

const defaultTheme = {
  colors: {
    primary: '#007BFF',
    secondary: '#707070',
    dark: '#2B2B2B',
  },
};

const CButton: React.FC<CButtonProps> = ({
  title,
  variant = 'primary',
  isDisabled = false,
  loading = false,
  onPress,
  containerStyle,
  textStyle,
}) => {
  const theme = useThemeStore() || defaultTheme;
  const styles = createStyles(theme);

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      style={[
        styles.base,
        styles[variant],
        (isDisabled || loading) && styles.disabled,
        containerStyle,
      ]}
      onPress={onPress}
      disabled={isDisabled || loading}>
      <View style={styles.contentContainer}>
        <Text
          style={[
            styles.text,
            textStyle,
            {
              opacity: loading ? 0 : 1,
              color: variant === 'active' ? theme.colors.black : '',
            },
          ]}>
          {title}
        </Text>
        <ActivityIndicator
          style={[styles.loader, {opacity: loading ? 1 : 0}]}
          size="small"
          color={theme.colors.text}
        />
      </View>
    </TouchableOpacity>
  );
};

export default CButton;
