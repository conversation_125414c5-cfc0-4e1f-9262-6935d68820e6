import {clearImageCache} from '@/components/CImage';
import {Platform, NativeModules, NativeEventEmitter} from 'react-native';

/**
 * Memory Manager Utility
 *
 * This utility provides functions to help manage memory usage in the app,
 * particularly for texture-intensive components like maps and images.
 */

// Track when the cache was last cleared
let lastCacheClearTime = Date.now();

/**
 * Clears image caches if needed based on time elapsed or memory pressure
 * @param forceClean Force clean regardless of time elapsed
 */
export const cleanMemoryIfNeeded = (forceClean = false) => {
  const now = Date.now();
  const timeSinceLastClean = now - lastCacheClearTime;

  // Clean cache if it's been more than 5 minutes or if forced
  if (forceClean || timeSinceLastClean > 5 * 60 * 1000) {
    clearImageCache();
    lastCacheClearTime = now;
    return true;
  }

  return false;
};

/**
 * Setup periodic memory cleaning
 * This should be called once during app initialization
 */
export const setupMemoryCleaning = () => {
  // Set up a timer to clean memory every 5 minutes
  const cleanInterval = setInterval(
    () => {
      cleanMemoryIfNeeded();
    },
    5 * 60 * 1000,
  );

  // Return a cleanup function
  return () => {
    clearInterval(cleanInterval);
  };
};

/**
 * Utility to handle texture allocation errors
 * Call this when catching texture-related errors
 */
export const handleTextureError = () => {
  // Force clean memory immediately
  cleanMemoryIfNeeded(true);

  // On Android, we can also try to reduce texture usage
  if (Platform.OS === 'android') {
    // Additional Android-specific cleanup could go here
    console.warn('Texture allocation error handled - memory cleaned');
  }
};

/**
 * Set up global error handlers for texture allocation issues
 * This should be called during app initialization
 */
export const setupTextureErrorHandlers = () => {
  // For React Native's error handling
  const originalConsoleError = console.error;
  console.error = (...args) => {
    // Check if this is a texture allocation error
    const errorMessage = args.join(' ');
    if (
      errorMessage.includes("Texture can't be extended") ||
      errorMessage.includes('allocateBlock') ||
      errorMessage.includes('GMM-CLIENT-INJECTED-STYLE-NAMESPACE')
    ) {
      // Handle texture allocation error
      handleTextureError();
    }

    // Call original console.error
    originalConsoleError(...args);
  };

  // Return cleanup function
  return () => {
    console.error = originalConsoleError;
  };
};
