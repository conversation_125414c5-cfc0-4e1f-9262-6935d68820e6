import {Dimensions, StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme?.colors?.background,
      borderWidth: 1,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    brandContainer: {
      flex: 1,
      alignItems: 'center',
      paddingTop: 5,
      paddingBottom: 15,
      gap: 18,
    },
    optionsContainer: {
      flex: 1,
      gap: 24,
    },
    pillLabel: {
      width: Dimensions.get('window').width - 32,
      flex: 1,
    },
    topBrandContainer: {
      width: Dimensions.get('screen').width - 32,
      height: 118,
    },
    topBrandImage: {
      width: '100%',
      height: '100%',
      borderRadius: 10,
      borderBottomRightRadius: 0,
    },
  });
