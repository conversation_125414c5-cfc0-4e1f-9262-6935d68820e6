import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, Switch, TouchableOpacity, Alert, ScrollView} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@navigation/index';
import {useThemeStore} from '@/store/themeStore';
import {
  isBiometricsAvailable,
  isBiometricsEnabled,
  enableBiometrics,
  disableBiometrics,
} from '@utils/biometrics';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import {BiometryTypes} from 'react-native-biometrics';
import {SafeAreaView} from '@/components';
import useTranslation from '@/hooks/useTranslation';

type BiometricsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Biometrics'>;

const BiometricsScreen = () => {
  const navigation = useNavigation<BiometricsScreenNavigationProp>();
  const theme = useThemeStore();
  const {t} = useTranslation();
  const [biometricsAvailable, setBiometricsAvailable] = useState(false);
  const [biometryType, setBiometryType] = useState<string | undefined>();
  const [isEnabled, setIsEnabled] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkBiometrics = async () => {
      // Check if biometrics are available on the device
      const {available, biometryType} = await isBiometricsAvailable();
      setBiometricsAvailable(available);
      setBiometryType(biometryType);

      // Check if biometrics are enabled in the app
      if (available) {
        const enabled = await isBiometricsEnabled();
        setIsEnabled(enabled);
      }

      setLoading(false);
    };

    checkBiometrics();
  }, []);

  const handleGoBack = () => {
    navigation.goBack();
  };

  const toggleBiometrics = async (value: boolean) => {
    try {
      if (value) {
        // Enable biometrics
        const success = await enableBiometrics();
        if (success) {
          setIsEnabled(true);
          Alert.alert('Success', t('BiometricsScreen.biometricAuthenticationEnabled'));
        } else {
          Alert.alert('Error', t('BiometricsScreen.failedToEnableBiometricAuthentication'));
        }
      } else {
        // Disable biometrics
        const success = await disableBiometrics();
        if (success) {
          setIsEnabled(false);
          Alert.alert('Success', t('BiometricsScreen.biometricAuthenticationDisabled'));
        } else {
          Alert.alert('Error', t('BiometricsScreen.failedToDisableBiometricAuthentication'));
        }
      }
    } catch (error) {
      console.error('Error toggling biometrics:', error);
      Alert.alert('Error', t('BiometricsScreen.errorOccurredWhileUpdatingBiometricSettings'));
    }
  };

  const getBiometryTypeText = () => {
    if (!biometryType) {
      return t('BiometricsScreen.biometrics');
    }

    switch (biometryType) {
      case BiometryTypes.FaceID:
        return t('BiometricsScreen.faceId');
      case BiometryTypes.TouchID:
        return t('BiometricsScreen.touchId');
      default:
        return t('BiometricsScreen.biometrics');
    }
  };

  const renderBiometricsIcon = () => {
    if (!biometryType) {
      return null;
    }

    const iconName = biometryType === BiometryTypes.FaceID ? 'user' : 'fingerprint';
    return <FontAwesome name={iconName} size={50} color={theme.colors.primary} />;
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, {backgroundColor: theme.colors.background}]}>
        <Text style={[styles.loadingText, {color: theme.colors.text}]}>{t('common.loading')}</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView includeTop style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
          <FontAwesome name="arrow-left" size={20} color={theme.colors.primary} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, {color: theme.colors.text}]}>
          {t('BiometricsScreen.title')}
        </Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView contentContainerStyle={styles.contentContainer}>
        {!biometricsAvailable ? (
          <View style={styles.notAvailableContainer}>
            <FontAwesome name="lock" size={50} color={theme.colors.primary} />
            <Text style={[styles.notAvailableText, {color: theme.colors.text}]}>
              {t('BiometricsScreen.notAvailable')}
            </Text>
          </View>
        ) : (
          <>
            <View style={styles.iconContainer}>{renderBiometricsIcon()}</View>

            <Text style={[styles.title, {color: theme.colors.text}]}>
              {getBiometryTypeText()} {t('BiometricsScreen.protection')}
            </Text>

            <Text style={[styles.description, {color: theme.colors.text}]}>
              {t('BiometricsScreen.use')} {getBiometryTypeText()} {t('BiometricsScreen.toSecure')}
            </Text>

            <View style={[styles.optionContainer, {backgroundColor: theme.colors.background}]}>
              <Text style={[styles.optionText, {color: theme.colors.text}]}>
                {t('BiometricsScreen.enable')} {getBiometryTypeText()}
              </Text>
              <Switch
                trackColor={{false: theme.colors.silver, true: theme.colors.primary}}
                thumbColor={isEnabled ? theme.colors.white : theme.colors.white}
                ios_backgroundColor={theme.colors.silver}
                onValueChange={toggleBiometrics}
                value={isEnabled}
              />
            </View>

            <Text style={[styles.infoText, {color: theme.colors.text}]}>
              {t('BiometricsScreen.whenEnabled')} {getBiometryTypeText()}{' '}
              {t('BiometricsScreen.required')}
            </Text>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 36,
  },
  contentContainer: {
    padding: 20,
    flexGrow: 1,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
  },
  iconContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  optionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginVertical: 8,
  },
  optionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  infoText: {
    fontSize: 14,
    marginTop: 16,
    textAlign: 'center',
    opacity: 0.8,
  },
  notAvailableContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  notAvailableText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
    lineHeight: 24,
  },
});

export default BiometricsScreen;
