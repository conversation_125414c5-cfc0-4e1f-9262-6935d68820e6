import React from 'react';
import {View, StyleSheet, Image, ImageSourcePropType, ViewStyle, ImageStyle} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import Typography from '@/components/Typography';
import CButton from '@/components/CButton';

interface NoDataProps {
  title?: string;
  message?: string;
  icon?: ImageSourcePropType;
  buttonTitle?: string;
  onButtonPress?: () => void;
  containerStyle?: ViewStyle;
  variant?: 'primary' | 'secondary';
  imageStyle?: ImageStyle;
}

const NoData: React.FC<NoDataProps> = ({
  title = 'No Data Found',
  message = 'There is no data available at the moment',
  icon,
  buttonTitle,
  onButtonPress,
  containerStyle,
  variant = 'primary',
  imageStyle,
}) => {
  const theme = useThemeStore();

  return (
    <View style={[styles(theme).container, containerStyle]}>
      {icon && (
        <Image source={icon} style={[styles(theme).image, imageStyle]} resizeMode="contain" />
      )}

      <Typography
        variant="title"
        style={styles(theme).title}
        color={variant === 'primary' ? theme.colors.activeColor : theme.colors.white}>
        {title}
      </Typography>

      <Typography variant="body" style={styles(theme).message} color={theme.colors.offWhite}>
        {message}
      </Typography>

      {buttonTitle && onButtonPress && (
        <View style={styles(theme).buttonContainer}>
          <CButton
            title={buttonTitle}
            variant={variant === 'primary' ? 'primary' : 'secondary'}
            onPress={onButtonPress}
          />
        </View>
      )}
    </View>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    image: {
      width: 120,
      height: 120,
      marginBottom: 20,
    },
    title: {
      marginBottom: 8,
      textAlign: 'center',
    },
    message: {
      textAlign: 'center',
      marginBottom: 24,
    },
    buttonContainer: {
      marginTop: 16,
      width: '80%',
    },
  });

export default NoData;
