import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    containerImage: {
      flex: 1,
    },
    separator: {
      height: 16,
    },
    headerContainer: {
      flexDirection: 'row',
      gap: 5,
      marginBottom: 40,
    },
    pushNotificationContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 3,
    },
    pillContainer: {
      height: 57,
      width: '49%',
      paddingTop: 2,
      paddingLeft: 20,
      borderTopLeftRadius: 11.72,
      borderTopRightRadius: 11.72,
      borderBottomLeftRadius: 11.72,
      borderBottomRightRadius: 0,
    },
    innerView: {
      flexGrow: 1,
      borderWidth: 1,
      borderRadius: 8,
      borderColor: theme.colors.white,
      margin: 12,
      // paddingHorizontal: 20,
    },
    closeButton: {
      position: 'absolute',
      top: 10,
      right: 10,
    },
    contentInnerView: {
      flex: 1,
      paddingHorizontal: 16,
    },
  });
