import React, {useState, useEffect, useCallback, useMemo} from 'react';
import {TouchableOpacity, View, ScrollView, FlatList} from 'react-native';
import {styles as createStyles} from './styles';
import ParkCard from '../ParkCard';
import Typography from '../Typography';
import Tabs from '../Tabs';
import * as yup from 'yup';
import RadioSelect from '../RadioSelect';
import {FONT_SIZE} from '@/utils/fonts';
import CustomDropdown from '../CustomDropdown/CustomDropdown';
import {equipmentData} from '@/config/staticData';
import CButton from '../CButton';
import {Icon, CustomModal, CImage} from '@/components';
import EquipmentDetailModal from '../EquipmentDetailModal';
import {sampleRacquetData} from '../EquipmentDetailModal/sampleData';
import {useThemeStore} from '@/store/themeStore';
import {Images} from '@/config';
import {useConfigStore} from '@/store';
import useTranslation from '@/hooks/useTranslation';
import {useNavigation} from '@react-navigation/native';
// Define types for the racquet data
interface Racquet {
  id: string;
  name: string;
  brand: string;
  more?: string;
}

interface RacquetData {
  image?: any;
  [key: string]: Racquet[] | any;
}

interface Tab {
  id: string;
  iconName: string;
  name: string;
}

// Define the validation schema
const validationSchema = yup.object().shape({
  selections: yup
    .object()
    .test('at-least-one-selection', 'Please select at least one racquet', value =>
      Object.values(value || {}).some(selected => selected !== ''),
    ),
});

const NavigationFooter = ({
  activeTab,
  onPrevious,
  onNext,
  styles,
  tabs,
}: {
  activeTab: string;
  onPrevious: () => void;
  onNext: () => void;
  styles: any;
  tabs: Tab[];
}) => {
  const {t} = useTranslation();
  return (
    <View style={styles.navigationButtons}>
      <CButton
        title={t('EquipmentReservation.previous')}
        onPress={onPrevious}
        isDisabled={tabs.findIndex(tab => tab.name === activeTab) === 0}
        containerStyle={[styles.navigationButton, {borderRadius: 8}]}
        variant="outline"
      />
      <CButton
        title={t('EquipmentReservation.next')}
        onPress={onNext}
        variant="primary"
        containerStyle={styles.navigationButton}
      />
    </View>
  );
};

// Add new RacquetSelection component
const RacquetSelection = React.memo(
  ({
    racquet,
    isSelected,
    onSelect,
    onMorePress,
  }: {
    racquet: Racquet;
    isSelected: boolean;
    onSelect: () => void;
    onMorePress: () => void;
  }) => {
    const theme = useThemeStore();
    const styles = createStyles(theme);

    return (
      <View style={styles.racquetItem}>
        <RadioSelect
          label={racquet.name}
          selected={isSelected}
          onPress={onSelect}
          textStyle={{color: theme.colors.text, fontSize: FONT_SIZE.lg}}
        />
        {racquet.more && (
          <TouchableOpacity style={styles.moreButton} activeOpacity={0.7} onPress={onMorePress}>
            <Typography variant="body" style={styles.moreText}>
              {racquet.more}
            </Typography>
          </TouchableOpacity>
        )}
      </View>
    );
  },
);

// Move static data outside component
const tabRacquetData: {[key: string]: RacquetData} = {
  racket: {
    'Recommended Tennis Racquets': [
      {id: '1', name: 'Dunlop CX 200', brand: 'Dunlop', more: 'more'},
      {id: '2', name: 'BOOM PRO', brand: 'BOOM', more: 'more'},
      {id: '3', name: 'Babolat Pure Aero 98 X2', brand: 'Babolat', more: 'more'},
    ],
    Dunlop: [
      {id: '4', name: 'Dunlop CX 200', brand: 'Dunlop', more: 'more'},
      {id: '5', name: 'Dunlop FX 500', brand: 'Dunlop', more: 'more'},
    ],
    Wilson: [
      {id: '6', name: 'Wilson Blade V8 98', brand: 'Wilson', more: 'more'},
      {id: '7', name: 'Wilson Clash V2 98', brand: 'Wilson', more: 'more'},
      {id: '8', name: 'Wilson Ultra V4 100', brand: 'Wilson', more: 'more'},
    ],
  },
  padel: {
    'Popular Padel Racquets': [
      {id: '9', name: 'Head Speed Pro', brand: 'Head', more: 'more'},
      {id: '10', name: 'Yonex EZONE', brand: 'Yonex', more: 'more'},
    ],
    Head: [
      {id: '11', name: 'Head Prestige', brand: 'Head', more: 'more'},
      {id: '12', name: 'Head Gravity', brand: 'Head', more: 'more'},
    ],
  },
  ball: {
    'Tennis Balls': [
      {id: '13', name: 'Wilson US Open', brand: 'Wilson', more: 'more'},
      {id: '14', name: 'Penn Championship', brand: 'Penn', more: 'more'},
    ],
  },
  bag: {
    image: Images.shoes,
    'Tennis Bags': [
      {id: '15', name: 'Wilson RF97 Bag', brand: 'Wilson', more: 'more'},
      {id: '16', name: 'Babolat Pure Drive Bag', brand: 'Babolat', more: 'more'},
    ],
  },
};

const options = [
  {label: 'Adult', value: 'adult'},
  {label: 'Junior', value: 'junior'},
];

const tabs: Tab[] = [
  {id: '1', iconName: 'racket4', name: 'racket'},
  {id: '2', iconName: 'racket3', name: 'padel'},
  {id: '3', iconName: 'ball', name: 'ball'},
  {id: '4', iconName: 'ball', name: 'bag'},
];

// Memoize HeaderComponent
const HeaderComponent = React.memo(
  ({
    bookingData,
    activeTab,
    handleTabChange,
    setIsModalVisible,
    theme,
    styles,
    racquetData,
    selectedSport,
    setSelectedSport,
  }: {
    bookingData: any;
    activeTab: string;
    handleTabChange: (tab: string) => void;
    setIsModalVisible: (visible: boolean) => void;
    theme: any;
    styles: any;
    racquetData: RacquetData;
    selectedSport: string;
    setSelectedSport: (sport: string) => void;
  }) => (
    <>
      <ParkCard
        variant="equipment"
        parkData={bookingData.selectedPark}
        date={bookingData.dateTime?.startDate}
        time={bookingData.dateTime?.startTime}
      />
      <View style={styles.divider} />
      <Typography style={styles.title} variant="subTitle3">
        {t('EquipmentReservation.reserveOrPurchaseEquipment')}
      </Typography>
      <View style={[styles.divider, {marginTop: 0}]} />
      <View style={styles.filterContainer}>
        <View style={styles.tabsContainer}>
          <Tabs
            variant="equipment"
            tabs={tabs.map(tab => tab.name)}
            activeTab={activeTab}
            onTabPress={handleTabChange}
            listContainerStyle={{
              paddingLeft: 0,
            }}
          />
        </View>
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => {
            setIsModalVisible(true);
          }}
          style={styles.filterButton}>
          <Icon name="filter" size={28} color={theme.colors.white} />
        </TouchableOpacity>
      </View>
      <CustomDropdown
        sportsData={equipmentData}
        value={selectedSport}
        onChangeValue={val => setSelectedSport(val)}
        containerStyle={styles.dropdownContainer}
      />

      {/* Display image for the active tab */}
      {racquetData.image && (
        <CImage source={racquetData.image} style={styles.categoryImage} resizeMode="cover" />
      )}
    </>
  ),
);

const EquipmentReservation = ({onSubmit}: {onSubmit: (data: any) => void}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const [activeTab, setActiveTab] = useState<string>(tabs[0].name);
  const [selectedSport, setSelectedSport] = useState<string>('');
  const {t} = useTranslation();

  const {getCartData, setCartData, bookingData} = useConfigStore();

  const navigation = useNavigation();
  // Initialize tab selections with data from MMKV if available
  const initializeTabSelections = useCallback(() => {
    // Default empty selections
    const defaultSelections = {
      racket: {},
      padel: {},
      ball: {},
      bag: {},
    };

    // If equipment data exists in bookingData, use it to initialize selections
    if (bookingData.equipment) {
      return bookingData.equipment;
    }

    return defaultSelections;
  }, [bookingData.equipment]);

  // Track selections for each tab
  const [tabSelections, setTabSelections] = useState<{
    [key: string]: {[key: string]: string};
  }>(initializeTabSelections());

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [detailModal, setDetailModal] = useState(false);

  const [racquetData, setRacquetData] = useState<RacquetData>(tabRacquetData[activeTab]);
  const [selectedValue, setSelectedValue] = React.useState<string | null>(null);

  // Update racquet data when tab changes
  useEffect(() => {
    setRacquetData(tabRacquetData[activeTab]);
  }, [activeTab]);

  // Set active tab based on existing equipment selection if available
  useEffect(() => {
    if (bookingData.equipment) {
      // Find the first tab that has selections
      for (const tab of tabs) {
        if (
          bookingData.equipment[tab.name] &&
          Object.values(bookingData.equipment[tab.name]).some(val => val !== '')
        ) {
          setActiveTab(tab.name);
          break;
        }
      }
    }
  }, [bookingData.equipment]);

  const handleRacquetSelection = useCallback(
    (racquet: Racquet, category: string) => {
      setTabSelections(prev => {
        const newSelections = {
          ...prev[activeTab],
          [category]: prev[activeTab][category] === racquet.name ? '' : racquet.name,
        };

        validationSchema
          .validate({selections: newSelections})
          .then(() => {
            console.log('Valid selections:', newSelections);
          })
          .catch(error => {
            console.error('Validation error:', error.message);
          });

        return {
          ...prev,
          [activeTab]: newSelections,
        };
      });
    },
    [activeTab],
  );

  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab);
  }, []);

  const handleNext = useCallback(() => {
    const currentIndex = tabs.findIndex(tab => tab.name === activeTab);
    if (currentIndex < tabs.length - 1) {
      setActiveTab(tabs[currentIndex + 1].name);
    }
    if (currentIndex === 3) {
      onSubmit(tabSelections);
    }
  }, [activeTab, onSubmit, tabSelections]);

  const handlePrevious = useCallback(() => {
    const currentIndex = tabs.findIndex(tab => tab.name === activeTab);
    if (currentIndex > 0) {
      setActiveTab(tabs[currentIndex - 1].name);
    }
  }, [activeTab]);

  const handleAddToCart = (cartItem: any) => {
    const existingCartData = getCartData() || [];
    const itemExists = existingCartData.some(item => item.id === cartItem.id);

    if (!itemExists) {
      // Add to cart (append don't replace)
      setCartData([...existingCartData, cartItem]);
    }
    navigation.navigate('CartScreen');
  };

  const renderRacquetSection = useCallback(
    ({item, index}: {item: {brand: string; racquets: Racquet[]}; index: number}) => (
      <View style={styles.sectionContainer}>
        <Typography
          variant={index === 0 ? 'subTitle3' : 'subtitle'}
          style={index === 0 ? styles.mainBrandTitle : styles.brandTitle}>
          {item.brand}
        </Typography>
        {item.racquets.map((racquet: Racquet) => (
          <RacquetSelection
            key={racquet.id}
            racquet={racquet}
            isSelected={tabSelections[activeTab][item.brand] === racquet.name}
            onSelect={() => handleRacquetSelection(racquet, item.brand)}
            onMorePress={() => setDetailModal(true)}
          />
        ))}
      </View>
    ),
    [activeTab, handleRacquetSelection, styles, tabSelections],
  );

  const formattedData = useMemo(
    () =>
      Object.entries(racquetData)
        .filter(([key]) => key !== 'image')
        .map(([brand, racquets]) => ({
          brand,
          racquets,
        })),
    [racquetData],
  );

  return (
    <View style={styles.root}>
      <FlatList
        data={formattedData}
        renderItem={renderRacquetSection}
        keyExtractor={item => item.brand}
        showsVerticalScrollIndicator={false}
        bounces={false}
        contentContainerStyle={styles.flexGrow}
        ListHeaderComponent={
          <HeaderComponent
            bookingData={bookingData}
            activeTab={activeTab}
            handleTabChange={handleTabChange}
            setIsModalVisible={setIsModalVisible}
            theme={theme}
            styles={styles}
            racquetData={racquetData}
            selectedSport={selectedSport}
            setSelectedSport={setSelectedSport}
          />
        }
        ListFooterComponent={
          <NavigationFooter
            activeTab={activeTab}
            onPrevious={handlePrevious}
            onNext={handleNext}
            styles={styles}
            tabs={tabs}
          />
        }
      />
      <CustomModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        variant="bottom"
        showCloseButton={true}
        title={t('EquipmentReservation.filterResults')}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.filter}>
            <View style={styles.content}>
              <View style={styles.filterItem}>
                <Icon name="Book-1" size={35} color={theme.colors.primary} />
                <Typography variant="subtitle" style={styles.text}>
                  {t('EquipmentReservation.availableNowInKiosk')}
                </Typography>
              </View>
              <Typography variant="subtitle" style={{color: theme.colors.offWhite}}>
                {t('EquipmentReservation.age')}
              </Typography>
              {options.map(option => (
                <RadioSelect
                  key={option.value}
                  label={option.label}
                  selected={selectedValue === option.value}
                  onPress={() => setSelectedValue(option.value)}
                />
              ))}
            </View>
            <CButton
              title={t('EquipmentReservation.apply')}
              onPress={() => setIsModalVisible(false)}
              variant="primary"
              containerStyle={styles.btn}
            />
          </View>
        </ScrollView>
      </CustomModal>

      <EquipmentDetailModal
        equipment={sampleRacquetData}
        visible={detailModal}
        onClose={() => setDetailModal(false)}
        onAdd={() => handleAddToCart(sampleRacquetData)}
      />
    </View>
  );
};

export default React.memo(EquipmentReservation);
