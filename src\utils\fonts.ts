// Define the font families
export const FONTS = {
  regular: 'Helvetica',
  medium: 'Helvetica-Medium',
  light: 'Helvetica-Light',
  thin: 'Helvetica-Thin',
  bold: 'Helvetica-Bold',
};

// Font size presets
export const FONT_SIZE = {
  xs: 10,
  sm: 12,
  sm1: 13,
  sm2: 12.49,
  sm3: 13.6,
  md: 14,
  md1: 15,
  lg: 16,
  xl: 18,
  xxl: 20,
  xxxl: 22,
  subTitle: 24,
  subTitle1: 24.84,
  subTitle2: 26,
  subTitle3: 25,
  title: 28,
  subHeading: 30,
  heading: 32,
  headingMd: 34,
  headingLg: 36,
};

// Helper function to get font styles
export const getFontStyle = (size = FONT_SIZE.md, fontFamily = FONTS.regular, color?: string) => ({
  fontSize: size,
  fontFamily,
  ...(color ? {color} : {}),
});

export default FONTS;
