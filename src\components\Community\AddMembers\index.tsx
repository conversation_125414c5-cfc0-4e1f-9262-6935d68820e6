import React, {useEffect, useState} from 'react';
import {View, TouchableOpacity, FlatList, TouchableWithoutFeedback} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import Typography from '@/components/Typography';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import {createStyles} from './Styles';
import CIcon from '@/components/CIcon';
import CInput from '@/components/CInput';
import PlayerCard from '@/components/PlayerCard';
import CustomModal from '@/components/Modal';
import RadioSelect from '@/components/RadioSelect';
import RatingSlider from '@/components/RatingSlider';
import SearchInput from '@/components/SearchInput';
import CButton from '@/components/CButton';
import {SafeAreaView} from '@/components';
import useTranslation from '@/hooks/useTranslation';

type NavigationProp = NativeStackNavigationProp<CommunityStackParamList>;

const members = [
  {
    id: '1',
    name: '<PERSON>',
    rating: '3.5',
    coach: true,
    location: 'Fort Greene',
    image: 'https://randomuser.me/api/portraits/men/1.jpg',
  },
  {
    id: '2',
    name: 'Jane Doe',
    rating: '3.5',
    coach: false,
    location: 'Lincoln Terrace',
    image: 'https://randomuser.me/api/portraits/women/2.jpg',
  },
  {
    id: '3',
    name: 'Babolat',
    rating: '',
    coach: false,
    location: 'McCarren Park',
    image: 'https://randomuser.me/api/portraits/men/3.jpg',
    isPremium: true,
  },
  {
    id: '4',
    name: 'Scott Freeman',
    rating: '',
    coach: false,
    location: 'Fort Greene',
    image: 'https://randomuser.me/api/portraits/men/4.jpg',
  },
  {
    id: '5',
    name: 'Jeremy Hill',
    rating: '',
    coach: false,
    location: 'Brooklyn Heights',
    image: 'https://randomuser.me/api/portraits/men/5.jpg',
    isPremium: true,
  },
  {
    id: '6',
    name: 'Peter Parker',
    rating: '',
    coach: false,
    location: 'Fort Greene',
    image: 'https://randomuser.me/api/portraits/men/6.jpg',
  },
  {
    id: '7',
    name: 'Peter Parker',
    rating: '',
    coach: false,
    location: 'McCarren Park',
    image: 'https://randomuser.me/api/portraits/men/6.jpg',
  },

  {
    id: '8',
    name: 'Peter Parker',
    rating: '',
    coach: false,
    location: 'McCarren Park',
    image: 'https://randomuser.me/api/portraits/men/6.jpg',
  },
];

const options = [
  {label: 'Tennis', value: 'tennis'},
  {label: 'Pickleball', value: 'pickleball'},
  {label: 'Plateform Tennis', value: 'plateform-tennis'},
  {label: 'Padel', value: 'padel'},
];
const options2 = [
  {label: 'Friends', value: 'friends'},
  {label: 'Invited', value: 'invited'},
  {label: 'Sponsored', value: 'sponsored'},
];

const AddMembers = ({route}: {route: any}) => {
  const data = route?.params || {};
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();
  const [search, setSearch] = React.useState('');
  const [selectedIds, setSelectedIds] = React.useState<string[]>(data?.members || []);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedValue, setSelectedValue] = React.useState<string | null>(null);
  const [searchValue, setSearchValue] = useState('');

  const {t} = useTranslation();

  const filteredMembers = members.filter(
    m =>
      m.name.toLowerCase().includes(search.toLowerCase()) ||
      m.location.toLowerCase().includes(search.toLowerCase()),
  );

  const handleSelect = (id: string) => {
    setSelectedIds(prev => (prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]));
  };

  const renderMember = ({item}: any) => {
    return (
      <TouchableWithoutFeedback key={item.id}>
        <PlayerCard
          playerData={item}
          onSelect={() => handleSelect(item)}
          isSelected={selectedIds.some(p => p.id === item.id)}
          borderColor={theme.colors.activeColor}
        />
      </TouchableWithoutFeedback>
    );
  };

  return (
    <SafeAreaView includeTop={true} includeBottom={false}>
      <View style={styles.content}>
        <View style={styles.headerRow}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Typography variant="parkTitle" color={theme.colors.activeColor}>
              {t('common.cancel')}
            </Typography>
          </TouchableOpacity>
          <Typography variant="parkTitle" color={theme.colors.white}>
            {t('addMembersScreen.addMembers')}
          </Typography>
          <TouchableOpacity
            disabled={selectedIds?.length === 0}
            onPress={() =>
              navigation.navigate('CreateGroupMemberList', {
                ...data,
                members: selectedIds,
              })
            }>
            {/* <TouchableOpacity onPress={handleSubmit(onSubmit)}> */}
            <Typography
              variant="parkTitle"
              color={selectedIds?.length === 0 ? theme.colors.secondary : theme.colors.primary}>
              {t('common.next')}
            </Typography>
          </TouchableOpacity>
        </View>
        <Typography variant="bodyMedium" style={styles.memberCount} color={theme.colors.white}>
          {selectedIds.length}/25
        </Typography>
        <CInput
          variant="dark"
          inputStyle={styles.searchInput}
          placeholder={t('addMembersScreen.searchPlaceholder')}
          placeholderTextColorStyle={theme.colors.secondary}
          value={search}
          onChangeText={setSearch}
        />
        <View style={styles.resultsHeaderRow}>
          <Typography variant="court" color={theme.colors.white}>
            {t('addMembersScreen.results')}
          </Typography>
          <TouchableOpacity onPress={() => setIsModalVisible(true)}>
            <CIcon name="filter" size={25} color={theme.colors.white} />
          </TouchableOpacity>
        </View>
        <FlatList
          data={filteredMembers}
          renderItem={renderMember}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.membersList}
          showsVerticalScrollIndicator={false}
        />
      </View>
      <CustomModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        variant="bottom"
        showCloseButton={true}
        title="Filter results">
        <View>
          <Typography variant="subtitle" style={{color: theme.colors.offWhite}}>
            {t('addMembersScreen.show')}
          </Typography>
          {options.map(option => (
            <RadioSelect
              key={option.value}
              label={option.label}
              selected={selectedValue === option.value}
              onPress={() => setSelectedValue(option.value)}
            />
          ))}

          {/* Rating Slider */}
          <RatingSlider />

          {options2.map(option => (
            <RadioSelect
              key={option.value}
              label={option.label}
              selected={selectedValue === option.value}
              onPress={() => setSelectedValue(option.value)}
            />
          ))}

          <SearchInput
            variant="light"
            placeholder={t('addMembersScreen.searchEvent')}
            containerStyle={styles.filterInput}
            inputStyle={{color: theme.colors.primary}}
            value={searchValue}
            placeholderTextColor={theme.colors.primary}
            onChangeText={setSearchValue}
            iconColor={theme.colors.primary}
            onClear={() => setSearchValue('')}
          />
          <CButton
            title={t('addMembersScreen.results')}
            onPress={() => setIsModalVisible(false)}
            variant="primary"
            containerStyle={{
              height: 70,
            }}
          />
        </View>
      </CustomModal>
    </SafeAreaView>
  );
};

export default AddMembers;
