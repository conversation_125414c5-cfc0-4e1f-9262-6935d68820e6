import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {SearchScreen} from '@/screens/tabs';
import SearchBottomSheet from '@/components/SearchBottomSheet/SearchBottomSheet';

export type SearchStackParamList = {
  SearchScreen: undefined;
  DateScreen: undefined;
  EquipmentReservationScreen: undefined;
};

const Stack = createNativeStackNavigator<SearchStackParamList>();
const SearchScreenWithBottomSheet = () => (
  <>
    <SearchScreen />
  </>
);

const SearchStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="SearchScreen"
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen
        name="SearchScreen"
        component={SearchScreenWithBottomSheet}
        options={{animation: 'fade', presentation: 'transparentModal'}}
      />
    </Stack.Navigator>
  );
};

export default SearchStack;
