import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {SearchScreen} from '@/screens/tabs';
import SearchBottomSheet from '@/components/SearchBottomSheet/SearchBottomSheet';

export type SearchStackParamList = {
  SearchScreen: undefined;
  DateScreen: undefined;
  EquipmentReservationScreen: undefined;
};

const Stack = createStackNavigator<SearchStackParamList>();
const SearchScreenWithBottomSheet = () => (
  <>
    <SearchScreen />
  </>
);

const SearchStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="SearchScreen"
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen
        name="SearchScreen"
        component={SearchScreenWithBottomSheet}
        options={{animationEnabled: true, gestureEnabled: true}}
      />
    </Stack.Navigator>
  );
};

export default SearchStack;
