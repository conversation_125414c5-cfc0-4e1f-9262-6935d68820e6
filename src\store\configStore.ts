import {create} from 'zustand';
import {persist, createJSONStorage} from 'zustand/middleware';
import {mmkvStorage} from '@/services/storage';
import {defaultAuth} from './authStore';
import {clearTokenStorage} from '@/services/api';
import {useAuthStore} from './authStore';
// Types
type BookingStep =
  | 'search_parks'
  | 'select_date_time'
  | 'purchase_equipments'
  | 'cart_view'
  | 'create_profile'
  | 'signup'
  | 'advertisement'
  | 'subscription'
  | '';

interface NavigationOption {
  id: string;
  title: string;
  icon: string;
}

interface cartData {
  id: string;
  name: string;
  brand: string;
}

// Config state interface
interface ConfigState {
  isFirstLaunch: boolean;
  language: 'en' | 'fr' | 'es' | 'de';
  notifications: boolean;
  permissions: {
    location: boolean;
    bluetooth: boolean;
    notification: boolean;
    termsAndConditions: boolean;
  };
  bookingStep: BookingStep;
  bookingStepIndex: number;
  bookingData: Record<string, any>;
  profileSetup: boolean;
  profileStep: string;
  communityOptions?: NavigationOption[];
  coachProfile: boolean;
  cartData: cartData[];
  drawerMenuItems: {
    id: string;
    label: string;
    title: string;
    screen?: string;
    isVisible: boolean;
  }[];
  communityFeatures: {
    id: string;
    label: string;
    title: string;
    screen?: string;
    isVisible: boolean;
  }[];
}

// Default config values
export const defaultConfig: ConfigState = {
  isFirstLaunch: true,
  language: 'en',
  notifications: true,
  permissions: {
    location: false,
    bluetooth: false,
    notification: false,
    termsAndConditions: false,
  },
  bookingStep: 'search_parks',
  bookingStepIndex: 0,
  bookingData: {},
  profileSetup: false,
  profileStep: 'create_profile',
  coachProfile: false,
  cartData: [],
  drawerMenuItems: [],
  communityFeatures: [],
};

// Define the booking flow steps
const BOOKING_FLOW: BookingStep[] = [
  'search_parks',
  'select_date_time',
  'purchase_equipments',
  'cart_view',
  'create_profile',
  'signup',
  'advertisement',
  'subscription',
];

// Define the store type
type ConfigStore = ConfigState & {
  setFirstLaunch: (isFirstLaunch: boolean) => void;
  setLanguage: (language: 'en' | 'fr' | 'es' | 'de') => void;
  toggleNotifications: () => void;
  setNotifications: (notifications: boolean) => void;
  setBookingStep: (bookingStep: BookingStep) => void;
  nextBookingStep: (state?: ConfigState, caller?: string) => void;
  previousBookingStep: () => void;
  setPermissions: (permissions: {
    location: boolean;
    bluetooth: boolean;
    notification: boolean;
    termsAndConditions: boolean;
  }) => void;
  updateBookingData: (data: object, type?: string) => void;
  resetBooking: () => void;
  setProfileSetup: (profileSetup: boolean) => void;
  setProfileStep: (profileStep: string) => void;
  setCommunityOptions: (options: NavigationOption[]) => void;
  getCommunityOptions: () => NavigationOption[] | undefined;
  setCoachProfile: (coachProfile: boolean) => void;
  setCartData: (cartData: cartData[]) => void;
  getCartData: () => cartData[] | undefined;
  setDrawerMenuItems: (items: ConfigState['drawerMenuItems']) => void;
  setCommunityFeatures: (features: ConfigState['communityFeatures']) => void;
  resetAll: () => void;
};

// Create the config store with persistence
export const useConfigStore = create<ConfigStore>()(
  persist(
    (set, get) => ({
      ...defaultConfig,

      setFirstLaunch: isFirstLaunch => set({isFirstLaunch}),

      setLanguage: language => set({language}),

      toggleNotifications: () =>
        set(state => ({
          notifications: !state.notifications,
        })),

      setNotifications: notifications => set({notifications}),

      setBookingStep: bookingStep =>
        set(state => {
          const stepIndex = BOOKING_FLOW.findIndex(step => step === bookingStep);
          return {
            bookingStep,
            bookingStepIndex: stepIndex !== -1 ? stepIndex : state.bookingStepIndex,
          };
        }),

      nextBookingStep: (state, caller) => {
        const currentState = state || get();
        console.log('nextBookingStep called from', caller);

        set(prev => {
          const nextIndex = Number(currentState.bookingStepIndex) + 1;
          if (nextIndex < BOOKING_FLOW.length) {
            return {
              bookingStepIndex: nextIndex,
              bookingStep: BOOKING_FLOW[nextIndex],
            };
          }
          return prev;
        });
      },

      previousBookingStep: () =>
        set(state => {
          const prevIndex = Number(state.bookingStepIndex) - 1;
          if (prevIndex >= 0) {
            return {
              bookingStepIndex: prevIndex,
              bookingStep: BOOKING_FLOW[prevIndex],
            };
          }
          return state;
        }),

      setPermissions: permissions => set({permissions}),

      updateBookingData: (data, type) => {
        set(state => ({
          bookingData: {
            ...state.bookingData,
            ...data,
          },
        }));

        if (type === 'next') {
          const updatedState = get();
          get().nextBookingStep(updatedState, 'updateBookingData');
        }
      },

      resetBooking: () =>
        set({
          bookingStep: 'search_parks',
          bookingStepIndex: 0,
          bookingData: {},
        }),

      setProfileSetup: profileSetup => set({profileSetup}),

      setProfileStep: profileStep => set({profileStep}),

      setCommunityOptions: options => set({communityOptions: options}),

      getCommunityOptions: () => get().communityOptions,

      setCoachProfile: coachProfile => set({coachProfile}),

      setCartData: cartData => set({cartData}),

      getCartData: () => get().cartData,

      setDrawerMenuItems: items => set({drawerMenuItems: items}),

      setCommunityFeatures: features => set({communityFeatures: features}),

      resetAll: () => {
        const currentPermissions = get().permissions;
        const currentUserLocation = useAuthStore.getState().userLocation;
        const currentDrawerMenuItems = get().drawerMenuItems;
        const currentCommunityFeatures = get().communityFeatures;
        set(() => ({
          ...defaultConfig,
          ...defaultAuth,
          permissions: currentPermissions,
          userLocation: currentUserLocation,
          drawerMenuItems: currentDrawerMenuItems,
          communityFeatures: currentCommunityFeatures,
        }));
        clearTokenStorage();
      },
    }),
    {
      name: 'app-config',
      storage: createJSONStorage(() => mmkvStorage),
    },
  ),
);
