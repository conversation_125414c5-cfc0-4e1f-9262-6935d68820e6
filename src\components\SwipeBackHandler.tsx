import React from 'react';
import {View, StyleSheet} from 'react-native';
import {PanGestureHandler, State} from 'react-native-gesture-handler';
import {useNavigation} from '@react-navigation/native';

interface SwipeBackHandlerProps {
  children: React.ReactNode;
  enabled?: boolean;
  threshold?: number;
}

const SwipeBackHandler: React.FC<SwipeBackHandlerProps> = ({
  children,
  enabled = true,
  threshold = 100,
}) => {
  const navigation = useNavigation();

  const onGestureEvent = (event: any) => {
    const {translationX, velocityX, state} = event.nativeEvent;

    // Check if gesture is a swipe from left edge to right
    if (
      enabled &&
      state === State.END &&
      translationX > threshold &&
      velocityX > 0
    ) {
      // Navigate back
      if (navigation.canGoBack()) {
        navigation.goBack();
      }
    }
  };

  if (!enabled) {
    return <View style={styles.container}>{children}</View>;
  }

  return (
    <PanGestureHandler onHandlerStateChange={onGestureEvent}>
      <View style={styles.container}>{children}</View>
    </PanGestureHandler>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default SwipeBackHandler;
