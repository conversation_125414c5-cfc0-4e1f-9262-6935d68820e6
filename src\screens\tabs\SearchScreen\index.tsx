import React, {useState, useEffect} from 'react';
import {View, StyleSheet, StatusBar, TouchableWithoutFeedback} from 'react-native';
import {useNavigation, DrawerActions, useRoute, RouteProp} from '@react-navigation/native';
import {useThemeStore} from '@/store/themeStore';
import {CustomMapView} from '@/components/common';
import {Header} from '@/components';
import {mapPins} from '@/data';
import {useSharedValue, useDerivedValue} from 'react-native-reanimated';
import LocationLegend from '@/components/LocationLegend';
import {getCurrentRegion, DEFAULT_REGION} from '@/utils/locationService';
import {useAuthStore} from '@/store';
import PlayerSchedulePlayCard from '@/components/PlayerSchedulePlayCard';
import SearchBottomSheet from '@/components/SearchBottomSheet/SearchBottomSheet';

// Define the type for route params
type SearchScreenRouteParams = {
  playerData?: {
    id?: string;
    title?: string;
    description?: string;
    image?: string;
    coordinate?: {
      latitude: number;
      longitude: number;
    };
    [key: string]: any; // For any other properties
  };
  location?: string;
  playerName?: string;
  image?: string;
};

const SearchScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<Record<string, SearchScreenRouteParams>, string>>();
  const theme = useThemeStore();
  const [initialRegion, setInitialRegion] = useState(DEFAULT_REGION);
  const {setUserLocation} = useAuthStore();
  const [showPlayerScheduleContainer, setShowPlayerScheduleContainer] = useState(false);
  const [selectedMarker, setSelectedMarker] = useState<any>(null);

  // Access params passed from PlayerConnectScreen
  const params = route.params || {};
  console.log('params', params);

  //#region animated variables
  const animatedIndex = useSharedValue<number>(0);
  const animatedPosition = useSharedValue<number>(0);

  const locationLegendAnimatedIndex = useDerivedValue(() => animatedIndex.value);
  const locationLegendAnimatedPosition = useDerivedValue(() => animatedPosition.value);
  //#endregion

  // Get user's current location when component mounts
  useEffect(() => {
    const fetchUserLocation = async () => {
      try {
        const region = await getCurrentRegion();
        setInitialRegion(region);
        setUserLocation(region);
      } catch (error) {
        console.error('Error getting user location:', error);
        // Keep the default region if there's an error
      }
    };

    fetchUserLocation();
  }, []);

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const hidePlayerScheduleContainer = () => {
    setShowPlayerScheduleContainer(false);
  };

  const handleMarkerPress = (marker: any) => {
    setSelectedMarker(marker);
    setShowPlayerScheduleContainer(true);
  };

  return (
    <>
      <View style={styles(theme).mainContainer}>
        <StatusBar barStyle="light-content" backgroundColor="transparent" />

        {/* Map takes full screen as background */}
        <View style={styles(theme).mapContainer}>
          <CustomMapView
            pins={mapPins}
            initialRegion={initialRegion}
            showUserLocation={true}
            onMarkerPress={handleMarkerPress}
          />
        </View>

        {/* Location Legend Component */}
        <LocationLegend
          animatedIndex={locationLegendAnimatedIndex}
          animatedPosition={locationLegendAnimatedPosition}
        />

        {/* Header overlay */}
        <View style={styles(theme).headerContainer}>
          <Header
            leftIcon={{
              name: 'side-menu',
              size: 24,
              color: theme.colors.white,
            }}
            leftIconButtonStyle={styles(theme).menuButton}
            onLeftPress={openDrawer}
            backgroundColor="transparent"
            containerStyle={styles(theme).transparentHeader}
            headerStyle={styles(theme).headerWithoutBorder}
          />
        </View>
        {showPlayerScheduleContainer && (
          <>
            <TouchableWithoutFeedback onPress={hidePlayerScheduleContainer}>
              <View style={styles(theme).overlay} />
            </TouchableWithoutFeedback>

            <View style={styles(theme).playerScheduleContainer}>
              <PlayerSchedulePlayCard
                playerName={selectedMarker?.title || 'John Doe'}
                location={selectedMarker?.description || '123 Main St, Anytown, USA'}
                image={selectedMarker?.image || 'https://via.placeholder.com/150'}
                onPress={() => {
                  hidePlayerScheduleContainer();
                }}
              />
            </View>
          </>
        )}
      </View>
      <SearchBottomSheet closePlayerScheduleCard={hidePlayerScheduleContainer} />
    </>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    mainContainer: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    playerScheduleContainer: {
      flexDirection: 'column',
      alignItems: 'center',
      paddingHorizontal: 16,
      position: 'absolute',
      bottom: 240,
      left: 0,
      right: 0,
      zIndex: 1,
    },
    overlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 0,
    },
    mapContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    headerContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
    },
    transparentHeader: {
      backgroundColor: 'transparent',
    },
    headerWithoutBorder: {
      borderBottomWidth: 0,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'rgba(40, 142, 206, 0.7)',
      shadowColor: theme.colors.black,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
  });

export default SearchScreen;
