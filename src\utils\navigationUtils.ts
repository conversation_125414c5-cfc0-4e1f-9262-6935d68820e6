import {getFocusedRouteNameFromRoute} from '@react-navigation/native';
import {Route} from '@react-navigation/native';

/**
 * Determines if the tab bar should be hidden based on the current route
 *
 * @param route - The current route object
 * @param hideTabScreens - Array of screen names where the tab bar should be hidden
 * @param keyboardVisible - Whether the keyboard is currently visible
 * @param defaultTabBarStyle - The default style to apply when the tab bar is visible
 * @returns The tab bar style object with display property set to 'none' or the default style
 */
export const getTabBarVisibility = (
  route: Route<string>,
  hideTabScreens: string[],
  keyboardVisible: boolean,
  defaultTabBarStyle: object,
) => {
  // Get the name of the currently focused route
  const routeName = getFocusedRouteNameFromRoute(route);

  // Hide the tab bar if the current screen is in the hideTabScreens array
  // or if the keyboard is visible
  if (hideTabScreens.includes(routeName as string) || keyboardVisible) {
    return {
      ...defaultTabBarStyle,
      height: 0,
      paddingBottom: 0,
      paddingTop: 0,
      overflow: 'hidden',
      borderTopWidth: 0,
    };
  }

  // Otherwise, return the default tab bar style
  return defaultTabBarStyle;
};

/**
 * Predefined lists of screens where the tab bar should be hidden
 */
export const HIDE_TAB_SCREENS = {
  // Group-related screens
  GROUP_SCREENS: [
    'Chat',
    'MyGroups',
    'NewGroup',
    'JoinGroups',
    'JoinGroupDetails',
    'AddMembers',
    'CreateGroupMemberList',
  ],

  // GoLife-related screens
  GOLIFE_SCREENS: [
    'GoFit',
    'GoEats',
    'GoTravel',
    'CommunityDetails',
    'CommentScreen',
    'CoachProfile',
    'FindCoach',
    'FindClass',
    'PlayerConnectDateScreen',
  ],

  // Get all screens where tab bar should be hidden
  getAllHiddenScreens: () => [
    ...HIDE_TAB_SCREENS.GROUP_SCREENS,
    ...HIDE_TAB_SCREENS.GOLIFE_SCREENS,
  ],
};
