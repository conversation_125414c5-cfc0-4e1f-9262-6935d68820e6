import React from 'react';
import {StyleSheet} from 'react-native';
import {
  SafeAreaView as RNSafeAreaView,
  Edge,
  SafeAreaViewProps,
} from 'react-native-safe-area-context';

/**
 * Custom props for our SafeAreaView component
 */
interface SafeAreaViewWrapperProps extends SafeAreaViewProps {
  /**
   * Whether to include the top edge (default: false)
   * Set to true for screens without a header
   */
  includeTop?: boolean;

  /**
   * Whether to include the bottom edge (default: true)
   */
  includeBottom?: boolean;
}

/**
 * A custom SafeAreaView component that applies the correct edges by default
 *
 * @param props - Props to pass to SafeAreaView
 * @returns A SafeAreaView component with proper edge configuration
 */
const SafeAreaViewWrapper: React.FC<SafeAreaViewWrapperProps> = ({
  children,
  style,
  edges,
  includeTop = false,
  includeBottom = true,
  ...rest
}) => {
  // Build the edges array based on props
  const defaultEdges: Edge[] = [];

  // Only include top edge if explicitly requested
  if (includeTop) {
    defaultEdges.push('top');
  }

  // Include bottom edge by default unless explicitly disabled
  if (includeBottom) {
    defaultEdges.push('bottom');
  }

  // Always include left and right edges
  defaultEdges.push('left', 'right');

  // Use provided edges or default edges
  const finalEdges = edges || defaultEdges;

  return (
    <RNSafeAreaView style={[styles.container, style]} edges={finalEdges} {...rest}>
      {children}
    </RNSafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default SafeAreaViewWrapper;
