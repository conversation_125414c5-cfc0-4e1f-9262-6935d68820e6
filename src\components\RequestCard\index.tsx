import React from 'react';
import {View} from 'react-native';
import Typography from '@/components/Typography';
import {useThemeStore} from '@/store/themeStore';
import {styles as CreateStyles} from './styles';

interface RequestCardProps {
  type: string;
  status: string;
  date: string;
}

const RequestCard: React.FC<RequestCardProps> = ({type, status, date}) => {
  const theme = useThemeStore();
  const styles = CreateStyles(theme);

  return (
    <View style={styles.requestCard}>
      <Typography variant="openingTitle" color={theme.colors.white}>
        Request for
      </Typography>
      <Typography variant="subTitle3" color={theme.colors.white}>
        {type}
      </Typography>
      <Typography
        variant="frequencyTitle"
        color={theme.colors.white}
        style={{position: 'absolute', right: 16, top: 12}}>
        {date}
      </Typography>
      <Typography
        variant="openingTitle"
        color={theme.colors.white}
        style={{marginTop: 15, lineHeight: 20}}>
        Status:{' '}
        <Typography variant="status" color={theme.colors.white} style={{marginBottom: 12}}>
          {status}
        </Typography>
      </Typography>
    </View>
  );
};

export default RequestCard;
