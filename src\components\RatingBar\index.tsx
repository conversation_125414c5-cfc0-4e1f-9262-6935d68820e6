import React from 'react';
import {View, TouchableOpacity, StyleSheet, StyleProp, ViewStyle} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import CIcon from '@components/CIcon';

interface RatingBarProps {
  value: number;
  onChange: (value: number) => void;
  containerStyle?: StyleProp<ViewStyle>;
  iconSize?: number;
  ratingCount?: number;
}

const RatingBar: React.FC<RatingBarProps> = ({
  value,
  onChange,
  containerStyle,
  iconSize = 20,
  ratingCount = 5,
}) => {
  const theme = useThemeStore();

  return (
    <View style={[styles.ratingContainer, containerStyle]}>
      {Array.from({length: ratingCount}, (_, i) => i + 1).map(i => (
        <TouchableOpacity activeOpacity={0.8} key={i} onPress={() => onChange(i)}>
          <CIcon
            name="ball2"
            size={iconSize}
            color={i <= value ? theme.colors.primary : theme.colors.text}
            style={styles.icon}
          />
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginHorizontal: 4,
  },
});

export default RatingBar;
