import {StyleSheet} from 'react-native';

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    title: {
      textAlign: 'center',
      marginBottom: 10,
      fontWeight: 'bold',
    },
    pillContainer: {
      width: '100%',
      flexGrow: 1,
      paddingHorizontal: 16,
      paddingBottom: 20,
      marginTop: 10,
    },
    separator: {
      height: 25,
    },
    // Filter styles
    filterContainer: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    filterTitle: {
      marginBottom: 20,
      textAlign: 'center',
      color: theme.colors.activeColor,
    },
    sectionTitle: {
      marginBottom: 5,
      color: theme.colors.offWhite,
    },
    radioOption: {
      marginVertical: 4,
    },
    applyButton: {
      backgroundColor: theme.colors.activeColor,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 25,
      alignSelf: 'center',
      marginTop: 20,
    },
    applyButtonText: {
      color: theme.colors.black,
      fontSize: 16,
      fontWeight: '600',
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.white2,
      marginVertical: 15,
      width: '90%',
      alignSelf: 'center',
    },
  });

export default styles;
