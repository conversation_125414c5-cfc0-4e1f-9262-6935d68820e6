import {Platform, PermissionsAndroid} from 'react-native';
import {check, request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import Geolocation from '@react-native-community/geolocation';

// Default region for NYC
export const DEFAULT_REGION = {
  latitude: 40.7128,
  longitude: -74.006,
  latitudeDelta: 0.0922,
  longitudeDelta: 0.0421,
};

// Default zoom level for the map
export const DEFAULT_ZOOM_LEVEL = {
  latitudeDelta: 0.0922,
  longitudeDelta: 0.0421,
};

// Closer zoom level for user location
export const USER_LOCATION_ZOOM_LEVEL = {
  latitudeDelta: 0.01,
  longitudeDelta: 0.01,
};

/**
 * Check if location permission is granted
 * @returns Promise<boolean>
 */
export const checkLocationPermission = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'ios') {
      const result = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
      return result === RESULTS.GRANTED;
    } else {
      const granted = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      );
      return granted;
    }
  } catch (error) {
    console.error('Error checking location permission:', error);
    return false;
  }
};

/**
 * Request location permission
 * @returns Promise<boolean>
 */
export const requestLocationPermission = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'ios') {
      const result = await request(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
      return result === RESULTS.GRANTED;
    } else {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
  } catch (error) {
    console.error('Error requesting location permission:', error);
    return false;
  }
};

/**
 * Get the current position of the user
 * This implementation uses the built-in Geolocation API from React Native
 * @returns Promise with the current position or null if permission is not granted
 */
export const getCurrentPosition = async (): Promise<{
  latitude: number;
  longitude: number;
} | null> => {
  try {
    // First check if we have permission
    const hasPermission = await requestLocationPermission();

    if (!hasPermission) {
      console.log('Location permission not granted');
      return null;
    }

    // Configure geolocation if needed
    if (Platform.OS === 'ios') {
      Geolocation.setRNConfiguration({
        skipPermissionRequests: false,
        authorizationLevel: 'whenInUse',
      });
    }

    return new Promise<{latitude: number; longitude: number} | null>(resolve => {
      try {
        Geolocation.getCurrentPosition(
          position => {
            resolve({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            });
          },
          error => {
            console.error('Error getting current position:', error);
            // Always resolve with null on error to prevent crashes
            resolve(null);
          },
          {
            enableHighAccuracy: Platform.OS === 'ios', // High accuracy can cause issues on Android
            timeout: 15000,
            maximumAge: 10000,
            distanceFilter: 10,
          },
        );
      } catch (e) {
        console.error('Exception in getCurrentPosition:', e);
        resolve(null);
      }
    });
  } catch (error) {
    console.error('Unexpected error in getCurrentPosition:', error);
    return null;
  }
};

/**
 * Get the current region for the map based on user's location
 * @param zoomLevel Optional zoom level to use
 * @returns Promise with the current region or default region if location is not available
 */
export const getCurrentRegion = async (zoomLevel = USER_LOCATION_ZOOM_LEVEL) => {
  try {
    const position = await getCurrentPosition();
    console.log('getCurrentRegion position:', position);

    if (position) {
      return {
        latitude: position.latitude,
        longitude: position.longitude,
        ...zoomLevel,
      };
    }

    return DEFAULT_REGION;
  } catch (error) {
    console.error('Error getting current region:', error);
    return DEFAULT_REGION;
  }
};
