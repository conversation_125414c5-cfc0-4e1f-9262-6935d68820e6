# Script to enable long paths in Windows
# Run this script as Administrator

# Check if running as Administrator
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "This script needs to be run as Administrator. Please restart PowerShell as Administrator and try again." -ForegroundColor Red
    exit
}

# Enable long paths in Windows registry
Write-Host "Enabling long paths in Windows registry..." -ForegroundColor Yellow
Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" -Name "LongPathsEnabled" -Value 1 -Type DWord

# Check if the change was successful
$longPathsEnabled = (Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" -Name "LongPathsEnabled").LongPathsEnabled

if ($longPathsEnabled -eq 1) {
    Write-Host "Long paths have been enabled successfully!" -ForegroundColor Green
    Write-Host "Please restart your computer for the changes to take effect." -ForegroundColor Yellow
} else {
    Write-Host "Failed to enable long paths. Please try again." -ForegroundColor Red
}
