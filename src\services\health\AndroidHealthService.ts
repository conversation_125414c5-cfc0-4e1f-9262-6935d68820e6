import {Platform} from 'react-native';
import GoogleFit, {Scopes} from 'react-native-google-fit';
import {HealthServiceInterface} from './types';
import {format} from 'date-fns';

/**
 * Android implementation of the health service using Google Fit
 */
class AndroidHealthService implements HealthServiceInterface {
  private isInitialized = false;
  private stepGoal = 10000; // Default step goal

  /**
   * Initialize Google Fit
   */
  async initialize(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    if (this.isInitialized) {
      return true;
    }

    try {
      const options = {
        scopes: [
          Scopes.FITNESS_ACTIVITY_READ,
          Scopes.FITNESS_BODY_READ,
          Scopes.FITNESS_HEART_RATE_READ,
        ],
      };

      const authResult = await GoogleFit.authorize(options);

      if (authResult.success) {
        this.isInitialized = true;
        return true;
      } else {
        console.error('Google Fit authorization failed:', authResult.message);
        return false;
      }
    } catch (error) {
      console.error('Error initializing Google Fit:', error);
      return false;
    }
  }

  /**
   * Request permissions for Google Fit
   */
  async requestPermissions(): Promise<boolean> {
    return this.initialize();
  }

  /**
   * Check if Google Fit permissions are granted
   */
  async hasPermissions(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      const authResult = await GoogleFit.checkIsAuthorized();
      return authResult.authorized;
    } catch (error) {
      console.error('Error checking Google Fit permissions:', error);
      return false;
    }
  }

  /**
   * Get step count for a specific date
   */
  async getStepCount(date: Date = new Date()): Promise<{count: number; date: string}> {
    if (Platform.OS !== 'android' || !this.isInitialized) {
      await this.initialize();
    }

    try {
      const options = {
        startDate: new Date(date.setHours(0, 0, 0, 0)).toISOString(), // Start of the day
        endDate: new Date(date.setHours(23, 59, 59, 999)).toISOString(), // End of the day
        bucketUnit: 'DAY',
        bucketInterval: 1,
      };

      const res = await GoogleFit.getDailyStepCountSamples(options);

      // Find the steps from the most reliable source
      let steps = 0;

      // First try to get steps from Google Fit's own source
      const googleFitSource = res.find(
        source => source.source === 'com.google.android.gms:estimated_steps',
      );
      if (googleFitSource && googleFitSource.steps && googleFitSource.steps.length > 0) {
        steps = googleFitSource.steps[0].value;
      } else {
        // Try other sources if Google Fit source is not available
        for (const source of res) {
          if (source.steps && source.steps.length > 0) {
            steps = source.steps[0].value;
            break;
          }
        }
      }

      return {
        count: steps,
        date: format(date, 'yyyy-MM-dd'),
      };
    } catch (error) {
      console.error('Error getting step count:', error);
      return {
        count: 0,
        date: format(date, 'yyyy-MM-dd'),
      };
    }
  }

  /**
   * Get heart rate for a specific date
   */
  async getHeartRate(
    date: Date = new Date(),
  ): Promise<{value: number; unit: string; date: string}> {
    if (Platform.OS !== 'android' || !this.isInitialized) {
      await this.initialize();
    }

    try {
      const options = {
        startDate: new Date(date.setHours(0, 0, 0, 0)).toISOString(), // Start of the day
        endDate: new Date(date.setHours(23, 59, 59, 999)).toISOString(), // End of the day
        bucketUnit: 'DAY',
        bucketInterval: 1,
      };

      const heartRateData = await GoogleFit.getHeartRateSamples(options);

      // Get the most recent heart rate reading
      let heartRate = 0;
      if (heartRateData && heartRateData.length > 0) {
        // Sort by date descending to get the most recent reading
        const sortedData = heartRateData.sort(
          (a, b) => new Date(b.endDate).getTime() - new Date(a.endDate).getTime(),
        );

        heartRate = sortedData[0].value;
      }

      return {
        value: heartRate,
        unit: 'bpm',
        date: format(date, 'yyyy-MM-dd'),
      };
    } catch (error) {
      console.error('Error getting heart rate:', error);
      return {
        value: 0,
        unit: 'bpm',
        date: format(date, 'yyyy-MM-dd'),
      };
    }
  }

  /**
   * Get step count goal
   */
  async getStepCountGoal(): Promise<number> {
    // Google Fit doesn't provide a direct API to get the step goal
    // We'll use the stored value or default
    return this.stepGoal;
  }

  /**
   * Set step count goal
   */
  async setStepCountGoal(goal: number): Promise<boolean> {
    this.stepGoal = goal;
    return true;
  }
}

export default AndroidHealthService;
