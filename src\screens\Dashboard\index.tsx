import {useThemeStore, useHealthStore} from '@/store';
import React, {useEffect, useState} from 'react';
import {
  View,
  ImageBackground,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {createStyles} from './styles';
import {Images} from '@/config';
import {Header, Icon, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import ProgressCircle from '@/components/ProgressCircle';
import LottieView from 'lottie-react-native';
import PlayerStatsChart from '@/components/PlayerStatsChart';
const DashboardScreen = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  // Get health data from store
  const {
    steps,
    heartRate,
    initialize,
    fetchStepCount,
    fetchHeartRate,
    requestPermissions,
    hasPermissions,
  } = useHealthStore();
  console.log('🚀 ~ DashboardScreen ~ heartRate:', heartRate);
  console.log('🚀 ~ DashboardScreen ~ steps:', steps);

  // State for user name
  const [userName, setUserName] = useState('Alex');

  // Initialize health services and fetch data
  useEffect(() => {
    const setupHealthServices = async () => {
      try {
        // Check if we have permissions
        const hasHealthPermissions = await hasPermissions();

        if (!hasHealthPermissions) {
          // Request permissions if not granted
          const permissionsGranted = await requestPermissions();

          if (!permissionsGranted) {
            Alert.alert(
              'Health Data Access',
              'To track your steps and heart rate, please grant health data access permissions.',
              [
                {text: 'Later', style: 'cancel'},
                {
                  text: 'Grant Access',
                  onPress: async () => {
                    const granted = await requestPermissions();
                    if (granted) {
                      await initialize();
                      await fetchStepCount();
                      await fetchHeartRate();
                    }
                  },
                },
              ],
            );
            return;
          }
        }

        // Initialize health services
        await initialize();

        // Fetch health data
        await fetchStepCount();
        await fetchHeartRate();
      } catch (error) {
        console.error('Error setting up health services:', error);
      }
    };

    setupHealthServices();
  }, [fetchHeartRate, fetchStepCount, hasPermissions, initialize, requestPermissions]);

  // Function to refresh health data
  const refreshHealthData = async () => {
    try {
      await fetchStepCount();
      await fetchHeartRate();
    } catch (error) {
      console.error('Error refreshing health data:', error);
    }
  };

  return (
    <ImageBackground source={Images.gradientBg} style={styles.container}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: theme.colors.primary,
        }}
        rightIcons={[
          {name: 'notification', size: 24, badge: 0},
          {name: 'chat', size: 24, badge: 14},
        ]}
        backgroundColor="transparent"
      />
      <SafeAreaView style={styles.root}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}>
          <View style={styles.cardContainer}>
            <View style={styles.cardHeader}>
              <View style={styles.cardImageContainer}>
                <Typography style={styles.cardImageText}>Add Photo</Typography>
              </View>
              <View>
                <Typography variant="profileCardTitle" color={theme.colors.text}>
                  Hello, {userName}
                </Typography>
                <Typography variant="invitePlayersTitle" color={theme.colors.text}>
                  This is your progress!
                </Typography>
              </View>
            </View>
            <TouchableOpacity onPress={refreshHealthData}>
              <Icon name="setting" size={20} color={theme.colors.activeColor} />
            </TouchableOpacity>
          </View>

          <View style={styles.progressContainer}>
            <View style={styles.progressCard}>
              {steps.loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                </View>
              ) : (
                <ProgressCircle
                  progress={steps.count}
                  maxProgress={steps.goal}
                  title="Steps"
                  progressDesc="steps"
                />
              )}
            </View>
            <View style={styles.progressCard}>
              <Typography variant="progressHeadingText" color={theme.colors.text}>
                Heart Rate
              </Typography>
              {heartRate.loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                </View>
              ) : (
                <>
                  <LottieView
                    source={Images.soundWave}
                    autoPlay
                    loop
                    style={{width: '100%', height: 100}}
                  />
                  <Typography variant="parkTitle" color={theme.colors.text}>
                    {heartRate.value > 0 ? `${heartRate.value} ${heartRate.unit}` : 'No data'}
                  </Typography>
                </>
              )}
            </View>
          </View>

          <View style={styles.chartContainer}>
            <PlayerStatsChart
              title="Win Streak"
              monthLabelStyle={{color: theme.colors.text}}
              containerStyle={{width: '100%', backgroundColor: 'transparent'}}
              initialYear="2024"
            />
          </View>
          <View style={styles.progressContainer}>
            <View style={styles.progressCard}>
              <ProgressCircle
                title="Time to Restring"
                progressColor={theme.colors.primary}
                progress={15}
                maxProgress={30}
                progressDesc="Days"
              />
            </View>
            <View style={styles.progressCard}>
              <ProgressCircle
                title="Time to Regrip"
                progressColor={theme.colors.primary}
                progress={2}
                maxProgress={30}
                progressDesc="Days"
              />
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};
export default DashboardScreen;
