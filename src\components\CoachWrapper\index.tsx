import React from 'react';
import {ImageBackground, TouchableOpacity, View, StyleSheet} from 'react-native';
import {useThemeStore} from '@/store';
import {Icon, SafeAreaView} from '@/components';
import {Images} from '@/config';
import {useNavigation} from '@react-navigation/native';
import type {StackNavigationProp} from '@react-navigation/stack';

interface CoachWrapperProps {
  children: React.ReactNode;
  navigateTo?: string;
  includeTop?: boolean;
  includeBottom?: boolean;
  style?: any;
}

const CoachWrapper: React.FC<CoachWrapperProps> = ({
  children,
  navigateTo = '',
  includeTop = true,
  includeBottom = false,
  style,
}) => {
  const theme = useThemeStore();
  const navigation = useNavigation<StackNavigationProp<any>>();

  const handleClose = () => {
    if (navigateTo) {
      navigation.navigate(navigateTo);
    } else {
      navigation.goBack();
    }
  };

  return (
    <ImageBackground
      source={Images.gradientBg}
      style={[styles.container, style]}
      resizeMode="cover">
      <SafeAreaView includeTop={includeTop} includeBottom={includeBottom}>
        <View style={styles.content}>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Icon name="close1-1" size={36} color={theme.colors.red} />
          </TouchableOpacity>
          {children}
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#fff',
    margin: 12,
  },

  closeButton: {
    margin: 12,
    alignItems: 'flex-end',
  },
});

export default CoachWrapper;
