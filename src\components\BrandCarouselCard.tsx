import React from 'react';
import {View, Text, Image, StyleSheet, Dimensions, TouchableOpacity} from 'react-native';
import CImage from '@/components/CImage';

interface BrandCarouselCardProps {
  brandName: string;
  brandLogo: any;
  image: any;
  style?: any;
  onCardPress?: () => void;
}

const BrandCarouselCard: React.FC<BrandCarouselCardProps> = ({
  brandName,
  brandLogo,
  image,
  style,
  onCardPress,
}) => {
  return (
    <TouchableOpacity style={[styles.card, style]} onPress={onCardPress} activeOpacity={0.7}>
      {/* <View style={styles.header}>
        <Image source={brandLogo} style={styles.logo} resizeMode="contain" />
        <Text style={styles.brandName}>{brandName}</Text>
      </View> */}
      <CImage source={image} style={styles.productImage} resizeMode="cover" />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    width: '100%',
    height: '100%',
    backgroundColor: '#222',
    borderRadius: 9,
    borderBottomRightRadius: 0,
    overflow: 'hidden',
    // padding: 12,
    justifyContent: 'flex-start',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  logo: {
    width: 36,
    height: 24,
    marginRight: 8,
  },
  brandName: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  productImage: {
    width: '100%',
    height: '100%',
    // alignSelf: 'center',
  },
});

export default BrandCarouselCard;
