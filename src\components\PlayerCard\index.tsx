import React from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View, TouchableOpacity, Image} from 'react-native';
import {styles as createStyles} from './styles';
import Typography from '@/components/Typography';
import {CImage, Icon} from '@/components';
import {Images} from '@/config';

interface PlayerCardProps {
  playerData: {
    id?: string;
    name: string;
    rating: string;
    location: string;
    image: string | number; // string for URI, number for require
    color?: string;
    isPremium?: boolean;
    status?: 'available' | 'busy' | 'in-match'; // Add status property
  };
  onSelect?: () => void;
  isSelected?: boolean;
  borderColor?: string;
}

const PlayerCard: React.FC<PlayerCardProps> = ({
  playerData,
  onSelect,
  isSelected = false,
  borderColor = '',
}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  // Render either CImage for remote images or Image for local images
  const renderImage = () => {
    if (typeof playerData.image === 'string') {
      return (
        <CImage
          source={{uri: playerData.image}}
          style={styles.avatar}
          fallbackSource={Images.profile1}
        />
      );
    }
    return <Image source={playerData.image} style={styles.avatar} resizeMode="cover" />;
  };

  // Get status indicator color based on player status
  const getStatusColor = () => {
    switch (playerData.status) {
      case 'available':
        return theme.colors.lime; // Green for available
      case 'busy':
      case 'in-match':
        return theme.colors.red; // Red for busy/in-match
      default:
        return 'transparent'; // No indicator if status is not specified
    }
  };

  return (
    <View
      style={[
        styles.card,
        {borderColor: borderColor && isSelected ? borderColor : theme.colors.secondary},
      ]}>
      {/* Avatar with Status Indicator */}
      <View style={styles.avatarContainer}>
        {renderImage()}

        {/* Status Indicator */}
        {playerData.status && (
          <View style={[styles.statusIndicator, {backgroundColor: getStatusColor()}]} />
        )}
      </View>

      {/* Info */}
      <View style={styles.infoContainer}>
        <View style={styles.nameRow}>
          <Typography
            variant="playerTitle"
            color={playerData?.isPremium ? theme.colors.orange : theme.colors.text}>
            {playerData?.name}
          </Typography>
        </View>

        <View style={styles.locationRow}>
          <Icon name="location-pin" size={22} color={theme?.colors?.activeColor} />
          <Typography
            variant="caption"
            color={playerData?.isPremium ? theme.colors.orange : theme.colors.text}>
            {playerData?.location}
          </Typography>
        </View>
      </View>

      {/* Checkbox / Plus Button */}
      <TouchableOpacity
        style={[
          styles.plusButton,
          {
            backgroundColor: isSelected ? theme.colors.activeColor : theme.colors.primary,
          },
        ]}
        onPress={onSelect}>
        <Icon
          name={isSelected ? 'check' : 'plus'}
          size={isSelected ? 26 : 25}
          color={isSelected ? theme.colors.background : theme?.colors?.text}
        />
      </TouchableOpacity>
    </View>
  );
};

export default PlayerCard;
