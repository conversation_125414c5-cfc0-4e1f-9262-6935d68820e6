import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    base: {
      paddingVertical: 14,
      paddingHorizontal: 20,
      alignItems: 'center',
      justifyContent: 'center',
    },
    contentContainer: {
      position: 'relative',
      alignItems: 'center',
      justifyContent: 'center',
    },
    primary: {
      backgroundColor: theme.colors.primary,
      borderRadius: 50,
    },
    secondary: {
      backgroundColor: theme.colors.secondary,
      borderRadius: 50,
      borderWidth: 1,
      borderColor: theme.colors.secondary,
    },
    outline: {
      borderRadius: 50,
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    active: {
      backgroundColor: theme.colors.activeColor,
      borderRadius: 50,
      paddingVertical: 10,
      fontSize: 10,
    },
    dark: {
      borderColor: theme.colors.text,
      backgroundColor: theme.colors.dark,
      borderWidth: 0.5,
      borderRadius: 50,
    },
    pill: {
      backgroundColor: theme.colors.primary,
      borderRadius: 999,
    },
    square: {
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      width: 100,
      height: 40,
    },
    text: {
      color: theme.colors.text,
      fontWeight: '500',
      fontSize: theme.fontSize.large,
    },
    textSecondary: {
      color: theme.colors.primary,
    },

    disabled: {
      opacity: 0.5,
    },
    loader: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });
