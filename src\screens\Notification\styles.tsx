import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.notificationBg,
    },
    listContent: {
      paddingHorizontal: 16,
      paddingBottom: 80,
      paddingTop: 10,
    },
    rowFront: {
      marginVertical: 6,
      backgroundColor: 'transparent',
      zIndex: 1,
      paddingRight: 0,
    },
    rowFrontOpen: {
      paddingRight: 20,
    },
    cardContainer: {
      width: '100%',
      borderRadius: 10,
      overflow: 'hidden',
    },
    rowBack: {
      alignItems: 'center',
      backgroundColor: 'transparent',
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginVertical: 5,
      borderRadius: 10,
      overflow: 'hidden',
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      paddingRight: 20,
    },
    actionButton: {
      alignItems: 'center',
      bottom: 0,
      justifyContent: 'center',
      position: 'absolute',
      top: 0,
      width: 70,
      borderRadius: 8,
      margin: 4,
      // height: '80%',
    },
    moreBtn: {
      backgroundColor: theme.colors.primary,
      right: 80,
    },
    trashBtn: {
      backgroundColor: theme.colors.primary,
      right: 0,
    },
    actionText: {
      color: theme.colors.white,
      marginTop: 5,
      textAlign: 'center',
    },
    footer: {
      position: 'absolute',
      bottom: 15,
      left: 0,
      right: 0,
    },
    footerButton: {
      backgroundColor: theme.colors.oceanBlue,
      paddingVertical: 14,
      borderRadius: 0,
      alignItems: 'center',
    },
    footerButtonText: {
      color: theme.colors.white,
      fontSize: 16,
      fontWeight: '500',
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    dotsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 5,
    },
    dot: {
      width: 5,
      height: 5,
      borderRadius: 2.5,
      backgroundColor: 'white',
      margin: 2,
    },

    modalContent: {
      marginTop: 70,
    },
    modalRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    modalButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      width: '49%',
      padding: 25,
      alignItems: 'center',
      justifyContent: 'center',
    },
    modalFullButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      width: '100%',
      padding: 20,
      justifyContent: 'center',
      marginBottom: 10,
    },
    modalButtonText: {
      color: theme.colors.white,
      fontSize: 16,
      fontWeight: '500',
    },
    iconContainer: {
      marginBottom: 5,
    },
    paginationLoader: {alignItems: 'center', padding: 10},
  });
