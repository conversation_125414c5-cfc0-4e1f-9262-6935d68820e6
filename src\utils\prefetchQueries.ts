import {queryClient} from '../providers/QueryClientProvider';
import api from '../services/api';
import {userKeys} from '../hooks/queries/useUsers';
import {postKeys} from '../hooks/queries/usePosts';

/**
 * Prefetch important data when the app starts or when the user logs in
 * This can improve the user experience by loading data in the background
 */
export const prefetchInitialQueries = async () => {
  try {
    // Prefetch users
    await queryClient.prefetchQuery({
      queryKey: userKeys.lists(),
      queryFn: async () => {
        const response = await api.get('/users');
        return response.data;
      },
    });

    // Prefetch posts (first page)
    await queryClient.prefetchQuery({
      queryKey: [...postKeys.list({}), 1, 10],
      queryFn: async () => {
        const response = await api.get('/posts', {params: {page: 1, limit: 10}});
        return response.data;
      },
    });

    // Add more prefetch queries as needed

    console.log('Initial queries prefetched successfully');
  } catch (error) {
    console.error('Error prefetching initial queries:', error);
  }
};

/**
 * Prefetch a specific user's data
 * Useful when you know the user will navigate to a user detail screen
 */
export const prefetchUser = async (userId: string) => {
  try {
    await queryClient.prefetchQuery({
      queryKey: userKeys.detail(userId),
      queryFn: async () => {
        const response = await api.get(`/users/${userId}`);
        return response.data;
      },
    });
    console.log(`User ${userId} prefetched successfully`);
  } catch (error) {
    console.error(`Error prefetching user ${userId}:`, error);
  }
};

/**
 * Prefetch a specific post's data
 * Useful when you know the user will navigate to a post detail screen
 */
export const prefetchPost = async (postId: string) => {
  try {
    await queryClient.prefetchQuery({
      queryKey: postKeys.detail(postId),
      queryFn: async () => {
        const response = await api.get(`/posts/${postId}`);
        return response.data;
      },
    });
    console.log(`Post ${postId} prefetched successfully`);
  } catch (error) {
    console.error(`Error prefetching post ${postId}:`, error);
  }
};
