import React, {useState} from 'react';
import {ImageBackground, ScrollView, View} from 'react-native';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import {Images} from '@/config';
import {CButton, CImage, Header, Icon, SafeAreaView, RatingBar} from '@/components';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import Typography from '@/components/Typography';
import useTranslation from '@/hooks/useTranslation';

type NavigationProp = StackNavigationProp<CommunityStackParamList>;

const CoachProfile = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  const navigation = useNavigation<NavigationProp>();

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const [ratingValue, setRatingValue] = useState(0);

  const {t} = useTranslation();

  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage}>
      <SafeAreaView includeBottom={false} style={styles.root}>
        <Header
          leftIcon={{
            name: 'side-menu',
            size: 24,
            color: theme.colors.white,
            containerStyle: theme.colors.primary,
          }}
          leftIconButtonStyle={styles.menuButton}
          onLeftPress={openDrawer}
          rightIcons={[
            {name: 'notification', size: 24, badge: 0},
            {name: 'chat', size: 24, badge: 14},
          ]}
          pageTitle={t('coachProfileScreen.title')}
          backgroundColor="transparent"
        />
        <ScrollView contentContainerStyle={styles.scrollViewContainer}>
          <View style={styles.coachProfileContainer}>
            <CImage
              source={Images.coachProfile}
              resizeMode="contain"
              style={styles.coachProfileImage}
            />
            <Icon name="badge" size={33} color={theme.colors.inputLabel} />
          </View>

          <View style={styles.coachNameContainer}>
            <Typography variant="coachTitle" color={theme.colors.white}>
              Jed Murray
            </Typography>
            <Typography variant="coachType" color={theme.colors.white2}>
              . Tennis
            </Typography>
          </View>

          <RatingBar
            value={ratingValue}
            onChange={setRatingValue}
            iconSize={20}
            containerStyle={styles.ratingBar}
            ratingCount={5}
          />

          <View style={styles.contentContainer}>
            <Typography variant="badgeText" color={theme.colors.white}>
              Primary Courts :
            </Typography>
            <Typography variant="communitySubDetail" color={theme.colors.white}>
              Wilton YMCA, Old Gick Rd., Saratoga Springs, NY
            </Typography>
            <Typography variant="badgeText" color={theme.colors.white}>
              Will travel to:{' '}
              <Typography variant="communitySubDetail" color={theme.colors.white}>
                Spain
              </Typography>
            </Typography>
          </View>
          <View style={styles.contentContainer}>
            <Typography variant="badgeText" color={theme.colors.white}>
              About Jed Murray:
            </Typography>
            <Typography variant="communitySubDetail" color={theme.colors.white}>
              Jed has been batting the ball since age three and was a junior champion. He is since
              specialized in coaching juniors
            </Typography>
          </View>
          <View style={styles.contentContainer}>
            <Typography variant="badgeText" color={theme.colors.white}>
              Qualifications:
            </Typography>
            <View>
              <Typography variant="communitySubDetail" color={theme.colors.white}>
                USTA Certification
              </Typography>
              <Typography variant="communitySubDetail" color={theme.colors.white}>
                20 years playing
              </Typography>
              <Typography variant="communitySubDetail" color={theme.colors.white}>
                12 years coaching
              </Typography>
            </View>
          </View>
          <View style={styles.contentContainer}>
            <Typography variant="badgeText" color={theme.colors.white}>
              Jedd Murray coaches:
            </Typography>
            <Typography variant="communitySubDetail" color={theme.colors.white}>
              Kids Teens Adults Senior Beginners Intermediate Advanced Individuals Small Groups
              Large Groups
            </Typography>
          </View>
          <View style={styles.contentContainer}>
            <Typography variant="badgeText" color={theme.colors.white}>
              Coaching Specialties:
            </Typography>
            <Typography variant="communitySubDetail" color={theme.colors.white}>
              Footwork & Conditioning Singles Doubles Fun & Games Fundamentals Red, Oragne, and
              Green Ball Progression Private Lessons Group Lessons
            </Typography>
          </View>

          <View style={styles.buttonContainer}>
            <CButton
              title={t('coachProfileScreen.book')}
              variant="primary"
              onPress={() => {
                navigation.goBack();
              }}
              containerStyle={{height: 70}}
            />
            <CButton
              title={t('coachProfileScreen.askQuestion')}
              variant="secondary"
              onPress={() => {
                navigation.goBack();
              }}
              containerStyle={{height: 70}}
            />
            <CButton
              title={t('coachProfileScreen.sendVideo')}
              variant="secondary"
              onPress={() => {
                navigation.goBack();
              }}
              containerStyle={{height: 70}}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default CoachProfile;
