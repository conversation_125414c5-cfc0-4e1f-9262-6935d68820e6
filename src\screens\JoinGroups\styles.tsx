import {Dimensions, StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    root: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },

    container: {
      flex: 1,
      paddingHorizontal: 16,
    },
    contentContainer: {
      flexGrow: 1,
      padding: 16,
    },
    actionContainer: {
      backgroundColor: theme.colors.black,
      paddingHorizontal: 30,
      paddingVertical: 5,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    cardContainer: {
      marginVertical: 7,
      marginHorizontal: 16,
    },
    tabStyle: {
      width: Dimensions.get('screen').width / 3 - 10,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 4,
      borderWidth: 0,
    },
    tabTitleStyle: {
      textAlign: 'center',
    },
    listContainerStyle: {
      paddingHorizontal: 0,
    },
    tabContainer: {
      marginBottom: 5,
    },
    content: {
      flexGrow: 1,
      paddingBottom: 16,
      paddingHorizontal: 16,
    },
    privacyButton: {
      flex: 1,
      marginHorizontal: 4,
      padding: 12,
      borderRadius: 8,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
    },
    privacyButtonSelected: {
      backgroundColor: theme.colors.white,
    },

    privacyButtonText: {
      color: theme.colors.white,
      fontWeight: 'bold',
    },
    privacyButtonTextSelected: {
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    privacyRow: {
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      marginVertical: 5,
      borderRadius: 10,
      paddingVertical: 8,
      gap: 10,
    },
    headerContainer: {
      paddingHorizontal: 16,
    },
    searchInput: {
      color: theme.colors.white,
      borderRadius: 30,
      height: 47,
    },
    searchContainer: {
      paddingHorizontal: 16,
      paddingBottom: 20,
    },
    headerRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 10,
      paddingHorizontal: 16,
      marginBottom: 20,
    },
  });
