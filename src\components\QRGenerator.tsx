import React, {useCallback, useState} from 'react';
import {View, Text, StyleSheet, StyleProp, ViewStyle, ActivityIndicator} from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import {useThemeStore} from '@/store/themeStore';
import {fetchQRGenerator} from '@/utils/commonFunctions';
import {useFocusEffect} from '@react-navigation/native';
import useTranslation from '@/hooks/useTranslation';

interface QRGeneratorProps {
  size?: number;
  showText?: boolean;
  style?: StyleProp<ViewStyle>;
}

const QRGenerator: React.FC<QRGeneratorProps> = ({size = 200, showText = true, style}) => {
  const theme = useThemeStore();
  const {t} = useTranslation();
  const styles = StyleSheet.create({
    container: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'transparent',
    },
    qrContainer: {
      padding: 10,
      backgroundColor: theme.colors.white,
      borderRadius: 10,
      minHeight: size,
      minWidth: size,
      justifyContent: 'center',
      alignItems: 'center',
    },
    instructionText: {
      color: theme.colors.white,
      marginTop: 20,
      fontSize: 16,
      textAlign: 'center',
    },
  });

  const [data, setData] = useState(null);

  useFocusEffect(
    useCallback(() => {
      let isActive = true;

      const loadQR = async () => {
        const result = await fetchQRGenerator();
        if (isActive) {
          setData(result);
        }
      };

      loadQR();

      return () => {
        isActive = false;
        setData(null); // Optional: clear state on blur/unmount
      };
    }, []),
  );

  return (
    <View style={[styles.container, style]}>
      <View style={styles.qrContainer}>
        {!data ? (
          <ActivityIndicator size="large" color={theme.colors.primary} />
        ) : (
          <QRCode
            value={data}
            size={size}
            color={theme.colors.black}
            backgroundColor={theme.colors.white}
          />
        )}
      </View>
      {showText && (
        <Text style={styles.instructionText} accessibilityRole="text">
          {t('qrGenerator.description')}
        </Text>
      )}
    </View>
  );
};

export default QRGenerator;
