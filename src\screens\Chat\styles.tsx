import FONTS from '@/utils/fonts';
import {StyleSheet, Platform} from 'react-native';

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      paddingVertical: 10,
      paddingHorizontal: 16,
      gap: 16,
    },
    backButton: {
      padding: 8,
      backgroundColor: theme.colors.background,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
    },
    cameraButton: {
      padding: 8,
      borderRadius: 100,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      backgroundColor: theme.colors.white,
    },
    headerIconText: {
      color: theme.colors.white,
      fontSize: 22,
      fontWeight: 'bold',
    },
    headerTitleContainer: {
      flex: 1,
    },
    headerTitle: {
      color: theme.colors.white,
    },
    headerSubtitle: {
      color: theme.colors.dotColor,
    },
    headerCameraIcon: {
      width: 24,
      height: 24,
      tintColor: theme.colors.white,
    },
    bubbleRight: {
      backgroundColor: theme.colors.orange,
      padding: 8,
      borderTopRightRadius: 8,
      borderTopLeftRadius: 8,
      borderBottomLeftRadius: 8,
      borderBottomRightRadius: 0,
      marginBottom: 2,
    },
    bubbleLeft: {
      backgroundColor: theme.colors.primary,
      padding: 8,
      borderTopRightRadius: 8,
      borderBottomRightRadius: 8,
      borderBottomLeftRadius: 8,
      borderTopLeftRadius: 0,
      marginBottom: 2,
    },
    bubbleTextRight: {
      color: theme.colors.white,
      fontSize: 14,
      fontFamily: FONTS.regular,
    },
    bubbleTextLeft: {
      color: theme.colors.white,
      fontSize: 14,
      fontFamily: FONTS.regular,
    },
    inputToolbarContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: 16,
      marginVertical: 16,
      paddingHorizontal: 16,
    },
    inputToolbar: {
      // flex: 1,
      backgroundColor: theme.colors.white,
      padding: 4,
      borderRadius: 8,
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    inputPrimary: {
      color: theme.colors.white,
    },
    avatarContainer: {
      flexDirection: 'column',
      alignItems: 'center',
      width: 56,
      marginRight: 8,
    },
    avatarName: {
      color: theme.colors.orange,
      fontWeight: 'bold',
      fontSize: 13,
      marginBottom: 4,
      alignSelf: 'flex-start',
    },
    bottomSheetContent: {
      flex: 1,
      padding: 16,
      gap: 16,
    },
    bottomSheetButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
      padding: 16,
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
    },
    bottomSheetButtonText: {
      color: theme.colors.white,
      fontSize: 16,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      backgroundColor: theme.colors.background,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      paddingBottom: Platform.OS === 'ios' ? 40 : 20,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.darkGray,
    },
    modalTitle: {
      color: theme.colors.white,
      fontSize: 18,
    },
    modalCloseButton: {
      padding: 8,
    },
    modalButtons: {
      padding: 16,
      gap: 12,
    },
    modalButton: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
      padding: 16,
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
    },
    modalButtonText: {
      color: theme.colors.white,
      fontSize: 16,
    },
    micButton: {
      padding: 8,
      alignItems: 'center',
      justifyContent: 'center',
    },

    recordingTime: {
      color: theme.colors.white,
      fontSize: 12,
      marginTop: 4,
    },
    audioContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 8,
      minWidth: 120,
    },
    audioWaveform: {
      flex: 1,
      height: 30,
      marginHorizontal: 8,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: 4,
    },
    audioDuration: {
      color: theme.colors.white,
      fontSize: 12,
      marginLeft: 4,
    },
    audioProgressBar: {
      flex: 1,
      height: 6,
      backgroundColor: 'rgba(255,255,255,0.3)',
      borderRadius: 3,
      overflow: 'hidden',
      marginHorizontal: 8,
      minWidth: 60,
      maxWidth: 120,
      position: 'relative',
    },
    audioProgress: {
      position: 'absolute',
      left: 0,
      top: 0,
      bottom: 0,
      backgroundColor: theme.colors.primary,
      borderRadius: 3,
      height: 6,
    },
    pendingImageContainer: {
      marginBottom: 8,
      borderRadius: 8,
      overflow: 'hidden',
      position: 'relative',
    },
    pendingImage: {
      width: 100,
      height: 100,
      borderRadius: 8,
    },
    removeImageButton: {
      position: 'absolute',
      top: 4,
      right: 4,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      borderRadius: 10,
      padding: 2,
    },
    pendingImageWrapper: {
      marginBottom: 8,
      alignSelf: 'flex-start',
      position: 'relative',
    },
  });

export default createStyles;
