/* eslint-disable react/display-name */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-native/no-inline-styles */
import React, {useState, useEffect, useCallback, useMemo, useRef} from 'react';
import {TouchableOpacity, View, ScrollView, FlatList} from 'react-native';
import * as yup from 'yup';
import {FONT_SIZE} from '@/utils/fonts';
import {equipmentData, padelData, pTennisData} from '@/config/staticData';
import {Icon, CustomModal, CImage, CButton, RadioSelect, Header} from '@/components';
import {useThemeStore} from '@/store/themeStore';
import {useConfigStore} from '@/store';
import {Images} from '@/config';
import {createStyles} from './styles';
import Typography from '@/components/Typography';
import ParkCard from '@/components/ParkCard';
import Tabs from '@/components/Tabs';
import CustomDropdown from '@/components/CustomDropdown/CustomDropdown';
import EquipmentDetailModal from '@/components/EquipmentDetailModal';
import {sampleRacquetData} from '@/components/EquipmentDetailModal/sampleData';
import {DrawerActions, NavigationProp, useIsFocused, useNavigation} from '@react-navigation/native';
import useTranslation from '@/hooks/useTranslation';
// Define types for the racquet data
interface Racquet {
  id: string;
  name: string;
  brand: string;
  more?: string;
}

interface RacquetData {
  image?: any;
  [key: string]: Racquet[] | any;
}

interface Tab {
  id: string;
  iconName: string;
  name: string;
}

// Define the validation schema for multiple selections
const validationSchema = yup.object().shape({
  selections: yup
    .object()
    .test('at-least-one-selection', 'Please select at least one racquet', value =>
      Object.values(value || {}).some(selected =>
        Array.isArray(selected) ? selected.length > 0 : selected !== '',
      ),
    ),
});

// Add new RacquetSelection component
const RacquetSelection = React.memo(
  ({
    racquet,
    isSelected,
    onSelect,
    onMorePress,
  }: {
    racquet: Racquet;
    isSelected: boolean;
    onSelect: () => void;
    onMorePress: () => void;
  }) => {
    const theme = useThemeStore();
    const styles = createStyles(theme);

    return (
      <View style={styles.racquetItem}>
        <View style={{flex: 1, flexWrap: 'wrap'}}>
          <RadioSelect
            label={racquet.name}
            selected={isSelected}
            onPress={onSelect}
            multiSelect={true}
            textStyle={{color: theme.colors.text, fontSize: FONT_SIZE.lg, flexShrink: 1}}
          />
        </View>
        <View style={{marginLeft: 8}}>
          {racquet.more && (
            <TouchableOpacity style={styles.moreButton} activeOpacity={0.7} onPress={onMorePress}>
              <Typography variant="body" style={styles.moreText}>
                {racquet.more}
              </Typography>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  },
);

// Move static data outside component
const tabRacquetData: {[key: string]: RacquetData} = {
  racket: {
    'Recommended Tennis Racquets': [
      {id: '1', name: 'Dunlop CX 200', brand: 'Dunlop', more: 'more'},
      {id: '2', name: 'BOOM PRO', brand: 'BOOM', more: 'more'},
      {id: '3', name: 'Babolat Pure Aero 98 X2', brand: 'Babolat', more: 'more'},
    ],
    Dunlop: [
      {id: '4', name: 'Dunlop CX 200', brand: 'Dunlop', more: 'more'},
      {id: '5', name: 'Dunlop FX 500', brand: 'Dunlop', more: 'more'},
      {id: '6', name: 'Dunlop SX 500', brand: 'Dunlop', more: 'more'},
      {id: '7', name: 'Dunlop FX 300', brand: 'Dunlop', more: 'more'},
    ],
    Wilson: [
      {id: '8', name: 'Wilson Blade V8 98', brand: 'Wilson', more: 'more'},
      {id: '9', name: 'Wilson Clash V2 98', brand: 'Wilson', more: 'more'},
      {id: '10', name: 'Wilson Ultra V3 100', brand: 'Wilson', more: 'more'},
      {id: '11', name: 'Wilson Ultra V4 100', brand: 'Wilson', more: 'more'},
    ],
    Babolat: [
      {id: '12', name: 'Babolat Pure Drive', brand: 'Babolat', more: 'more'},
      {id: '13', name: 'Babolat Pure Aero', brand: 'Babolat', more: 'more'},
      {id: '14', name: 'Babolat Pure Aero Rafa', brand: 'Babolat', more: 'more'},
      {id: '15', name: 'Babolat Pure Strike', brand: 'Babolat', more: 'more'},
    ],
    Head: [
      {id: '16', name: 'Head Speed', brand: 'Head', more: 'more'},
      {id: '17', name: 'Head Boom', brand: 'Head', more: 'more'},
      {id: '18', name: 'Head Gravity', brand: 'Head', more: 'more'},
      {id: '19', name: 'Head Radical', brand: 'Head', more: 'more'},
    ],
    Yonex: [
      {id: '20', name: 'Yonex EZONE', brand: 'Yonex', more: 'more'},
      {id: '21', name: 'Yonex VCORE', brand: 'Yonex', more: 'more'},
      {id: '22', name: 'Yonex PERCEPT', brand: 'Yonex', more: 'more'},
      {id: '23', name: 'Yonex ASTREL', brand: 'Yonex', more: 'more'},
    ],
  },
  padel: {
    'Recommended Pickleball Paddles': [
      {id: '24', name: 'Head Speed Pro', brand: 'Head', more: 'more'},
      {id: '25', name: 'Yonex EZONE', brand: 'Yonex', more: 'more'},
      {id: '26', name: 'Boom PRO', brand: 'Yonex', more: 'more'},
    ],
    Dunlop: [
      {id: '27', name: 'Dunlop CX 200', brand: 'Dunlop', more: 'more'},
      {id: '28', name: 'Dunlop FX 500', brand: 'Dunlop', more: 'more'},
      {id: '29', name: 'Dunlop SX 500', brand: 'Dunlop', more: 'more'},
      {id: '30', name: 'Dunlop FX 300', brand: 'Dunlop', more: 'more'},
    ],
  },
  ball: {
    'Recommended Tennis Balls': [
      {id: '31', name: 'Dunlop ATP Official Tennis Balls', brand: 'Dunlop', more: 'more'},
      {id: '32', name: 'Dunlop AO Tennis Balls', brand: 'Dunlop', more: 'more'},
      {id: '33', name: 'Dunlop Stage 1 Green Tennis Balls', brand: 'Dunlop', more: 'more'},
    ],
    Dunlop: [
      {id: '34', name: 'Dunlop AO Tennis Balls', brand: 'Dunlop', more: 'more'},
      {id: '35', name: 'Dunlop ATP Official Tennis Balls', brand: 'Dunlop', more: 'more'},
      {id: '36', name: 'Dunlop Grand Prix Tennis Balls', brand: 'Dunlop', more: 'more'},
      {id: '37', name: 'Dunlop Stage 1 Green Tennis Balls', brand: 'Dunlop', more: 'more'},
    ],
  },
  bag: {
    image: Images.shoes,
    'Recommended Gear': [
      {id: '38', name: 'New Balance Coco CG1 Unity of Sport', brand: 'New Balance', more: 'more'},
      {id: '39', name: 'Collared Tournament Tank', brand: 'New Balance', more: 'more'},
      {id: '40', name: 'Tournament Skort', brand: 'Babolat', more: 'more'},
    ],
    'New Balance': [
      {id: '41', name: 'New Balance Coco CG1 Unity of Sport 1', brand: 'New Balance', more: 'more'},
      {id: '42', name: 'New Balance Coco CG1 Unity of Sport 2', brand: 'New Balance', more: 'more'},
      {id: '43', name: 'New Balance Coco CG1 Unity of Sport 3', brand: 'New Balance', more: 'more'},
    ],
  },
};

const options = [
  {label: 'Adult', value: 'adult'},
  {label: 'Junior', value: 'junior'},
];

const tabs: Tab[] = [
  {id: '1', iconName: 'racket4', name: 'racket'},
  {id: '2', iconName: 'racket3', name: 'padel'},
  {id: '3', iconName: 'ball', name: 'ball'},
  {id: '4', iconName: 'ball', name: 'bag'},
];

// Memoize HeaderComponent
const HeaderComponent = React.memo(
  ({
    bookingData,
    activeTab,
    handleTabChange,
    setIsModalVisible,
    theme,
    styles,
    racquetData,
    setTabSportSelections,
    navigation,
    tabSportSelections,
  }: {
    bookingData: any;
    activeTab: string;
    handleTabChange: (tab: string) => void;
    setIsModalVisible: (visible: boolean) => void;
    theme: any;
    styles: any;
    racquetData: RacquetData;
    setTabSportSelections: (
      callback: (prev: {[key: string]: string}) => {[key: string]: string},
    ) => void;
    navigation: any;
    tabSportSelections: {[key: string]: string};
  }) => {
    const {t} = useTranslation();

    const getSportData = (tabName: string) => {
      switch (tabName) {
        case 'padel':
          return padelData;
        case 'ball':
          return pTennisData;
        case 'bag':
          return pTennisData;
        default:
          return equipmentData;
      }
    };
    return (
      <>
        <ParkCard
          variant="equipment"
          parkData={bookingData.parkData}
          date={bookingData.dateTime?.startDate}
          time={bookingData.dateTime?.startTime}
          onEditPress={() => {
            // navigation.reset({
            //   index: 0,
            //   routes: [{name: 'SearchScreen'}],
            // });

            // need to remove below code and uncomment above code when stack navigation issue will fix.
            navigation.navigate('Drawer', {
              screen: 'MainTabs',
              params: {
                screen: 'SEARCH',
                params: {
                  screen: 'SearchScreen',
                },
              },
            });
          }}
          style={{marginTop: 20}}
        />
        <View style={styles.divider} />
        <Typography style={styles.title} variant="subTitle3">
          {t('equipmentReservationScreen.title')}
        </Typography>
        <View style={[styles.divider, {marginTop: 0}]} />
        <View style={styles.filterContainer}>
          <View style={styles.tabsContainer}>
            <Tabs
              variant="equipment"
              tabs={tabs.map(tab => tab.name)}
              activeTab={activeTab}
              onTabPress={handleTabChange}
              listContainerStyle={{
                paddingLeft: 0,
              }}
            />
          </View>
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={() => {
              setIsModalVisible(true);
            }}
            style={styles.filterButton}>
            <Icon name="filter" size={28} color={theme.colors.white} />
          </TouchableOpacity>
        </View>
        <CustomDropdown
          sportsData={getSportData(activeTab)}
          value={tabSportSelections[activeTab] || ''}
          onChangeValue={val =>
            setTabSportSelections(prev => ({
              ...prev,
              [activeTab]: val,
            }))
          }
          containerStyle={styles.dropdownContainer}
        />
      </>
    );
  },
);

const EquipmentReservationScreen = ({route}: {route: any}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp<any>>();
  const [activeTab, setActiveTab] = useState<string>(tabs[0].name);
  const {t} = useTranslation();
  const [currentRacquetData, setCurrentRacquetData] = useState<{
    racquet: Racquet;
    category: string;
    onSelect: () => void;
  } | null>(null);

  const isFocused = useIsFocused();

  //   const {bookingData} = useConfigStore();
  const bookingData = route?.params?.bookingData || {
    equipment: {},
    parkData: {},
    sportSelections: {},
  };

  // Initialize tab sport selections from bookingData if available
  const initializeTabSportSelections = useCallback(() => {
    const defaultSelections = {
      racket: '',
      padel: '',
      ball: '',
      bag: '',
    };

    // If sport selections exist in bookingData, use them
    if (bookingData.sportSelections) {
      return bookingData.sportSelections;
    }

    return defaultSelections;
  }, [bookingData.sportSelections]);

  // Track selected sport for each tab
  const [tabSportSelections, setTabSportSelections] = useState<{[key: string]: string}>(
    initializeTabSportSelections(),
  );
  const {setCartData, getCartData} = useConfigStore();

  const cartData = getCartData();

  const [isCartModalVisible, setIsCartModalVisible] = useState<boolean>(false);
  const cartTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [lastSelectedItem, setLastSelectedItem] = useState<Racquet | null>(null);

  // Initialize tab selections with data from MMKV if available
  const initializeTabSelections = useCallback(() => {
    // Default empty selections - now using arrays for multiple selections
    const defaultSelections = {
      racket: {},
      padel: {},
      ball: {},
      bag: {},
    };

    // If equipment data exists in bookingData, convert it to array format if needed
    if (bookingData.equipment) {
      const convertedEquipment: {[key: string]: {[key: string]: string[]}} = {};

      Object.keys(bookingData.equipment).forEach(tabKey => {
        convertedEquipment[tabKey] = {};
        Object.keys(bookingData.equipment[tabKey]).forEach(brandKey => {
          const value = bookingData.equipment[tabKey][brandKey];
          // Convert string to array if it's not already an array
          if (Array.isArray(value)) {
            convertedEquipment[tabKey][brandKey] = value;
          } else if (value && value !== '') {
            convertedEquipment[tabKey][brandKey] = [value];
          } else {
            convertedEquipment[tabKey][brandKey] = [];
          }
        });
      });

      return convertedEquipment;
    }

    return defaultSelections;
  }, [bookingData.equipment]);

  // Track selections for each tab - now supports multiple selections per brand
  const [tabSelections, setTabSelections] = useState<{
    [key: string]: {[key: string]: string[]};
  }>(initializeTabSelections());

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [detailModal, setDetailModal] = useState(false);

  const [racquetData, setRacquetData] = useState<RacquetData>(tabRacquetData[activeTab]);
  // Filter state with temporary copy for modal
  const [filterValue, setFilterValue] = useState({
    availableOnKiosk: false,
    age: '',
    // Add a temporary copy that we'll use while the modal is open
    temp: {
      availableOnKiosk: false,
      age: '',
    },
  });

  // Clear timer when component unmounts
  useEffect(() => {
    return () => {
      if (cartTimerRef.current) {
        clearTimeout(cartTimerRef.current);
      }
    };
  }, [isFocused]);

  // Update racquet data when tab changes
  useEffect(() => {
    setRacquetData(tabRacquetData[activeTab]);
  }, [activeTab]);

  // Initialize temporary filter values when modal opens
  useEffect(() => {
    if (isModalVisible) {
      // Copy the main filter values to the temp property
      setFilterValue(prev => ({
        ...prev,
        temp: {
          availableOnKiosk: prev.availableOnKiosk,
          age: prev.age,
        },
      }));
    }
  }, [isModalVisible]);

  // Save tab sport selections to bookingData when they change
  useEffect(() => {
    if (Object.values(tabSportSelections).some(val => val !== '')) {
      // Here you would typically save to your store or API
      console.log('Saving sport selections:', tabSportSelections);
      // Example: updateBookingData({ ...bookingData, sportSelections: tabSportSelections });
    }
  }, [tabSportSelections]);

  // Set active tab based on existing equipment selection if available
  useEffect(() => {
    if (bookingData.equipment) {
      // Find the first tab that has selections
      for (const tab of tabs) {
        if (
          bookingData.equipment[tab.name] &&
          Object.values(bookingData.equipment[tab.name]).some(val =>
            Array.isArray(val) ? val.length > 0 : val !== '',
          )
        ) {
          setActiveTab(tab.name);
          break;
        }
      }
    }
  }, [bookingData.equipment]);

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  // Function to add item to cart and start timer
  const addToCart = useCallback(
    (racquet: Racquet, category: string, isSelected: boolean, showCartModal: boolean = true) => {
      // Get existing cart data
      const existingCartData = getCartData() || [];

      if (!isSelected) {
        // Save the last selected item
        setLastSelectedItem(racquet);

        // Convert the selected racquet to cart data format
        const cartItem = {
          id: racquet.id,
          name: racquet.name,
          brand: racquet.brand,
        };

        // Check if item already exists in cart to avoid duplicates
        const itemExists = existingCartData.some(item => item.id === cartItem.id);

        if (!itemExists) {
          // Add to cart (append don't replace)
          setCartData([...existingCartData, cartItem]);
        }

        // Only set the cart timer if showCartModal is true
        if (showCartModal) {
          // Reset timer
          if (cartTimerRef.current) {
            clearTimeout(cartTimerRef.current);
          }

          // Set a new timer to show cart modal after 5 seconds
          cartTimerRef.current = setTimeout(() => {
            // Only show modal if items are in cart
            const currentCartData = getCartData() || [];
            if (currentCartData.length > 0) {
              setIsCartModalVisible(true);
            }
          }, 5000);
        }
      } else {
        // If user is deselecting, remove the item from cart
        const updatedCart = existingCartData.filter(item => item.id !== racquet.id);
        setCartData(updatedCart);

        // Clear the timer
        if (cartTimerRef.current) {
          clearTimeout(cartTimerRef.current);
        }
      }
    },
    [setCartData, getCartData],
  );

  const handleRacquetSelection = useCallback(
    (
      racquet: Racquet,
      category: string,
      showCartModal: boolean = true,
      forceSelect: boolean = false,
    ) => {
      setTabSelections(prev => {
        // Get current selections for this category, or empty array if none
        const currentSelections = prev[activeTab][category] || [];

        // Check if this racquet is already selected
        const isAlreadySelected = currentSelections.includes(racquet.name);

        // If forceSelect is true and item is already selected, keep it selected
        if (forceSelect && isAlreadySelected) {
          return prev; // Return previous state without changes
        }

        let newSelections: string[];
        if (isAlreadySelected) {
          // Remove from selections
          newSelections = currentSelections.filter(name => name !== racquet.name);
        } else {
          // Add to selections
          newSelections = [...currentSelections, racquet.name];
        }

        const updatedTabSelections = {
          ...prev[activeTab],
          [category]: newSelections,
        };

        validationSchema
          .validate({selections: updatedTabSelections})
          .then(() => {
            console.log('Valid selections:', updatedTabSelections);
          })
          .catch(error => {
            console.error('Validation error:', error.message);
          });

        // Only call addToCart if we're not forcing selection of an already selected item
        if (!(forceSelect && isAlreadySelected)) {
          addToCart(racquet, category, isAlreadySelected, showCartModal);
        }

        return {
          ...prev,
          [activeTab]: updatedTabSelections,
        };
      });
    },
    [activeTab, addToCart],
  );

  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab);
  }, []);

  const handleNext = useCallback(
    (toFirstTab: boolean = false) => {
      const currentIndex = tabs.findIndex(tab => tab.name === activeTab);
      if (currentIndex < tabs.length - 1) {
        setActiveTab(tabs[currentIndex + 1].name);
      }
      if (currentIndex === 3 && toFirstTab) {
        setActiveTab(tabs[0].name);
      }
      if (currentIndex === 3 && !toFirstTab) {
        navigation.navigate('CartScreen');
      }
    },
    [activeTab, tabSelections],
  );

  const renderRacquetSection = useCallback(
    ({item, index}: {item: {brand: string; racquets: Racquet[]}; index: number}) => (
      <View style={styles.sectionContainer}>
        {racquetData.image && index === 0 && (
          <CImage source={racquetData.image} style={styles.categoryImage} resizeMode="cover" />
        )}
        <Typography
          variant={index === 0 ? 'subTitle3' : 'subtitle'}
          style={index === 0 ? styles.mainBrandTitle : styles.brandTitle}>
          {item.brand}
        </Typography>
        {item.racquets.map((racquet: Racquet) => (
          <RacquetSelection
            key={racquet.id}
            racquet={racquet}
            isSelected={(tabSelections[activeTab][item.brand] || []).includes(racquet.name)}
            onSelect={() => handleRacquetSelection(racquet, item.brand)}
            onMorePress={() => {
              setCurrentRacquetData({
                racquet,
                category: item.brand,
                onSelect: () => handleRacquetSelection(racquet, item.brand),
              });
              setDetailModal(true);
            }}
          />
        ))}
      </View>
    ),
    [activeTab, handleRacquetSelection, styles, tabSelections, racquetData.image],
  );

  const formattedData = useMemo(
    () =>
      Object.entries(racquetData)
        .filter(([key]) => key !== 'image')
        .map(([brand, racquets]) => ({
          brand,
          racquets,
        })),
    [racquetData],
  );

  const handleAddToCart = (cartItem: any) => {
    const existingCartData = getCartData() || [];
    const itemExists = existingCartData.some(item => item.id === cartItem.id);

    if (!itemExists) {
      // Add to cart (append don't replace)
      setCartData([...existingCartData, cartItem]);
    }
    navigation.navigate('CartScreen');
  };

  return (
    <View style={styles.root}>
      <Header
        backgroundImage={true}
        parkData={bookingData.parkData}
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: theme.colors.primary,
        }}
        leftIconButtonStyle={styles.menuButton}
        onLeftPress={openDrawer}
        backgroundColor="transparent"
        mapBackButton
        rightIcons={[
          {
            name: 'cart',
            size: 32,
            color: theme.colors.activeColor,
            badge: cartData?.length || 0,
          },
        ]}
        onSkip={() => {
          navigation.navigate('CartScreen');
        }}
      />
      <View style={styles.contentContainer}>
        <HeaderComponent
          bookingData={bookingData}
          activeTab={activeTab}
          handleTabChange={handleTabChange}
          setIsModalVisible={setIsModalVisible}
          theme={theme}
          styles={styles}
          racquetData={racquetData}
          setTabSportSelections={setTabSportSelections}
          tabSportSelections={tabSportSelections}
          navigation={navigation}
        />
        <FlatList
          data={formattedData}
          renderItem={renderRacquetSection}
          keyExtractor={item => item.brand}
          showsVerticalScrollIndicator={false}
          bounces={false}
          contentContainerStyle={styles.flexGrow}
        />
      </View>

      <CustomModal
        visible={isModalVisible}
        onClose={() => {
          // Just close the modal without applying changes
          setIsModalVisible(false);
        }}
        variant="bottom"
        showClearButtonRight={'Clear'}
        onClear={() => {
          // Clear all filters
          setFilterValue({
            availableOnKiosk: false,
            age: '',
            temp: {
              availableOnKiosk: false,
              age: '',
            },
          });
          setTabSportSelections(initializeTabSportSelections());
          setIsModalVisible(false);
        }}
        showCloseButton={true}
        title="Filter results">
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.filter}>
            <View style={styles.content}>
              <TouchableOpacity
                style={[
                  styles.filterItem,
                  filterValue.temp.availableOnKiosk
                    ? {
                        backgroundColor: theme.colors.activeColor,
                        borderRadius: 10,
                        padding: 8,
                      }
                    : {},
                ]}
                onPress={() => {
                  setFilterValue(prev => ({
                    ...prev,
                    temp: {
                      ...prev.temp,
                      availableOnKiosk: !prev.temp.availableOnKiosk,
                    },
                  }));
                }}>
                <Icon
                  name="Book-1"
                  size={35}
                  color={
                    filterValue.temp.availableOnKiosk ? theme.colors.black : theme.colors.primary
                  }
                />
                <Typography
                  variant="subtitle"
                  style={{
                    ...styles.text,
                    color: filterValue.temp.availableOnKiosk
                      ? theme.colors.black
                      : theme.colors.text,
                  }}>
                  {t('equipmentReservationScreen.availableNowInKiosk')}
                </Typography>
              </TouchableOpacity>
              <Typography variant="subtitle" style={{color: theme.colors.offWhite}}>
                {t('equipmentReservationScreen.age')}
              </Typography>
              {options.map(option => (
                <RadioSelect
                  key={option.value}
                  label={option.label}
                  selected={filterValue.temp.age === option.value}
                  onPress={() =>
                    setFilterValue(prev => ({
                      ...prev,
                      temp: {
                        ...prev.temp,
                        age: option.value,
                      },
                    }))
                  }
                />
              ))}
            </View>
            <CButton
              title="Apply"
              onPress={() => {
                // Apply the temporary filter values to the main filter state
                setFilterValue(prev => ({
                  availableOnKiosk: prev.temp.availableOnKiosk,
                  age: prev.temp.age,
                  temp: prev.temp,
                }));
                setIsModalVisible(false);
              }}
              variant="primary"
              containerStyle={styles.btn}
            />
          </View>
        </ScrollView>
      </CustomModal>

      <EquipmentDetailModal
        equipment={sampleRacquetData}
        visible={detailModal}
        onClose={() => {
          setDetailModal(false);
          setCurrentRacquetData(null);
        }}
        onAdd={() => {
          if (currentRacquetData?.onSelect) {
            // Pass false for showCartModal and true for forceSelect
            handleRacquetSelection(
              currentRacquetData.racquet,
              currentRacquetData.category,
              false,
              true,
            );
          }
          handleAddToCart(sampleRacquetData);
          setDetailModal(false);
          setCurrentRacquetData(null);
        }}
      />

      <CustomModal
        visible={isCartModalVisible}
        onClose={() => setIsCartModalVisible(false)}
        showCloseButton={true}
        modalContainerStyle={styles.cartModalContainer}>
        <View style={styles.cartModalContent}>
          <Icon name="arrow" size={150} color={theme.colors.activeColor} />
          <Typography variant="subtitle" style={styles.cartModalTitle}>
            Purchase balls or equipment
          </Typography>
          <CButton
            title="Yes!"
            onPress={() => {
              setIsCartModalVisible(false);
              handleNext(true);
            }}
            variant="pill"
            containerStyle={styles.cartModalButton}
            textStyle={styles.cartModalButtonText}
          />
          <CButton
            title="Take me to my cart"
            onPress={() => {
              setIsCartModalVisible(false);
              navigation.navigate('CartScreen');
            }}
            variant="outline"
            containerStyle={styles.cartModalButtonOutline}
          />
        </View>
      </CustomModal>
    </View>
  );
};

export default React.memo(EquipmentReservationScreen);
