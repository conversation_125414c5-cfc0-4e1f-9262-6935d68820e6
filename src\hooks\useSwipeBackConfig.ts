import {useRoute} from '@react-navigation/native';

// Define which screens should have swipe-to-back functionality
const SWIPE_BACK_SCREENS = [
  'MyGroups',
  'NewGroup', 
  'JoinGroups',
  'JoinGroupDetails',
  'AddMembers',
  'CreateGroupMemberList',
  'Chat',
  'CommunityDetails',
  'CommentScreen',
  'CoachProfile',
  'FindCoach',
  'FindClass',
  'PlayerConnectDateScreen',
];

// Define the navigation flow for community screens
const COMMUNITY_NAVIGATION_FLOW = {
  'MyGroups': 'CommunityHome', // MyGroups should go back to Community Groups tab
  'NewGroup': 'CommunityHome',
  'JoinGroups': 'CommunityHome',
  'JoinGroupDetails': 'JoinGroups',
  'AddMembers': 'NewGroup',
  'CreateGroupMemberList': 'AddMembers',
  'Chat': 'MyGroups',
  'CommunityDetails': 'CommunityHome',
  'CommentScreen': 'CommunityDetails',
  'CoachProfile': 'CommunityHome',
  'FindCoach': 'CommunityHome',
  'FindClass': 'CommunityHome',
  'PlayerConnectDateScreen': 'CommunityHome',
};

export const useSwipeBackConfig = () => {
  const route = useRoute();
  const currentScreenName = route.name;

  const shouldEnableSwipeBack = SWIPE_BACK_SCREENS.includes(currentScreenName);
  const backDestination = COMMUNITY_NAVIGATION_FLOW[currentScreenName as keyof typeof COMMUNITY_NAVIGATION_FLOW];

  return {
    shouldEnableSwipeBack,
    backDestination,
    currentScreenName,
  };
};

export default useSwipeBackConfig;
