import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    root: {
      backgroundColor: theme.colors.TranslucentWhite,
      borderRadius: 12,
      paddingHorizontal: 20,
      borderColor: theme.colors.white,
      paddingVertical: 10,
      flexDirection: 'row',
      gap: 10,
    },
    contentContainer: {
      flex: 1,
    },
    title: {
      color: theme.colors.text,
      marginBottom: 6,
    },
    description: {
      color: theme.colors.white,
      justifyContent: 'center',
      lineHeight: 16,
    },
    descriptionContainer: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    descriptionContainerexpand: {
      gap: 10,
    },
    moreText: {
      color: theme.colors.inputLabel,
    },
    actionsBar: {
      flexDirection: 'row',
      gap: 16,
      alignItems: 'center',
    },
    actionsContainer: {
      flex: 1,
      flexDirection: 'row',
      gap: 10,
      marginTop: 15,
    },
    tagsContainer: {
      flex: 1,
      flexDirection: 'row',
      gap: 7,
      justifyContent: 'flex-start',
      alignContent: 'center',
      marginTop: 16,
    },
    tag: {
      paddingHorizontal: 20,
      borderRadius: 5,
    },
    thumbnailContainer: {
      height: 100,
      width: 100,
      borderRadius: 10,
      overflow: 'hidden',
    },
    thumbnail: {
      height: '100%',
      width: '100%',
    },
    moreIconContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: 'auto',
    },
  });
