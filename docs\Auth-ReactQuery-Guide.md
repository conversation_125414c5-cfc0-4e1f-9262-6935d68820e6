# Authentication with React Query

This guide explains how to use React Query for authentication in the GoRaqt app.

## Overview

The authentication system in this app uses:

1. **React Query**: For handling API requests and caching
2. **Zustand**: For global state management
3. **MMKV**: For secure token storage

## Authentication Flow

1. **Login/Registration**: User submits credentials
2. **Token Storage**: Access and refresh tokens are stored securely in MMKV
3. **Automatic Token Refresh**: Tokens are refreshed automatically when they expire
4. **Global State**: Authentication state is managed in Zustand store

## API Services

Authentication API services are defined in `src/services/authApi.ts`:

```typescript
// Login with email and password
export const loginWithEmailApi = async (data: LoginRequest): Promise<AuthResponse>;

// Register with email, password, and name
export const registerWithEmailApi = async (data: RegisterRequest): Promise<AuthResponse>;

// Logout - clear tokens
export const logoutApi = async (): Promise<void>;

// Refresh token
export const refreshTokenApi = async (refreshToken: string): Promise<AuthResponse>;

// Check if user is authenticated
export const checkAuthStatusApi = async (): Promise<boolean>;

// Get current user profile
export const getCurrentUserApi = async (): Promise<AuthResponse['user']>;
```

## React Query Hooks

Authentication hooks are defined in `src/hooks/queries/useAuth.ts`:

### Login Hook

```typescript
const LoginScreen = () => {
  const loginMutation = useLogin();
  
  const handleLogin = (credentials) => {
    loginMutation.mutate(credentials, {
      onSuccess: () => {
        // Navigate to main screen
        navigation.navigate('MainTabs');
      },
      onError: (error) => {
        Alert.alert('Login Failed', error.message);
      }
    });
  };
  
  return (
    <Form onSubmit={handleLogin}>
      {/* Form fields */}
      <Button 
        title="Login" 
        onPress={handleSubmit(handleLogin)} 
        loading={loginMutation.isPending}
      />
    </Form>
  );
};
```

### Registration Hook

```typescript
const RegisterScreen = () => {
  const registerMutation = useRegister();
  
  const handleRegister = (userData) => {
    registerMutation.mutate(userData, {
      onSuccess: () => {
        // Navigate to main screen
        navigation.navigate('MainTabs');
      },
      onError: (error) => {
        Alert.alert('Registration Failed', error.message);
      }
    });
  };
  
  return (
    <Form onSubmit={handleRegister}>
      {/* Form fields */}
      <Button 
        title="Register" 
        onPress={handleSubmit(handleRegister)} 
        loading={registerMutation.isPending}
      />
    </Form>
  );
};
```

### Logout Hook

```typescript
const ProfileScreen = () => {
  const logoutMutation = useLogout();
  const navigation = useNavigation();
  
  const handleLogout = () => {
    logoutMutation.mutate(undefined, {
      onSuccess: () => {
        navigation.navigate('Login');
      }
    });
  };
  
  return (
    <View>
      <Button 
        title="Logout" 
        onPress={handleLogout} 
        loading={logoutMutation.isPending}
      />
    </View>
  );
};
```

### Check Authentication Status

```typescript
const AppNavigator = () => {
  const { data: isAuthenticated, isLoading } = useAuthStatus();
  
  if (isLoading) {
    return <LoadingScreen />;
  }
  
  return (
    <NavigationContainer>
      <Stack.Navigator>
        {isAuthenticated ? (
          <Stack.Screen name="MainTabs" component={MainTabsNavigator} />
        ) : (
          <Stack.Screen name="Auth" component={AuthNavigator} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};
```

### Get Current User

```typescript
const ProfileScreen = () => {
  const { data: user, isLoading } = useCurrentUser();
  
  if (isLoading) {
    return <LoadingIndicator />;
  }
  
  return (
    <View>
      <Text>Welcome, {user?.name}</Text>
      <Text>Email: {user?.email}</Text>
    </View>
  );
};
```

## Integration with Zustand

React Query works alongside Zustand for managing authentication state:

1. **React Query**: Handles API requests, caching, and error handling
2. **Zustand**: Stores authentication state (isAuthenticated, user, etc.)

When a user logs in:
1. React Query sends the login request
2. On success, it updates the Zustand store
3. The UI reacts to the Zustand store changes

## Token Management

Tokens are stored securely using MMKV:

```typescript
// Store tokens
if (response.data.accessToken) {
  tokenStorage.set('accessToken', response.data.accessToken);
}

if (response.data.refreshToken) {
  tokenStorage.set('refreshToken', response.data.refreshToken);
}
```

## Automatic Token Refresh

The API service automatically refreshes tokens when they expire:

```typescript
// Response interceptor
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle 401 Unauthorized errors (token expired)
    if (error.response?.status === 401 && !originalRequest._retry) {
      // Refresh token logic
      // ...
    }
    return Promise.reject(error);
  }
);
```

## Best Practices

1. **Error Handling**: Always handle errors in mutations
2. **Loading States**: Show loading indicators during API requests
3. **Token Security**: Store tokens securely using MMKV
4. **Automatic Refresh**: Implement automatic token refresh
5. **Logout**: Clear tokens and invalidate queries on logout
