import React, {useRef, useState, useEffect} from 'react';
import {View, TouchableOpacity, Image} from 'react-native';
import { useThemeStore } from '@/store/themeStore';;
import {createStyles} from './styles';
import {FlatList} from 'react-native-gesture-handler';
import VideoPlayer, {VideoPlayerRef} from 'react-native-video-player';
import {Icon} from '@/components';
import VideoActionsModal from '@/components/VideoActionsModal';
import { clearImageCache } from '@/components/CImage';
import { handleTextureError } from '@/utils/memoryManager';

interface VideoData {
  image: string;
  id: string;
}

interface VisibleState {
  open: boolean;
  data: VideoData;
}

const GoStream: React.FC = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  // Use a map of refs instead of a single ref
  const playerRefs = useRef<Map<string, VideoPlayerRef>>(new Map());
  // Track which videos are currently visible
  const [visibleVideos, setVisibleVideos] = useState<Set<string>>(new Set());
  // Track which video is currently active/playing
  const [activeVideoId, setActiveVideoId] = useState<string | null>(null);

  const data: VideoData[] = [
    {image: 'https://picsum.photos/202/300', id: '1'},
    {image: 'https://picsum.photos/202/302', id: '2'},
    {image: 'https://picsum.photos/202/303', id: '3'},
    {image: 'https://picsum.photos/202/306', id: '4'},
  ];

  const [visible, setVisible] = useState<VisibleState>({open: false, data: {image: '', id: ''}});

  // Clean up resources when component unmounts
  useEffect(() => {
    return () => {
      // Clear image cache when leaving the screen
      clearImageCache();
      // Clear player refs
      playerRefs.current.clear();
    };
  }, []);

  // Handle video visibility changes
  const handleViewableItemsChanged = ({viewableItems}: {viewableItems: any[]}) => {
    const newVisibleVideos = new Set<string>();

    viewableItems.forEach(item => {
      if (item.item && item.item.id) {
        newVisibleVideos.add(item.item.id);
      }
    });

    setVisibleVideos(newVisibleVideos);
  };

  // Handle video play/pause
  const handleVideoPress = (videoId: string) => {
    try {
      // If there's an active video and it's different from the one being pressed
      if (activeVideoId && activeVideoId !== videoId) {
        // Pause the currently active video
        const activePlayer = playerRefs.current.get(activeVideoId);
        if (activePlayer) {
          activePlayer.pause();
        }
      }

      // Set the new active video
      setActiveVideoId(videoId);
    } catch (error) {
      console.error('Error handling video press:', error);
      // Handle texture errors
      handleTextureError();
    }
  };

  // Render a video item with optimizations
  const renderVideoItem = ({item}: {item: VideoData}) => {
    const isVisible = visibleVideos.has(item.id);

    // If video is not visible, just render a placeholder with thumbnail
    if (!isVisible) {
      return (
        <View style={styles.videoContainer}>
          <Image
            source={{uri: item.image}}
            style={styles.thumbnail}
            resizeMode="cover"
          />
          <View style={styles.cardContent}>
            <TouchableOpacity onPress={() => setVisible({open: true, data: item})}>
              <Icon name="threeDot" size={20} color={theme.colors.white} />
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // For visible videos, render the full VideoPlayer
    return (
      <View style={styles.videoContainer}>
        <VideoPlayer
          ref={(ref) => {
            if (ref) {
              playerRefs.current.set(item.id, ref);
            } else {
              playerRefs.current.delete(item.id);
            }
          }}
          endWithThumbnail
          thumbnail={{
            uri: item.image,
          }}
          customStyles={{
            playButton: styles.playButton,
            playArrow: styles.playArrow,
            video: styles.video,
            thumbnail: styles.thumbnail,
            wrapper: styles.wrapper,
          }}
          source={{uri: item.image}}
          onError={(e) => {
            console.log('Video error:', e.error);
            handleTextureError();
          }}
          showDuration={true}
          onPlay={() => handleVideoPress(item.id)}
        />
        <View style={styles.cardContent}>
          <TouchableOpacity onPress={() => setVisible({open: true, data: item})}>
            <Icon name="threeDot" size={20} color={theme.colors.white} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={data}
        contentContainerStyle={styles.content}
        renderItem={renderVideoItem}
        keyExtractor={(item) => item.id}
        // Add viewability configuration
        onViewableItemsChanged={handleViewableItemsChanged}
        viewabilityConfig={{
          itemVisiblePercentThreshold: 50,
          minimumViewTime: 300,
        }}
        // Add window size to limit rendering
        windowSize={3}
        // Add maximum number of rendered items
        maxToRenderPerBatch={2}
        // Add initial number to render
        initialNumToRender={2}
        // Add remove clipped subviews
        removeClippedSubviews={true}
      />
      <VideoActionsModal
        visible={visible?.open}
        onClose={() => setVisible({...visible, open: false})}
        videoTitle="Video Actions"
        videoThumbnail={visible?.data?.image}
        onLike={() => {}}
        onComment={() => {}}
        onShare={() => {}}
        onReport={() => {}}
      />
    </View>
  );
};

export default GoStream;
