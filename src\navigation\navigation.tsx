import 'react-native-gesture-handler';
/* eslint-disable react/display-name */
import {NavigationContainer, NavigationContainerRef} from '@react-navigation/native';
import {createStackNavigator, StackScreenProps} from '@react-navigation/stack'; // ✅ Switched to stack
import React, {forwardRef} from 'react';
import {useConfigStore} from '@/store/configStore';
import TabNavigator from './TabNavigator';
import {useQuery} from '@tanstack/react-query';
import api from '@/services/api';

import MyGroups from '@/screens/MyGroups';
import NewGroup from '@/components/Community/NewGroup';
import JoinGroups from '@/screens/JoinGroups';
import JoinGroupDetails from '@/screens/JoinGroupDetails';
import AddMembers from '@/components/Community/AddMembers';
import CreateGroupMemberList from '@/screens/CreateGroupMemberList';
import ChatScreen from '@/screens/Chat';
import CommunityDetails from '@/screens/CommunityDetails';
import CommentScreen from '@/screens/CommentScreen';
import CommunityStack from './CommunityStack';

export type RootStackParamList = {
  Splash: undefined;
  Permissions: undefined;
  TermsOfService: undefined;
  Login: {email?: string};
  Signup: undefined;
  EmailLogin: undefined;
  Verification: {email: string; password?: string; type?: 'forgotPassword'};
  EmailSignup: undefined;
  ForgotPassword: {email?: string};
  MainTabs: undefined;
  Drawer: {
    screen?: string;
    params?: {
      screen?: string;
      params?: {
        screen?: string;
        params?: any;
      };
    };
  };
  MyGroups: undefined;
  NewGroup: {groupName?: string};
  JoinGroups: undefined;
  JoinGroupDetails: undefined;
  AddMembers: {groupName: string};
  CreateGroupMemberList: {
    groupImage?: string;
    groupName?: string;
    members: Array<{id: string; name: string; image: string}>;
  };
  Chat: undefined;
  CommunityDetails: {
    from?: string;
    title?: string;
    data?: any;
  };
  CommentScreen: {
    from?: string;
    title?: string;
    data?: any;
  };
  CoachProfile: undefined;
  FindCoach: undefined;
  FindClass: undefined;
  PlayerConnectDateScreen: undefined;
  Biometrics: undefined;
  Settings: undefined;
  Notifications: undefined;
  ReferFriend: undefined;
  MyMatches: undefined;
  NotificationsList: undefined;
  CartScreen: undefined;
  GoFit: undefined;
  Examples: undefined;
  TermsAndConditions: undefined;
};

export type RootStackScreenProps<T extends keyof RootStackParamList> = StackScreenProps<
  RootStackParamList,
  T
>;

interface DrawerMenuItem {
  id: string;
  label: string;
  title: string;
  screen?: string;
  isVisible: boolean;
}

interface ApiResponse {
  status: boolean;
  data: {
    language: string;
    json_data: DrawerMenuItem[];
  };
}

interface CommunityFeaturesResponse {
  status: boolean;
  data: {
    language: string;
    json_data: DrawerMenuItem[];
  };
}

// ✅ Updated to use createStackNavigator instead of native stack
const Stack = createStackNavigator<RootStackParamList>();

const Navigator = forwardRef<NavigationContainerRef<RootStackParamList>>((props, ref) => {
  useQuery<ApiResponse>({
    queryKey: ['app-config'],
    queryFn: async () => {
      try {
        const response = await api.get('/app-configuration/app_features');
        if (response.data.status) {
          useConfigStore.getState().setDrawerMenuItems(response.data.data.json_data);
          return response.data;
        }
      } catch (error) {
        console.error('Error fetching app config:', error);
        return null;
      }
    },
  });

  useQuery<CommunityFeaturesResponse>({
    queryKey: ['community-features'],
    queryFn: async () => {
      try {
        const response = await api.get('/app-configuration/community_features');
        if (response.data.status) {
          useConfigStore.getState().setCommunityFeatures(response.data.data.json_data);
          return response.data;
        }
      } catch (error) {
        console.error('Error fetching community features:', error);
        return null;
      }
    },
  });

  return (
    <NavigationContainer ref={ref}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: true,
          gestureDirection: 'horizontal', // 👈 Required for swipe-back on Android
        }}>
        <>
          <Stack.Screen name="Drawer" component={CommunityStack} />

          <Stack.Screen name="MyGroups" component={MyGroups} />
          <Stack.Screen name="NewGroup" component={NewGroup} />
          <Stack.Screen name="JoinGroups" component={JoinGroups} />
          <Stack.Screen name="JoinGroupDetails" component={JoinGroupDetails} />
          <Stack.Screen name="AddMembers" component={AddMembers} />
          <Stack.Screen name="CreateGroupMemberList" component={CreateGroupMemberList} />
          <Stack.Screen name="Chat" component={ChatScreen} />
          <Stack.Screen name="CommunityDetails" component={CommunityDetails} />
          <Stack.Screen name="CommentScreen" component={CommentScreen} />
        </>
      </Stack.Navigator>
    </NavigationContainer>
  );
});

export {Navigator};
