import React, {useState} from 'react';
import {View, StyleSheet, Text, TouchableOpacity, Modal} from 'react-native';
import UsersList from '@/components/UsersList';
import {useCreateUser, useUpdateUser, useUser} from '@/hooks/queries/useUsers';
import {useForm, Controller} from 'react-hook-form';
import {CInput, CButton} from '@/components';
import {useAuthStore} from '@/store';

interface User {
  id: string;
  name: string;
  email: string;
}

interface UserFormData {
  name: string;
  email: string;
  password?: string;
}

const UsersScreen: React.FC = () => {
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);

  // Fetch selected user details when editing
  const {data: selectedUser, isLoading: isLoadingUser} = useUser(selectedUserId || '');

  // Mutations for creating and updating users
  const createUserMutation = useCreateUser();
  const updateUserMutation = useUpdateUser(selectedUserId || '');

  // Form for creating/editing users
  const {
    control,
    handleSubmit,
    reset,
    formState: {errors},
  } = useForm<UserFormData>();

  const {isApiStatus} = useAuthStore();

  // Handle user selection for editing
  const handleUserPress = (user: User) => {
    setSelectedUserId(user.id);
    setIsEditModalVisible(true);

    // Pre-fill the form with user data
    reset({
      name: user.name,
      email: user.email,
    });
  };

  // Handle creating a new user
  const handleCreateUser = (data: UserFormData) => {
    if (isApiStatus) {
      createUserMutation.mutate(
        {
          name: data.name,
          email: data.email,
          password: data.password || 'defaultPassword123', // In a real app, you'd handle this differently
        },
        {
          onSuccess: () => {
            setIsCreateModalVisible(false);
            reset();
          },
        },
      );
    } else {
      setIsCreateModalVisible(false);
      reset();
    }
  };

  // Handle updating a user
  const handleUpdateUser = (data: UserFormData) => {
    if (!selectedUserId) return;
    if (isApiStatus) {
      updateUserMutation.mutate(
        {
          name: data.name,
          email: data.email,
        },
        {
          onSuccess: () => {
            setIsEditModalVisible(false);
            setSelectedUserId(null);
            reset();
          },
        },
      );
    } else {
      setIsEditModalVisible(false);
      setSelectedUserId(null);
      reset();
    }
  };

  // Open create user modal
  const openCreateModal = () => {
    reset({
      name: '',
      email: '',
      password: '',
    });
    setIsCreateModalVisible(true);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Users Management</Text>
        <TouchableOpacity style={styles.addButton} onPress={openCreateModal}>
          <Text style={styles.addButtonText}>Add User</Text>
        </TouchableOpacity>
      </View>

      <UsersList onUserPress={handleUserPress} />

      {/* Create User Modal */}
      <Modal
        visible={isCreateModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsCreateModalVisible(false)}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Create New User</Text>

            <Controller
              control={control}
              rules={{required: 'Name is required'}}
              render={({field: {onChange, onBlur, value}}) => (
                <CInput
                  label="Name"
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value}
                  error={errors.name?.message}
                  placeholder="Enter name"
                />
              )}
              name="name"
            />

            <Controller
              control={control}
              rules={{
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address',
                },
              }}
              render={({field: {onChange, onBlur, value}}) => (
                <CInput
                  label="Email"
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value}
                  error={errors.email?.message}
                  placeholder="Enter email"
                  keyboardType="email-address"
                />
              )}
              name="email"
            />

            <Controller
              control={control}
              rules={{required: 'Password is required'}}
              render={({field: {onChange, onBlur, value}}) => (
                <CInput
                  label="Password"
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value}
                  error={errors.password?.message}
                  placeholder="Enter password"
                  secureTextEntry
                />
              )}
              name="password"
            />

            <View style={styles.modalButtons}>
              <CButton
                title="Cancel"
                onPress={() => setIsCreateModalVisible(false)}
                variant="secondary"
                style={styles.modalButton}
              />
              <CButton
                title="Create"
                onPress={handleSubmit(handleCreateUser)}
                loading={createUserMutation.isPending}
                style={styles.modalButton}
              />
            </View>
          </View>
        </View>
      </Modal>

      {/* Edit User Modal */}
      <Modal
        visible={isEditModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsEditModalVisible(false)}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Edit User</Text>

            {isLoadingUser ? (
              <Text>Loading user details...</Text>
            ) : (
              <>
                <Controller
                  control={control}
                  rules={{required: 'Name is required'}}
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label="Name"
                      onBlur={onBlur}
                      onChangeText={onChange}
                      value={value}
                      error={errors.name?.message}
                      placeholder="Enter name"
                    />
                  )}
                  name="name"
                />

                <Controller
                  control={control}
                  rules={{
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Invalid email address',
                    },
                  }}
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label="Email"
                      onBlur={onBlur}
                      onChangeText={onChange}
                      value={value}
                      error={errors.email?.message}
                      placeholder="Enter email"
                      keyboardType="email-address"
                    />
                  )}
                  name="email"
                />

                <View style={styles.modalButtons}>
                  <CButton
                    title="Cancel"
                    onPress={() => setIsEditModalVisible(false)}
                    variant="secondary"
                    style={styles.modalButton}
                  />
                  <CButton
                    title="Update"
                    onPress={handleSubmit(handleUpdateUser)}
                    loading={updateUserMutation.isPending}
                    style={styles.modalButton}
                  />
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  addButton: {
    backgroundColor: '#007bff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 5,
  },
});

export default UsersScreen;
