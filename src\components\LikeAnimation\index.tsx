import React, {JSX, useRef} from 'react';
import {View, TouchableOpacity, Animated, StyleSheet} from 'react-native';
import Typography from '@/components/Typography';
import {Icon} from '@/components';
import {useThemeStore} from '@/store';

function LikeAnimation({
  addLike,
  isLiked,
  totalLikes,
  label,
  size = 30,
}: {
  addLike: () => void;
  isLiked: boolean;
  totalLikes?: number;
  label: string;
  size?: number;
}): JSX.Element {
  const likeScaleAnim = useRef(new Animated.Value(1)).current;
  const theme = useThemeStore();

  const handleLikePress = () => {
    addLike();
    likeScaleAnim.setValue(1); // Reset scale
    Animated.spring(likeScaleAnim, {
      toValue: 1.4,
      friction: 3,
      tension: 100,
      useNativeDriver: true,
    }).start(() => {
      Animated.spring(likeScaleAnim, {
        toValue: 1,
        friction: 5,
        tension: 80,
        useNativeDriver: true,
      }).start();
    });
  };

  return (
    <View style={styles(theme).content}>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={handleLikePress}
        style={styles(theme).likeContainer}>
        <Animated.View
          style={{
            zIndex: 100,
            transform: [{scale: likeScaleAnim}],
          }}>
          <Icon
            name={isLiked ? 'heart' : 'heart-outline'}
            size={size}
            color={isLiked ? theme.colors.red : theme.colors.white}
          />
        </Animated.View>
        <Typography variant="subtitle" style={styles(theme).label}>
          {label}
        </Typography>
      </TouchableOpacity>
      {totalLikes && totalLikes > 0 && <Typography variant="subtitle">{totalLikes}</Typography>}
    </View>
  );
}

export default LikeAnimation;

const styles = (theme: any) =>
  StyleSheet.create({
    content: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    likeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    label: {
      color: theme.colors.white,
    },
  });
