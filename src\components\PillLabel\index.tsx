import React from 'react';
import {StyleSheet, ViewStyle, TextStyle, TouchableOpacity, View} from 'react-native';
import Typography from '../Typography';
import {useThemeStore} from '@/store/themeStore';

interface PillLabelProps {
  label?: string;
  backgroundColor?: string;
  textColor?: string;
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;
  onPress?: () => void;
  triangle?: boolean;
}

const PillLabel: React.FC<PillLabelProps> = ({
  label = 'Myself',
  backgroundColor,
  textColor = '#000000',
  containerStyle,
  textStyle,
  onPress,
  triangle = false,
}) => {
  const theme = useThemeStore();
  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={onPress}
      style={[
        styles.container,
        {
          backgroundColor: backgroundColor || theme.colors.dimGray,
        },
        containerStyle,
      ]}>
      <Typography variant="pillTitle" style={[{color: textColor}, textStyle || {}]}>
        {label}
      </Typography>
      {triangle && <View style={styles.triangle} />}
    </TouchableOpacity>
  );
};

export default PillLabel;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 88,
    paddingTop: 12,
    paddingLeft: 22,
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 0, // sharp corner
    position: 'relative',
    overflow: 'hidden',
  },
  triangle: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    width: 56, // adjust as needed
    height: 56, // adjust as needed
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftWidth: 56,
    borderTopWidth: 56,
    borderLeftColor: '#18B6F6', // triangle color
    borderTopColor: 'transparent',
    transform: [{rotate: '270deg'}],
  },
});
