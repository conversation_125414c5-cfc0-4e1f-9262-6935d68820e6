import {FONT_SIZE} from '@/utils/fonts';
import {StyleSheet} from 'react-native';

export const styles = (theme: any, variant?: string) =>
  StyleSheet.create({
    parkItem: {
      flexDirection: 'row',
      paddingVertical: 7,
    },
    pinContainer: {
      width: 50,
      alignItems: 'center',
      justifyContent: variant === 'equipment' ? 'flex-start' : 'center',
      marginTop: variant === 'equipment' ? 10 : 0,
    },
    parkInfo: {
      paddingLeft: 5,
      flex: 1,
    },
    parkName: {
      color: theme.colors.text,
      lineHeight: 20,
    },
    parkLocation: {
      color: theme.colors.text,
      marginTop: 3,
    },
    iconContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    equipParkName: {
      color: theme.colors.text,
      width: '90%',
      lineHeight: 20,
      marginBottom: 2,
    },
    courtsText: {
      color: theme.colors.text,
      fontSize: FONT_SIZE.sm,
      lineHeight: 16,
    },
    detailText: {
      color: theme.colors.text,
      fontSize: FONT_SIZE.sm,
      lineHeight: 18,
      marginTop: 2,
    },
    timeText: {
      color: theme.colors.text,
      fontSize: FONT_SIZE.lg,
      lineHeight: 16,
      marginTop: 10,
      marginLeft: -5,
    },
    editButton: {
      position: 'absolute',
      right: 0,
      top: -5,
    },
  });
