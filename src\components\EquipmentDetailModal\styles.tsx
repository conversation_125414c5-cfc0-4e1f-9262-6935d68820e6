import {StyleSheet} from 'react-native';
export const styles = (theme: any) =>
  StyleSheet.create({
    scrollView: {
      flexGrow: 1,
    },
    scroll: {
      alignItems: 'center',
    },
    modalContainer: {
      borderWidth: 1,
      borderColor: theme.colors.secondary,
      borderRadius: 23,
      paddingHorizontal: 25,
      paddingVertical: 10,
      backgroundColor: theme.colors.background,
      height: '93%',
      margin: 'auto',
      width: '90%',
    },
    header: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingBottom: 20,
    },
    closeIcon: {
      color: theme.colors.red,
    },
    tryNowContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    dropdownIcon: {
      marginTop: 20,
      marginBottom: 40,
    },
    plusIcon: {
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      color: theme.colors.activeColor,
      borderRadius: 50,
      padding: 1,
    },
    tryText: {
      color: theme.colors.activeColor,
      fontSize: 16,
      fontWeight: 'bold',
    },
    title: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginVertical: 15,
    },
    racketImage: {
      width: 212,
      height: 286,
    },
    specHeading: {
      color: theme.colors.activeColor,
      marginVertical: 5,
      alignSelf: 'flex-start',
    },

    specItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      paddingVertical: 3,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.text,
      marginBottom: 8,
    },
    label: {
      color: theme.colors.text,
      fontSize: 16,
    },

    arrow: {
      fontSize: 26,
      color: theme.colors.text,
      marginTop: 15,
      marginBlock: 30,
    },
    ratingRow: {
      flexDirection: 'row',
      gap: 10,
      marginVertical: 20,
    },
    ratingRow2: {
      flexDirection: 'row',
      gap: 5,
      marginVertical: 10,
    },

    reviewText: {
      color: theme.colors.text,
      fontSize: 16,
      fontWeight: '400',
    },
    reviewName: {
      color: theme.colors.text,
      fontWeight: '700',
      marginBottom: 5,
    },

    review: {
      color: theme.colors.primary,
      fontSize: 22,
      marginTop: 10,
      marginBottom: 4,
      alignSelf: 'flex-start',
    },
    reviewContainer: {
      alignSelf: 'flex-start',
      width: '100%',
    },
    reviewDivider: {
      height: 1,
      backgroundColor: theme.colors.secondary,
      width: '100%',
      marginVertical: 10,
    },
    moreText: {
      color: theme.colors.text,
      marginTop: 10,
    },
    videoPreview: {
      width: '100%',
      height: 170,
      borderRadius: 8,
    },
    videoContainer: {
      width: '100%',
      height: 170,
      borderRadius: 8,
      overflow: 'hidden',
      marginBottom: 20,
      backgroundColor: theme.colors.darkGray,
    },
    videoPlayer: {
      width: '100%',
      height: '100%',
      borderRadius: 8,
    },
    playButton: {
      backgroundColor: 'transparent',
      borderColor: theme.colors.orange,
      borderWidth: 5,
    },
    playArrow: {
      tintColor: theme.colors.orange,
    },
    video: {
      width: '100%',
      height: '100%',
      borderRadius: 8,
    },
    thumbnail: {
      width: '100%',
      height: '100%',
      borderRadius: 8,
    },
    wrapper: {
      flex: 1,
      borderRadius: 8,
    },
  });
