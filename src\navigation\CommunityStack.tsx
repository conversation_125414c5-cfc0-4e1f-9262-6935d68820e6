import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {CommunityScreen} from '@/screens/tabs';
// All community screens moved to root level for proper swipe-to-back navigation

export type CommunityStackParamList = {
  CommunityHome:
    | {
        type?: string;
        data?: any;
      }
    | undefined;
  PlayerConnectScreen: undefined;
  GoFit: undefined;
  GoEats: undefined;
  GoTravel: undefined;
  CommunityDetails: {
    from?: string;
    title?: string;
    data?: any;
  };
  CommentScreen: {
    from?: string;
    title?: string;
    data?: any;
  };
  CoachProfile: undefined;
  FindCoach: undefined;
  FindClass: undefined;
  MyGroups: undefined;
  NewGroup: {
    groupName?: string;
  };
  JoinGroups: undefined;
  JoinGroupDetails: undefined;
  AddMembers: {
    groupName: string;
  };
  CreateGroupMemberList: {
    groupImage?: string;
    groupName?: string;
    members: Array<{id: string; name: string; image: string}>;
  };
  Chat: undefined;
  PlayerConnectDateScreen: undefined;
};

const Stack = createStackNavigator<CommunityStackParamList>();

// Community stack with bottom sheet
const CommunityScreenWithBottomSheet = ({route}: {route: any}) => <CommunityScreen route={route} />;

const CommunityStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="CommunityHome"
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen name="CommunityHome" component={CommunityScreenWithBottomSheet} />
    </Stack.Navigator>
  );
};

export default CommunityStack;
