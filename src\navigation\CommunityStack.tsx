import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {CommunityScreen} from '@/screens/tabs';
import MyGroups from '@/screens/MyGroups';
import NewGroup from '@/components/Community/NewGroup';
import JoinGroups from '@/screens/JoinGroups';
import JoinGroupDetails from '@/screens/JoinGroupDetails';
import AddMembers from '@/components/Community/AddMembers';
import CreateGroupMemberList from '@/screens/CreateGroupMemberList';
import ChatScreen from '@/screens/Chat';
import PlayerConnectDateScreen from '@/screens/PlayerConnectDateScreen';
import CommunityDetails from '@/screens/CommunityDetails';
import CommentScreen from '@/screens/CommentScreen';
import CoachProfile from '@/screens/UpYourGame/CoachProfile';
import FindCoach from '@/screens/UpYourGame/FindCoach';
import FindClass from '@/screens/UpYourGame/FindClass';
import PlayerConnectScreen from '@/screens/tabs/PlayerConnectScreen';
import GoFit from '@/screens/GoFit';
import GoEats from '@/screens/GoEats';
import GoTravel from '@/screens/GoTravel';

export type CommunityStackParamList = {
  CommunityHome:
    | {
        type?: string;
        data?: any;
      }
    | undefined;
  PlayerConnectScreen: undefined;
  GoFit: undefined;
  GoEats: undefined;
  GoTravel: undefined;
  CommunityDetails: {
    from?: string;
    title?: string;
    data?: any;
  };
  CommentScreen: {
    from?: string;
    title?: string;
    data?: any;
  };
  CoachProfile: undefined;
  FindCoach: undefined;
  FindClass: undefined;
  MyGroups: undefined;
  NewGroup: {
    groupName?: string;
  };
  JoinGroups: undefined;
  JoinGroupDetails: undefined;
  AddMembers: {
    groupName: string;
  };
  CreateGroupMemberList: {
    groupImage?: string;
    groupName?: string;
    members: Array<{id: string; name: string; image: string}>;
  };
  Chat: undefined;
  PlayerConnectDateScreen: undefined;
};

const Stack = createNativeStackNavigator<CommunityStackParamList>();

// Community stack with bottom sheet
const CommunityScreenWithBottomSheet = ({route}: {route: any}) => <CommunityScreen route={route} />;

const CommunityStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="CommunityHome"
      screenOptions={{
        headerShown: false,
        gestureEnabled: true, // Enable swipe-to-back gesture
        gestureDirection: 'horizontal', // iOS style swipe from left edge
      }}>
      <Stack.Screen
        name="CommunityHome"
        component={CommunityScreenWithBottomSheet}
        options={{animation: 'fade', presentation: 'transparentModal'}}
      />
      <Stack.Screen
        name="MyGroups"
        component={MyGroups}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="NewGroup"
        component={NewGroup}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="JoinGroups"
        component={JoinGroups}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="JoinGroupDetails"
        component={JoinGroupDetails}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="AddMembers"
        component={AddMembers}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="CreateGroupMemberList"
        component={CreateGroupMemberList}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="Chat"
        component={ChatScreen}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="PlayerConnectDateScreen"
        component={PlayerConnectDateScreen}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="CommunityDetails"
        component={CommunityDetails}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="CommentScreen"
        component={CommentScreen}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="CoachProfile"
        component={CoachProfile}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="FindCoach"
        component={FindCoach}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="FindClass"
        component={FindClass}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="PlayerConnectScreen"
        component={PlayerConnectScreen}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="GoFit"
        component={GoFit}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="GoEats"
        component={GoEats}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen
        name="GoTravel"
        component={GoTravel}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          animation: 'slide_from_right',
        }}
      />
    </Stack.Navigator>
  );
};

export default CommunityStack;
