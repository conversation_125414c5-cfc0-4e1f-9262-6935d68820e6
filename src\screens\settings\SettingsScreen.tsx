import React, {useState, useMemo, useCallback} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View, StyleSheet, TouchableOpacity, ScrollView} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '@navigation/index';
import {useAuthStore, useConfigStore} from '@/store';
import {Header, Icon, SafeAreaView, LanguageSelector} from '@/components';
import SearchInput from '@/components/SearchInput';
import CustomToggleSwitch from '@/components/CustomToggleSwitch';
import Typography from '@/components/Typography';
import {useTranslationContext} from '@/context/TranslationContext';

type SettingsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Settings'>;

const SettingsScreen = () => {
  const navigation = useNavigation<SettingsScreenNavigationProp>();
  const {logout} = useAuthStore();
  const {resetBooking, resetAll, notifications, setNotifications} = useConfigStore();
  const theme = useThemeStore();
  const {t, currentLanguage} = useTranslationContext();
  const [showLanguageModal, setShowLanguageModal] = useState(false);

  const [searchValue, setSearchValue] = useState('');

  // We'll use a state to track if the language modal is visible

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },

    placeholder: {
      width: 36,
    },
    section: {
      marginBottom: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginLeft: 16,
      marginTop: 24,
      marginBottom: 16,
      letterSpacing: 0.5,
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 14,
      marginHorizontal: 16,
      borderBottomWidth: 1,
    },
    settingItemTransparent: {
      borderBottomColor: theme.colors.offWhite,
      backgroundColor: 'transparent',
    },
    settingItemLogout: {
      borderBottomColor: theme.colors.offWhite,
      backgroundColor: theme.colors.darkRed + '20',
    },
    settingItemLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    settingIcon: {
      marginRight: 12,
      width: 28,
      alignItems: 'center',
    },
    settingChevron: {
      marginLeft: 8,
    },
    settingSwitch: {
      marginLeft: 8,
    },
    logoutButton: {
      marginVertical: 24,
      marginHorizontal: 16,
      paddingVertical: 16,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
    },
    logoutButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    searchContainer: {
      paddingHorizontal: 16,
    },
    scrollView: {
      flexGrow: 1,
    },
    languageModalContainer: {
      position: 'absolute',
      width: '100%',
      height: '100%',
      zIndex: 1000,
    },
  });

  const handleLogout = useCallback(() => {
    resetBooking();
    resetAll();
    logout();
  }, [resetBooking, resetAll, logout]);

  const renderSettingItem = (
    title: string,
    onPress?: () => void,
    hasSwitch?: boolean,
    switchValue?: boolean,
    onToggle?: (value: boolean) => void,
    isLogout?: boolean,
    isLanguage?: boolean,
  ) => {
    // If it's the language setting, render the language selector button
    if (isLanguage) {
      return (
        <TouchableOpacity key={title} onPress={onPress}>
          <View style={[styles.settingItem, styles.settingItemTransparent]}>
            <Typography variant="notificationText" color={theme.colors.text}>
              {title}
            </Typography>
            <Icon
              name="dropdown2"
              size={22}
              color={theme.colors.white}
              style={{
                transform: [{rotate: '270deg'}],
              }}
            />
          </View>
        </TouchableOpacity>
      );
    }

    const settingItem = (
      <View
        style={[
          styles.settingItem,
          isLogout ? styles.settingItemLogout : styles.settingItemTransparent,
        ]}>
        <Typography
          variant="notificationText"
          color={isLogout ? theme.colors.darkRed : theme.colors.text}>
          {title}
        </Typography>

        {hasSwitch && onToggle ? (
          <CustomToggleSwitch isEnabled={switchValue} onToggle={onToggle} />
        ) : !isLogout ? (
          <Icon
            name="dropdown2"
            size={22}
            color={theme.colors.white}
            style={{
              transform: [{rotate: '270deg'}],
            }}
          />
        ) : null}
      </View>
    );

    if (onPress) {
      return (
        <TouchableOpacity onPress={onPress} key={title}>
          {settingItem}
        </TouchableOpacity>
      );
    }

    return <View key={title}>{settingItem}</View>;
  };

  const settingsItems = useMemo(
    () => [
      {
        title: 'settings.notifications',
        hasSwitch: true,
        switchValue: notifications,
        onToggle: (value: boolean) => setNotifications(value),
      },
      {
        title: 'settings.language',
        onPress: () => setShowLanguageModal(true),
        isLanguage: true,
      },
      {
        title: 'settings.twoFactorAuthentication',
        onPress: () => {},
      },
      {
        title: 'settings.shareLocationWithFriends',
        onPress: () => {},
      },
      {
        title: 'settings.biometrics',
        onPress: () => navigation.navigate('Biometrics'),
      },
      {
        title: 'auth.logout',
        onPress: handleLogout,
        isLogout: true,
      },
    ],
    [t, currentLanguage, notifications, setNotifications, navigation, handleLogout],
  );

  const filteredSettings = useMemo(() => {
    if (!searchValue.trim()) {
      return settingsItems;
    }
    const searchLower = searchValue.toLowerCase();
    return settingsItems.filter(item => t(item.title).toLowerCase().includes(searchLower));
  }, [searchValue, settingsItems, t]);

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <Header
        leftComponent={
          <TouchableOpacity onPress={() => navigation.goBack()} activeOpacity={0.7}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        }
        rightIcons={[
          {name: 'notification', size: 24, color: theme.colors.activeColor},
          {name: 'chat', size: 24, badge: 14, color: theme.colors.activeColor},
        ]}
        pageTitle={t('settings.settings')}
        backgroundColor={'transparent'}
        transparent={false}
        showBack={false}
      />

      <ScrollView style={styles.scrollView}>
        <View style={styles.searchContainer}>
          <SearchInput
            placeholder={t('settings.searchSettings')}
            value={searchValue}
            onChangeText={setSearchValue}
          />
        </View>

        {filteredSettings.map(item =>
          renderSettingItem(
            t(item.title),
            item.onPress,
            item.hasSwitch,
            item.switchValue,
            item.onToggle,
            item.isLogout,
            item.isLanguage,
          ),
        )}
      </ScrollView>

      {/* Language Selector Modal */}
      {showLanguageModal && (
        <LanguageSelector
          containerStyle={styles.languageModalContainer}
          initialModalVisible={true}
          onClose={() => {
            setShowLanguageModal(false);
            // Force a re-render of the component
            setTimeout(() => {
              console.log('Forcing re-render after language change');
            }, 100);
          }}
        />
      )}
    </SafeAreaView>
  );
};

export default SettingsScreen;
