import React from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import DateTimePicker from '../DateTimePicker';
import ParkCard from '../ParkCard';
import Typography from '../Typography';
import { useThemeStore } from '@/store/themeStore';;

interface SelectDateTimeSectionProps {
  onDateTimeSelect: (dateTime: any) => void;
  onBack: () => void;
  selectedPark: any;
}

export const SelectDateTimeSection: React.FC<SelectDateTimeSectionProps> = ({
  onDateTimeSelect,
  onBack,
  selectedPark,
}) => {
  const theme = useThemeStore();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 16,
      gap: 10,
    },
    backButton: {
      width: 30,
      height: 30,
      borderRadius: 15,
      justifyContent: 'flex-start',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.Gainsboro,
    },
    backButtonText: {
      color: theme.colors.Gainsboro,
      fontSize: 18,
      fontWeight: 'bold',
    },
    title: {
      marginVertical: 10,
      fontSize: 22,
      fontWeight: 'bold',
    },
  });

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={onBack}>
        <Typography style={styles.backButtonText}>{'\u2190'}</Typography>
      </TouchableOpacity>

      {selectedPark && (
        <ParkCard
          parkData={selectedPark}
          onSelect={park => {
            console.log('Selected park data:', park);
          }}
        />
      )}

      <Typography variant="subtitle" color={theme.colors.white} style={styles.title}>
        Select a date and time
      </Typography>

      <DateTimePicker
        onConfirm={onDateTimeSelect}
        onClose={onBack}
        allowRangeSelection
        minDate={new Date()}
        title=""
      />
    </View>
  );
};
