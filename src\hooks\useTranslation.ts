// hooks/useTranslation.ts
import {useCallback, useEffect} from 'react';
import {<PERSON><PERSON>, I18nManager} from 'react-native';
import {I18n} from 'i18n-js';
import api from '@/services/api';
import {useConfigStore} from '@/store/configStore';

import en from '@/translations/en.json';
import fr from '@/translations/fr.json';
import es from '@/translations/es.json';
import de from '@/translations/de.json';
import {usei18nStore} from '@/store/i18nStore';

import type {SupportedLanguage} from '@/store/i18nStore';

const RTL_LANGUAGES: SupportedLanguage[] = []; // Add 'ar', 'he' if needed

const localFiles: Record<SupportedLanguage, Record<string, any>> = {
  en,
  fr,
  es,
  de,
};

const i18n = new I18n(localFiles);
i18n.defaultLocale = 'en';
i18n.locale = 'en';
i18n.enableFallback = true;

export const useTranslation = () => {
  const {language, setLanguage, translationVersion} = usei18nStore();
  const bumpVersion = usei18nStore(state => state.bumpTranslationVersion);

  // Initialize language on mount
  useEffect(() => {
    if (language) {
      i18n.locale = language;
      updateLanguageJson(language);
    }
  }, []);

  // Apply language on language change
  useEffect(() => {
    if (language && i18n.locale !== language) {
      i18n.locale = language;
      updateLanguageJson(language);
    }
  }, [language]);

  // Translation function (will update if version changes)
  const t = useCallback(
    (key: string, params?: Record<string, string | number>): string => {
      try {
        return i18n.t(key, params);
      } catch (error) {
        console.warn(`Missing translation for key "${key}"`, error);
        return key;
      }
    },
    [translationVersion],
  );

  // Fetch translations from API or fallback
  const updateLanguageJson = async (lang: SupportedLanguage) => {
    try {
      const response = await api.get(`/app-configuration/app?language=${lang}`);
      const remote = response.data?.data?.json_data || {};
      i18n.store({[lang]: remote});
    } catch (error) {
      console.error(`Failed to fetch translations for ${lang}:`, error);
      // Alert.alert('Error', 'Could not fetch language from server. Using local fallback.');
      i18n.store({[lang]: localFiles[lang]});
    }
    bumpVersion();
  };

  // Change language and reload translation
  const switchLanguage = useCallback(
    async (lang: SupportedLanguage) => {
      const isRTL = RTL_LANGUAGES.includes(lang);
      if (I18nManager.isRTL !== isRTL) {
        try {
          I18nManager.allowRTL(isRTL);
          I18nManager.forceRTL(isRTL);
        } catch (e) {
          console.warn('Could not update RTL layout:', e);
        }
      }

      try {
        // Store language preference in i18nStore (persisted by zustand/middleware)
        setLanguage(lang);

        i18n.locale = lang;
        await updateLanguageJson(lang);
        return true;
      } catch (error) {
        console.error('Error changing language:', error);
        return false;
      }
    },
    [setLanguage],
  );

  const availableLanguages = [
    {code: 'en', name: 'English'},
    {code: 'fr', name: 'Français'},
    {code: 'es', name: 'Español'},
    {code: 'de', name: 'Deutsch'},
  ];

  return {
    t,
    currentLanguage: language,
    switchLanguage,
    availableLanguages,
    isRTL: RTL_LANGUAGES.includes(language),
  };
};

export default useTranslation;
