import {Dimensions, StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },

    flatListContainer: {
      flexGrow: 1,
      padding: 16,
      gap: 16,
    },
    reviewContainer: {
      marginBottom: 20,
    },
    cardContainer: {
      gap: 20,
      flex: 1,
    },
    headerContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 16,
    },
    headerTitle: {
      color: theme.colors.white,
    },
    imageContainer: {
      height: Dimensions.get('screen').height * 0.25,
      width: '100%',
    },
    actionContainer: {
      backgroundColor: theme.colors.black,
      paddingHorizontal: 30,
      paddingVertical: 6,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 20,
    },

    searchContainer: {
      marginBottom: 10,
    },

    searchBar: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      paddingHorizontal: 20,
      paddingVertical: 10,
      gap: 20,
      borderWidth: 1,
      borderColor: theme.colors.primary,
    },
    searchIcon: {
      marginRight: 8,
      fontSize: 16,
    },
    searchInput: {
      color: theme.colors.primary,
    },
    btn: {
      width: '100%',
      borderWidth: 0,
      marginTop: 20,
    },
    footer: {
      padding: 16,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
  });
