import React, {useState} from 'react';
import {View, TouchableOpacity, FlatList} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import Typography from '@/components/Typography';
import {useNavigation, RouteProp} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import {createStyles} from './styles';
import CInput from '@/components/CInput';
import {Icon, SafeAreaView} from '@/components';
import CImage from '@/components/CImage';
import useTranslation from '@/hooks/useTranslation';
import UploadComponent from '@/components/Cupload/Index';

type NavigationProp = NativeStackNavigationProp<CommunityStackParamList>;
type CreateGroupMemberListParams = {
  groupImage?: string;
  groupName?: string;
  members: Array<{id: string; name: string; image: string}>;
  // add any other params you expect here
};
type CreateGroupMemberListRouteProp = RouteProp<
  {CreateGroupMemberList: CreateGroupMemberListParams},
  'CreateGroupMemberList'
>;

const CreateGroupMemberList: React.FC<{route: CreateGroupMemberListRouteProp}> = ({route}) => {
  const data = route.params;
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation<NavigationProp>();

  const [gImg, setGImg] = useState<string>(data?.groupImage || '');
  const [gName, setGname] = useState<string>(data?.groupName || '');
  const [groupNameError, setGroupNameError] = useState<string>('');
  const [selectedMembers, setSelectedMembers] = useState(data?.members);

  const {t} = useTranslation();

  return (
    <SafeAreaView includeTop={true} includeBottom={false}>
      <View style={styles.content}>
        <View style={styles.headerRow}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Typography variant="parkTitle" color={theme.colors.primary}>
              {t('common.back')}
            </Typography>
          </TouchableOpacity>
          <Typography variant="parkTitle" color={theme.colors.white}>
            {t('createGroupMemberListScreen.newGroup')}
          </Typography>
          <TouchableOpacity onPress={() => navigation.navigate('MyGroups')}>
            {/* <TouchableOpacity onPress={handleSubmit(onSubmit)}> */}
            <Typography variant="parkTitle" color={theme.colors.activeColor}>
              {t('common.create')}
            </Typography>
          </TouchableOpacity>
        </View>

        <View style={styles.groupNameContainer}>
          <UploadComponent
            onSelected={val => setGImg(val)}
            value={gImg}
            allowCameraCapture={true}
            allowGallerySelection={true}
            containerStyle={styles.cameraCircle}
            imageContentStyle={styles.imageContent}
          />
          <View style={{flex: 1}}>
            <CInput
              containerStyle={styles.groupNameInput}
              placeholder={t('createGroupMemberListScreen.groupNamePlaceholder')}
              inputStyle={styles.groupNameInput}
              placeholderTextColorStyle={theme.colors.activeColor}
              value={gName}
              onChangeText={setGname}
              error={groupNameError}
            />
          </View>
        </View>
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.groupSettings}
          onPress={() =>
            navigation.navigate('NewGroup', {
              ...data,
              members: selectedMembers,
            })
          }>
          <Typography variant="notificationText" color={theme.colors.white}>
            {t('createGroupMemberListScreen.groupSettings')}
          </Typography>
          <Icon name="Right-chevron" size={20} color={theme.colors.white} />
        </TouchableOpacity>

        <View style={styles.selectedMembersContainer}>
          <Typography
            style={styles.selectedMembersTitle}
            variant="notificationText"
            color={theme.colors.white}>
            {t('createGroupMemberListScreen.members')} {selectedMembers.length}{' '}
            {t('createGroupMemberListScreen.of')} 25
          </Typography>
          <FlatList
            data={selectedMembers}
            horizontal
            keyExtractor={item => item.id}
            contentContainerStyle={styles.memberList}
            renderItem={({item}) => (
              <View style={styles.memberContainer}>
                <View style={styles.memberAvatarContainer}>
                  <CImage source={{uri: item.image}} style={styles.memberAvatar} />
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => {
                      setSelectedMembers(prev => prev.filter(m => m.id !== item.id));
                    }}>
                    <Icon name="close" size={12} color={theme.colors.black} />
                  </TouchableOpacity>
                </View>
                <Typography
                  style={styles.userName}
                  color={theme.colors.inputLabel}
                  variant="caption">
                  {item.name}
                </Typography>
              </View>
            )}
            showsHorizontalScrollIndicator={false}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default CreateGroupMemberList;
