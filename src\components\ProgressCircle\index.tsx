import React, {useEffect, useRef} from 'react';
import {View, Animated, ViewStyle} from 'react-native';
import Svg, {Circle} from 'react-native-svg';
import {useThemeStore} from '@/store';
import {createStyles} from './styles';
import Typography from '../Typography';

interface ProgressCircleProps {
  title?: string;
  progress?: number;
  maxProgress?: number;
  size?: number;
  strokeWidth?: number;
  backgroundColor?: string;
  progressColor?: string;
  style?: ViewStyle;
  progressDesc?: string;
}

const ProgressCircle: React.FC<ProgressCircleProps> = ({
  title = 'Steps',
  progress = 0,
  maxProgress = 13,
  size = 130,
  strokeWidth = 10,
  backgroundColor = '#2B2B2B',
  progressColor = '#DFFF4F',
  style,
  progressDesc = 'steps',
}) => {
  const theme = useThemeStore();
  const animatedProgress = useRef(new Animated.Value(0)).current;
  // Calculate circle dimensions and properties
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const styles = createStyles(theme);

  useEffect(() => {
    // Animate the progress
    Animated.spring(animatedProgress, {
      toValue: ((progress <= maxProgress ? progress : maxProgress) / (maxProgress || 1)) * 100,
      friction: 5,
      tension: 40,
      useNativeDriver: true,
    }).start();
  }, [progress, maxProgress, animatedProgress]);

  // Interpolate the progress for stroke dashoffset
  const animatedStrokeDashoffset = animatedProgress.interpolate({
    inputRange: [0, 100],
    outputRange: [circumference, 0],
  });

  return (
    <View style={style}>
      <Typography variant="progressHeadingText" style={styles.progressText}>
        {title}
      </Typography>
      <View style={styles.progressContainer}>
        <View style={styles.svgWrapper}>
          <Svg width={size} height={size} style={styles.svgRotated}>
            {/* Background Circle */}
            <Circle
              cx={size / 2}
              cy={size / 2}
              r={radius}
              fill="transparent"
              stroke={backgroundColor}
              strokeWidth={strokeWidth}
            />

            {/* Progress Circle */}
            <AnimatedCircle
              cx={size / 2}
              cy={size / 2}
              r={radius}
              fill="transparent"
              stroke={progressColor}
              strokeWidth={strokeWidth}
              strokeDasharray={circumference}
              strokeDashoffset={animatedStrokeDashoffset}
              strokeLinecap="round"
            />
          </Svg>

          {/* Text Overlay */}
          <View style={styles.progressTextContainer}>
            <Typography variant="progressNumberText" style={styles.progressNumberText}>
              {progress}
            </Typography>
            <Typography variant="progressDescText" style={styles.progressDescText}>
              {progressDesc}
            </Typography>
          </View>
        </View>
      </View>
    </View>
  );
};

// Create an Animated Circle component
const AnimatedCircle = Animated.createAnimatedComponent(Circle);

export default ProgressCircle;
