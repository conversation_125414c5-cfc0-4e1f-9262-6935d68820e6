import React from 'react';
import {View, StyleSheet} from 'react-native';
import {PanGestureHandler, State} from 'react-native-gesture-handler';
import {useNavigation, useRoute} from '@react-navigation/native';

interface TabStackGestureHandlerProps {
  children: React.ReactNode;
  enabled?: boolean;
  threshold?: number;
}

/**
 * Custom gesture handler that enables swipe-to-back for nested stack navigators
 * inside bottom tab navigators. This solves the issue where bottom tab navigators
 * don't support native swipe-to-back gestures for their nested stacks.
 */
const TabStackGestureHandler: React.FC<TabStackGestureHandlerProps> = ({
  children,
  enabled = true,
  threshold = 100,
}) => {
  const navigation = useNavigation();
  const route = useRoute();

  // Define screens that should have swipe-to-back functionality
  const swipeBackScreens = [
    'MyGroups', 'NewGroup', 'JoinGroups', 'JoinGroupDetails',
    'AddMembers', 'CreateGroupMemberList', 'Chat', 'CommunityDetails',
    'CommentScreen', 'CoachProfile', 'FindCoach', 'FindClass',
    'PlayerConnectDateScreen', 'GoFit', 'GoEats', 'GoTravel'
  ];

  const shouldEnableSwipe = enabled && swipeBackScreens.includes(route.name);

  const onGestureEvent = (event: any) => {
    const {translationX, velocityX, state} = event.nativeEvent;

    // Check if gesture is a swipe from left edge to right
    if (
      shouldEnableSwipe &&
      state === State.END &&
      translationX > threshold &&
      velocityX > 0
    ) {
      // Navigate back if possible
      if (navigation.canGoBack()) {
        navigation.goBack();
      }
    }
  };

  if (!shouldEnableSwipe) {
    return <View style={styles.container}>{children}</View>;
  }

  return (
    <PanGestureHandler onHandlerStateChange={onGestureEvent}>
      <View style={styles.container}>{children}</View>
    </PanGestureHandler>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default TabStackGestureHandler;
