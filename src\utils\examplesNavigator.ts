/**
 * Utility functions for navigating to example screens
 */
import {CommonActions} from '@react-navigation/native';

/**
 * Navigate to the OptimizedFlatList example screen
 * @param navigation The navigation object from React Navigation
 */
export const navigateToOptimizedFlatListExample = (navigation: any) => {
  // First navigate to the Examples stack
  navigation.navigate('Examples');

  // Then navigate to the specific example screen within the stack
  navigation.dispatch(
    CommonActions.navigate({
      name: 'Examples',
      params: {
        screen: 'OptimizedFlatList',
      },
    }),
  );
};

/**
 * Navigate back from an example screen
 * @param navigation The navigation object from React Navigation
 */
export const navigateBackFromExample = (navigation: any) => {
  navigation.goBack();
};
