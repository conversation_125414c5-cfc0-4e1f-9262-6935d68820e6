import React from 'react';
import {View, Image, StyleSheet} from 'react-native';
import {Images} from '@/config/images';
import {useThemeStore} from '@/store';
import CImage from '@/components/CImage';

interface CustomMarkerProps {
  type: 'available_kiosk' | 'planned_location' | 'player';
  pin: any;
}

const CustomMarker: React.FC<CustomMarkerProps> = ({type, pin}) => {
  const theme = useThemeStore();
  // Render either CImage for remote images or Image for local images
  const renderImage = () => {
    if (typeof pin.image === 'string' && pin.image.startsWith('http')) {
      return (
        <CImage
          source={{uri: pin.image}}
          style={styles(theme).customMarkerImage}
          fallbackSource={Images.profile1}
          resizeMode="cover"
        />
      );
    }
    return <CImage source={pin.image} style={styles(theme).customMarkerImage} resizeMode="cover" />;
  };
  if (type === 'player') {
    return (
      <View style={styles(theme).markerContainer}>
        <View style={styles(theme).customMarker}>{renderImage()}</View>
        <View style={styles(theme).customMarkerBadge} />
      </View>
    );
  } else {
    return (
      <View style={styles(theme).markerContainer}>
        <Image
          source={type === 'available_kiosk' ? Images.availableKiosk : Images.plannedLocation}
          style={styles(theme).markerImage}
          resizeMode="contain"
        />
      </View>
    );
  }
};

const styles = (theme: any) =>
  StyleSheet.create({
    markerContainer: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    markerImage: {
      width: 32,
      height: 32,
    },
    customMarker: {
      width: 32,
      height: 32,
      borderWidth: 1,
      borderColor: theme.colors.activeColor,
      borderRadius: 100,
    },
    customMarkerImage: {
      width: '100%',
      height: '100%',
      borderRadius: 100,
    },
    customMarkerBadge: {
      width: 0,
      height: 0,
      borderLeftWidth: 5,
      borderRightWidth: 5,
      borderTopWidth: 5,
      borderLeftColor: 'transparent',
      borderRightColor: 'transparent',
      borderTopColor: theme.colors.activeColor,
      marginTop: 1,
    },
  });

export default CustomMarker;
