import React, {useState, useCallback, useMemo, memo} from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  Platform,
} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {SAMPLE_DATA, ListItem} from './sampleData';
import Typography from '../Typography';

// The height of each item - used for getItemLayout optimization
const ITEM_HEIGHT = 100;

// Memoized list item component to prevent unnecessary re-renders
const ListItemComponent = memo(
  ({item, onPress}: {item: ListItem; onPress: (id: string) => void}) => {
    const theme = useThemeStore();

    // Handle item press with useCallback to prevent function recreation
    const handlePress = useCallback(() => {
      onPress(item.id);
    }, [item.id, onPress]);

    return (
      <TouchableOpacity
        style={[styles.itemContainer, {borderBottomColor: theme.colors.divider}]}
        onPress={handlePress}
        activeOpacity={0.7}>
        <Image source={{uri: item.image}} style={styles.itemImage} />
        <View style={styles.itemContent}>
          <Typography variant="subtitle" numberOfLines={1}>
            {item.title}
          </Typography>
          <Typography variant="body" numberOfLines={2} style={{color: theme.colors.textSecondary}}>
            {item.description}
          </Typography>
          <Typography variant="caption" style={{color: theme.colors.textTertiary}}>
            {new Date(item.timestamp).toLocaleDateString()}
          </Typography>
        </View>
      </TouchableOpacity>
    );
  },
  // Custom comparison function for memo
  (prevProps, nextProps) => {
    // Only re-render if the item id changes
    return prevProps.item.id === nextProps.item.id;
  },
);

// Optimized FlatList example component
const OptimizedFlatListExample = () => {
  const theme = useThemeStore();
  const [data, setData] = useState(SAMPLE_DATA.slice(0, 50));
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Memoized key extractor function
  const keyExtractor = useCallback((item: ListItem) => item.id, []);

  // Memoized getItemLayout function for fixed height items
  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: ITEM_HEIGHT,
      offset: ITEM_HEIGHT * index,
      index,
    }),
    [],
  );

  // Memoized renderItem function
  const renderItem = useCallback(
    ({item}: {item: ListItem}) => <ListItemComponent item={item} onPress={handleItemPress} />,
    [handleItemPress],
  );

  // Handle item press
  const handleItemPress = useCallback((id: string) => {
    console.log(`Item pressed: ${id}`);
    // Navigate or perform action here
  }, []);

  // Handle end reached - load more data
  const handleEndReached = useCallback(() => {
    if (loading || data.length >= SAMPLE_DATA.length) return;

    setLoading(true);

    // Simulate network request delay
    setTimeout(() => {
      const newData = [...data, ...SAMPLE_DATA.slice(data.length, data.length + 20)];
      setData(newData);
      setLoading(false);
    }, 1000);
  }, [data, loading]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setRefreshing(true);

    // Simulate network request delay
    setTimeout(() => {
      setData(SAMPLE_DATA.slice(0, 50));
      setRefreshing(false);
    }, 1500);
  }, []);

  // Memoized footer component
  const ListFooterComponent = useMemo(() => {
    if (!loading) return null;

    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
        <Typography variant="caption" style={{marginLeft: 8}}>
          Loading more items...
        </Typography>
      </View>
    );
  }, [loading, theme.colors.primary]);

  // Memoized empty component
  const ListEmptyComponent = useMemo(
    () => (
      <View style={styles.emptyContainer}>
        <Typography variant="subtitle" align="center">
          No items found
        </Typography>
        <Typography variant="body" align="center" style={{marginTop: 8}}>
          Pull down to refresh
        </Typography>
      </View>
    ),
    [],
  );

  return (
    <View style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        getItemLayout={getItemLayout}
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.5}
        ListFooterComponent={ListFooterComponent}
        ListEmptyComponent={ListEmptyComponent}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        // Performance optimization props
        removeClippedSubviews={Platform.OS === 'android'} // Use on Android, can cause issues on iOS
        maxToRenderPerBatch={10} // Default is 10
        updateCellsBatchingPeriod={50} // Default is 50ms
        initialNumToRender={10} // Render enough to fill the screen
        windowSize={21} // Default is 21 (10 viewports above, 10 below, 1 visible)
        // Style props
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  itemContainer: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    padding: 12,
    borderBottomWidth: 1,
    height: ITEM_HEIGHT,
  },
  itemImage: {
    width: 70,
    height: 70,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  itemContent: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'space-between',
  },
  footer: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default OptimizedFlatListExample;
