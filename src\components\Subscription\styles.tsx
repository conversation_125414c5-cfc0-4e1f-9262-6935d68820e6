import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    scrollViewContainer: {
      // flexGrow: 1,
      // margin: 20,
      paddingHorizontal: 20,
      paddingVertical: 20,
    },

    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },

    promoMessageContainer: {
      backgroundColor: theme.colors.darkGray,
      padding: 20,
      borderRadius: 8,
      marginTop: 30,
    },

    divider: {
      height: 1,
      backgroundColor: theme.colors.divider,
      marginVertical: 30,
    },
    bulletPointsContainer: {
      paddingLeft: 10,
      gap: 30,
      marginBottom: 30,
    },
    bulletPoint: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },

    bulletTextContainer: {
      flex: 1,
    },

    termsText: {
      textAlign: 'center',
      paddingHorizontal: 35,
      marginTop: 10,
    },

    termsLink: {
      marginVertical: 30,
      textAlign: 'center',
    },
  });
