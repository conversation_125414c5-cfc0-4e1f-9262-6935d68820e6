import {Platform, StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    centeredView: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    clearButtonRight: {
      paddingVertical: 0,
      paddingHorizontal: 10,
      color: theme.colors.text,
    },
    modalOverlay: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.shadowColor,
    },
    bottomOverlay: {
      flex: 1,
      justifyContent: 'flex-end',
      backgroundColor: theme.colors.semiTransparentBlack,
    },
    gradientContainer: {
      width: '100%',
      height: '100%',
    },
    gradientContent: {
      width: '100%',
      borderRadius: 12,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      padding: 16,
      flex: 1,
      justifyContent: 'center',
    },
    borderContainer: {
      // flex: 1,
      height: Platform.OS === 'ios' ? '92%' : '100%',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.white,
      // padding: 10,
    },
    modalContent: {
      minWidth: '85%',
      backgroundColor: theme.colors.background,
      borderRadius: 12,
      padding: 25,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      shadowColor: theme.colors.black,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 10,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 15,
    },
    closeButton: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    closeButtonRight: {
      marginLeft: 'auto',
      marginTop: 10,
      marginRight: 10,
    },
    title: {
      flex: 1,
      textAlign: 'center',
      color: theme.colors.activeColor,
      fontWeight: 'bold',
    },
    contentContainer: {
      marginBottom: 15,
    },

    overlay: {
      flex: 1,
    },
  });
