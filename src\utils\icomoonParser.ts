import {Platform} from 'react-native';

interface IconMap {
  [key: string]: string;
}

interface IcomoonIcon {
  properties: {
    name: string;
    code: number;
  };
}

interface IcomoonSelection {
  icons: IcomoonIcon[];
  preferences: {
    fontPref: {
      metadata: {
        fontFamily: string;
      };
    };
  };
}

export const parseIconsFromSelection = (selection: IcomoonSelection): IconMap => {
  const iconMap: IconMap = {};

  selection.icons.forEach(icon => {
    const name = icon.properties.name;
    const code = icon.properties.code;
    iconMap[name] = String.fromCodePoint(code);
  });

  return iconMap;
};

export const getIcomoonFontFamily = (selection: IcomoonSelection): string => {
  const fontFamily = selection.preferences.fontPref.metadata.fontFamily;
  // React Native on Android often needs the exact filename
  return Platform.OS === 'ios' ? fontFamily : fontFamily.toLowerCase();
};
