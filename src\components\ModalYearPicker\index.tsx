import React, {useState, useMemo, useRef, useEffect} from 'react';
import {Text, TouchableOpacity, StyleSheet, Dimensions, ScrollView, View} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import CustomModal from '../Modal';

interface YearPickerModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  initialYear?: number;
  onYearSelected: (year: number) => void;
}

const ITEM_HEIGHT = 50;

const ModalYearPicker: React.FC<YearPickerModalProps> = ({
  visible,
  onClose,
  title,
  initialYear = new Date().getFullYear(),
  onYearSelected,
}) => {
  const theme = useThemeStore();
  const [isInitialized, setIsInitialized] = useState(false);

  const years = useMemo(() => {
    const currentYear = new Date().getFullYear();
    // Generate years from (currentYear - 59) to currentYear (total 60 years)
    return Array.from({length: 60}, (_, i) => currentYear - 59 + i);
  }, []);

  const initialIndex = useMemo(() => {
    const index = years.indexOf(initialYear);
    return index >= 0 ? index : years.length / 2; // Default to middle if not found
  }, [years, initialYear]);

  const scrollViewRef = useRef<ScrollView>(null);
  const [selectedIndex, setSelectedIndex] = useState(initialIndex);
  const [ready, setReady] = useState(false);

  // Reset selected index when modal becomes visible
  useEffect(() => {
    if (visible) {
      const index = years.indexOf(initialYear);
      const validIndex = index >= 0 ? index : years.length / 2;
      setSelectedIndex(validIndex);
      setIsInitialized(false);

      // Use a longer timeout to ensure layout is complete
      setTimeout(() => {
        setReady(true);
        // Wait a bit more to ensure the ScrollView is rendered
        setTimeout(() => {
          if (scrollViewRef.current) {
            scrollViewRef.current.scrollTo({
              y: validIndex * ITEM_HEIGHT,
              animated: false,
            });
            setIsInitialized(true);
          }
        }, 100);
      }, 200);
    } else {
      setReady(false);
    }
  }, [visible, initialYear, years]);

  // Additional effect to ensure scrolling happens after render
  useEffect(() => {
    if (visible && ready && !isInitialized && scrollViewRef.current) {
      const index = years.indexOf(initialYear);
      const validIndex = index >= 0 ? index : years.length / 2;

      const scrollTimer = setTimeout(() => {
        if (scrollViewRef.current) {
          scrollViewRef.current.scrollTo({
            y: validIndex * ITEM_HEIGHT,
            animated: false,
          });
          setIsInitialized(true);
        }
      }, 300);

      return () => clearTimeout(scrollTimer);
    }
  }, [visible, ready, isInitialized, initialYear, years]);

  const scrollToIndex = (index: number, animated = true) => {
    if (scrollViewRef.current && index >= 0) {
      const yOffset = index * ITEM_HEIGHT;
      scrollViewRef.current.scrollTo({
        y: yOffset,
        animated,
      });
    }
  };

  const handleConfirm = () => {
    onYearSelected(years[selectedIndex]);
    onClose();
  };

  const handleScroll = (event: any) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    // Using Math.round for more accurate selection
    const index = Math.round(offsetY / ITEM_HEIGHT);

    if (index >= 0 && index < years.length) {
      // Only update if the index actually changed to prevent unnecessary re-renders
      if (index !== selectedIndex) {
        setSelectedIndex(index);
      }
    }
  };

  // Handle year selection by tapping
  const handleYearPress = (index: number) => {
    setSelectedIndex(index);
    // Use exact positioning for tap selection
    scrollToIndex(index);
  };

  // Get the currently selected year
  const selectedYear = years[selectedIndex] || initialYear;

  const renderYearItem = (year: number, index: number) => {
    const isSelected = index === selectedIndex;
    const distanceFromCenter = Math.abs(index - selectedIndex);

    return (
      <TouchableOpacity
        key={year.toString()}
        style={[styles(theme).item]}
        onPress={() => handleYearPress(index)}
        activeOpacity={0.7}>
        <Text
          style={[
            styles(theme).itemText,
            isSelected
              ? styles(theme).selectedText
              : distanceFromCenter <= 1
                ? styles(theme).nearbyText
                : styles(theme).farText,
          ]}>
          {year}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <CustomModal
      visible={visible}
      onClose={onClose}
      variant="bottom"
      title={title}
      titleStyle={{color: theme.colors.text}}
      modalContainerStyle={styles(theme).modalContent}>
      <View style={styles(theme).pickerContainer}>
        {/* Highlight box */}
        <View style={styles(theme).highlightOverlay} pointerEvents="none" />

        {ready && (
          <ScrollView
            ref={scrollViewRef}
            snapToInterval={ITEM_HEIGHT}
            decelerationRate="fast"
            scrollEventThrottle={16}
            showsVerticalScrollIndicator={false}
            onScroll={handleScroll}
            onMomentumScrollEnd={event => {
              handleScroll(event);
              // Force snap to exact position after momentum scroll ends
              setTimeout(() => {
                scrollToIndex(selectedIndex);
              }, 50);
            }}
            contentContainerStyle={styles(theme).scrollContent}>
            {years.map(renderYearItem)}
          </ScrollView>
        )}
      </View>

      <TouchableOpacity
        style={[styles(theme).confirmButton, {borderColor: theme.colors.activeColor}]}
        onPress={handleConfirm}>
        <Text style={[styles(theme).confirmText, {color: theme.colors.activeColor}]}>Done</Text>
      </TouchableOpacity>
    </CustomModal>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    modalContent: {
      backgroundColor: theme.colors.blackBg,
      padding: 20,
      alignItems: 'center',
      justifyContent: 'center',
      width: Dimensions.get('window').width,
      maxHeight: Dimensions.get('window').height * 0.6,
      height: Dimensions.get('window').height * 0.5,
    },
    pickerContainer: {
      height: ITEM_HEIGHT * 5,
      width: '60%',
      position: 'relative',
      overflow: 'hidden',
    },
    scrollContent: {
      paddingVertical: ITEM_HEIGHT * 2,
    },
    item: {
      height: ITEM_HEIGHT,
      justifyContent: 'center',
      alignItems: 'center',
      width: '100%',
      paddingVertical: 5,
    },

    itemText: {
      fontSize: 20,
      textAlign: 'center',
    },
    selectedText: {
      fontWeight: '700',
      color: theme.colors.activeColor,
      fontSize: 22,
    },
    nearbyText: {
      color: theme.colors.text,
      fontWeight: '500',
    },
    farText: {
      color: theme.colors.bgGrey,
      fontWeight: '400',
    },
    highlightOverlay: {
      position: 'absolute',
      top: ITEM_HEIGHT * 2,
      height: ITEM_HEIGHT,
      left: 0,
      right: 0,
      borderTopWidth: 1,
      borderBottomWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.3)',
      zIndex: 1,
    },

    confirmButton: {
      marginTop: 10,
      paddingVertical: 12,
      paddingHorizontal: 40,
      borderWidth: 1,
      borderRadius: 20,
    },
    confirmText: {
      fontSize: 16,
      fontWeight: '600',
    },
  });

export default ModalYearPicker;
