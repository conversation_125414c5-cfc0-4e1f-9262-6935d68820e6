import React from 'react';
import {View, TouchableOpacity, FlatList, ScrollView} from 'react-native';
import {createStyles} from './styles';
import {useThemeStore} from '@/store';
import Typography from '@/components/Typography';
import PlayerCard from '@/components/PlayerCard';
import {Images} from '@/config';
import {CImage, Header, Icon, SafeAreaView} from '@/components';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import useTranslation from '@/hooks/useTranslation';
const JoinGroupDetails = () => {
  const theme = useThemeStore();
  const navigation = useNavigation();
  const styles = createStyles(theme);
  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const {t} = useTranslation();

  const playerData = [
    {
      id: '1',
      name: '<PERSON>',
      rating: '4.5',
      location: 'New York, NY',
      image: 'https://via.placeholder.com/150',
    },
    {
      id: '2',
      name: '<PERSON>',
      rating: '4.5',
      location: 'New York, NY',
      image: 'https://via.placeholder.com/150',
    },
    {
      id: '3',
      name: 'John <PERSON>e',
      rating: '4.5',
      location: 'New York, NY',
      image: 'https://via.placeholder.com/150',
    },
    {
      id: '4',
      name: 'John Doe',
      rating: '4.5',
      location: 'New York, NY',
      image: 'https://via.placeholder.com/150',
    },
    {
      id: '5',
      name: 'John Doe',
      rating: '4.5',
      location: 'New York, NY',
      image: 'https://via.placeholder.com/150',
    },
  ];
  return (
    <SafeAreaView includeBottom={false} style={styles.container}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: theme.colors.primary,
        }}
        leftIconButtonStyle={styles.menuButton}
        onLeftPress={openDrawer}
        rightIcons={[
          {name: 'notification', size: 24, badge: 0},
          {name: 'chat', size: 24, badge: 14},
        ]}
        pageTitle={t('joinGroupDetailsScreen.title')}
        backgroundColor="transparent"
        // showBack={false}
      />
      <View style={styles.header}>
        <TouchableOpacity activeOpacity={0.8} onPress={() => navigation.goBack()}>
          <Typography variant="parkTitle" style={styles.cancel}>
            {t('common.cancel')}
          </Typography>
        </TouchableOpacity>
        <TouchableOpacity activeOpacity={0.8} onPress={() => navigation.goBack()}>
          <Typography variant="parkTitle" style={styles.next}>
            {t('common.next')}
          </Typography>
        </TouchableOpacity>
      </View>
      <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
        <View style={styles.groupInfoCard}>
          <View style={styles.avatarContainer}>
            <CImage source={Images.coachProfile} style={styles.avatar} resizeMode="contain" />
            <View style={styles.groupInfoContent}>
              <Typography variant="parkTitle" style={styles.groupName}>
                Group Name
              </Typography>
              <Typography variant="caption" style={styles.members}>
                9 members
              </Typography>
              <View style={styles.locationRow}>
                <Icon
                  name="location-pin"
                  size={22}
                  color={theme?.colors?.activeColor}
                  style={{marginLeft: -4}}
                />
                <Typography variant="caption" style={styles.location}>
                  Fort Greene
                </Typography>
              </View>
            </View>
          </View>

          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => {
              navigation.goBack();
            }}>
            <Typography variant="playerTitle" style={styles.joinText}>
              {t('joinGroupDetailsScreen.joinGroup')}
            </Typography>
          </TouchableOpacity>
        </View>
        <Typography variant="description" style={styles.description}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce faucibus convallis turpis,
          eu lobortis eros. Nam at risus posuere ante tincidunt feugiat. Quisque molestie justo non
          ante suscipit iaculis. Nulla porttitor justo et scelerisque pretium. Quisque aliquam
          fringilla eros, vitae eleifend odio placerat in. Vivamus vitae cursus arcu. Nullam eu
          nulla augue.
        </Typography>

        <FlatList
          data={playerData}
          renderItem={({item}) => <PlayerCard playerData={item} />}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.playerList}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default JoinGroupDetails;
