/**
 * <PERSON>ript to help update AppContext imports to direct Zustand+MMKV store imports
 *
 * To run: node update-appcontext-to-zustand.js
 */

const fs = require('fs');
const path = require('path');
const {execSync} = require('child_process');

// Find all files using AppContext
const findAppContextFiles = () => {
  try {
    const grepCommand =
      "grep -r 'useTheme\\|useConfig\\|useAuth\\|useAppContext\\|AppContext' --include='*.tsx' --include='*.ts' src/";
    const result = execSync(grepCommand, {encoding: 'utf-8'});

    // Parse grep results
    const files = new Set();
    result.split('\n').forEach(line => {
      if (!line) return;
      const filePath = line.split(':')[0];
      if (filePath) {
        files.add(filePath);
      }
    });

    return Array.from(files);
  } catch (error) {
    console.error('Error finding AppContext files:', error.message);
    return [];
  }
};

// Process a file to update AppContext imports to Zustand
const processFile = filePath => {
  try {
    console.log(`Processing ${filePath}...`);
    let content = fs.readFileSync(filePath, 'utf-8');

    // Skip files that are already using the new Zustand stores
    if (
      content.includes('useThemeStore') ||
      content.includes('useConfigStore') ||
      content.includes('useAuthStore')
    ) {
      console.log(`File ${filePath} is already using Zustand stores, skipping...`);
      return false;
    }

    // Check what is being used
    const usesAppContext = content.includes('useAppContext');
    const usesTheme = content.includes('useTheme');
    const usesConfig = content.includes('useConfig');
    const usesAuth = content.includes('useAuth');
    const usesThemeState = content.includes('ThemeState');

    // Track if we made changes
    let modified = false;

    // Replace AppContext imports
    if (usesAppContext || usesTheme || usesConfig || usesAuth || usesThemeState) {
      // Replace import from AppContext with spaces
      content = content.replace(
        /import\s+{\s*(.*)useAppContext(.*)\s*}\s+from\s+['"]@\/context\/AppContext['"]/g,
        (match, before, after) => {
          modified = true;
          return `import { useStore } from '@/store';`;
        },
      );

      content = content.replace(
        /import\s+{\s*(.*)useTheme(.*)\s*}\s+from\s+['"]@\/context\/AppContext['"]/g,
        (match, before, after) => {
          modified = true;
          return `import { useThemeStore } from '@/store/themeStore';`;
        },
      );

      content = content.replace(
        /import\s+{\s*(.*)useConfig(.*)\s*}\s+from\s+['"]@\/context\/AppContext['"]/g,
        (match, before, after) => {
          modified = true;
          return `import { useConfigStore } from '@/store/configStore';`;
        },
      );

      content = content.replace(
        /import\s+{\s*(.*)useAuth(.*)\s*}\s+from\s+['"]@\/context\/AppContext['"]/g,
        (match, before, after) => {
          modified = true;
          return `import { useAuthStore } from '@/store/authStore';`;
        },
      );

      content = content.replace(
        /import\s+{\s*(.*)ThemeState(.*)\s*}\s+from\s+['"]@\/context\/AppContext['"]/g,
        (match, before, after) => {
          modified = true;
          return `import { ThemeState } from '@/store/themeStore';`;
        },
      );

      // Handle multiple imports from AppContext with various spacing
      content = content.replace(
        /import\s+{\s*(.*?(?:useTheme|useConfig|useAuth|useAppContext|ThemeState).*?)\s*}\s+from\s+['"]@\/context\/AppContext['"]/g,
        (match, importString) => {
          modified = true;

          // Extract all imports, handling various spacing
          const imports = importString.split(',').map(i => i.trim());

          // Create new import statements
          const newImports = [];

          if (imports.some(i => i === 'useTheme' || i === 'ThemeState')) {
            const themeImports = [];
            if (imports.includes('useTheme')) themeImports.push('useThemeStore');
            if (imports.includes('ThemeState')) themeImports.push('ThemeState');
            newImports.push(`import { ${themeImports.join(', ')} } from '@/store/themeStore';`);
          }

          if (imports.includes('useConfig')) {
            newImports.push(`import { useConfigStore } from '@/store/configStore';`);
          }

          if (imports.includes('useAuth')) {
            newImports.push(`import { useAuthStore } from '@/store/authStore';`);
          }

          if (imports.includes('useAppContext')) {
            newImports.push(`import { useStore } from '@/store';`);
          }

          return newImports.join('\n');
        },
      );
    }

    // Replace useAppContext usage
    if (usesAppContext) {
      content = content.replace(
        /const\s+{\s*state\s*}\s+=\s+useAppContext\(\)/g,
        'const store = useStore()',
      );

      content = content.replace(/state\.theme/g, 'useThemeStore.getState()');

      content = content.replace(/state\.config/g, 'useConfigStore.getState()');

      content = content.replace(/state\.auth/g, 'useAuthStore.getState()');
    }

    // Replace useTheme usage
    if (usesTheme) {
      content = content.replace(
        /const\s+theme\s+=\s+useTheme\(\)/g,
        'const theme = useThemeStore()',
      );

      content = content.replace(/useTheme\(\)/g, 'useThemeStore()');
    }

    // Replace useConfig usage
    if (usesConfig) {
      content = content.replace(
        /const\s+{\s*(.*?)\s*}\s+=\s+useConfig\(\)/g,
        (match, destructured) => {
          return `const { ${destructured} } = useConfigStore()`;
        },
      );

      content = content.replace(
        /const\s+config\s+=\s+useConfig\(\)/g,
        'const config = useConfigStore()',
      );

      content = content.replace(/useConfig\(\)/g, 'useConfigStore()');
    }

    // Replace useAuth usage
    if (usesAuth) {
      content = content.replace(
        /const\s+{\s*(.*?)\s*}\s+=\s+useAuth\(\)/g,
        (match, destructured) => {
          return `const { ${destructured} } = useAuthStore()`;
        },
      );

      content = content.replace(/const\s+auth\s+=\s+useAuth\(\)/g, 'const auth = useAuthStore()');

      content = content.replace(/useAuth\(\)/g, 'useAuthStore()');
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Updated ${filePath}`);
    } else {
      console.log(`No changes needed for ${filePath}`);
    }

    return modified;
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error.message);
    return false;
  }
};

// Main function
const main = () => {
  console.log('Finding AppContext files...');
  const files = findAppContextFiles();
  console.log(`Found ${files.length} files using AppContext`);

  let updatedCount = 0;

  files.forEach(file => {
    const wasUpdated = processFile(file);
    if (wasUpdated) {
      updatedCount++;
    }
  });

  console.log(`\nMigration summary:`);
  console.log(`Total files scanned: ${files.length}`);
  console.log(`Files updated: ${updatedCount}`);
  console.log(
    `\nNote: You may need to manually update complex AppContext usages that the script couldn't handle automatically.`,
  );
  console.log(`Also remember to create the Zustand store files if they don't exist yet.`);
};

// Run the script
main();
