# Icomoon Font Setup

This directory contains the Icomoon font files for the icon system.

## Required Files

1. `icomoon.ttf` - The font file containing the icon glyphs
2. `selection.json` - Contains the mapping between icon names and unicode points

## How to Update

1. Go to [IcoMoon App](https://icomoon.io/app/#/select)
2. Upload your existing `selection.json` (if updating)
3. Select/design your icons
4. Download the font package
5. Extract and place the `selection.json` and `fonts/icomoon.ttf` files in this directory

## After Updating

After updating the font files, you need to:

1. Run `npx react-native link` to ensure the font is linked properly
2. Restart your bundler with `npx react-native start --reset-cache`
3. Rebuild your app with `npx react-native run-ios` and `npx react-native run-android`