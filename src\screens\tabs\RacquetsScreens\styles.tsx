import {Dimensions, StyleSheet} from 'react-native';

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    contentContainer: {
      flexGrow: 1,
      paddingHorizontal: 16,
      marginTop: 16,
      paddingBottom: 30,
      gap: 16,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },

    topBrandContainer: {
      width: Dimensions.get('screen').width - 32,
      height: 118,
      marginTop: 2,
    },
    topBrandImage: {
      width: '100%',
      height: '100%',
      borderRadius: 10,
      borderBottomRightRadius: 0,
    },
    sportsContent: {
      flexGrow: 1,
      marginTop: 4,
    },
    sportsCard: {
      marginRight: 20,
      height: 251,
      width: 148,
    },
    allBrandContainer: {
      marginTop: 10,
    },
    allBrandTitle: {
      marginBottom: -10,
    },
  });

export default styles;
