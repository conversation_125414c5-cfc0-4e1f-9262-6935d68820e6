import {Dropdown} from 'react-native-element-dropdown';
import React, {useState, useMemo} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {View, StyleSheet, StyleProp, ViewStyle} from 'react-native';

interface SportItem {
  label: string;
  value: string;
}

interface CustomDropdownProps {
  sportsData: SportItem[];
  initialValue?: string;
  value?: string;
  onChangeValue?: (value: string) => void;
  containerStyle?: StyleProp<ViewStyle>;
  mainStyles?: StyleProp<ViewStyle>;
  contentContainerStyle?: StyleProp<ViewStyle>;
}

const CustomDropdown = ({
  // Smaple Data Example
  sportsData = [
    {label: 'Tennis', value: 'Tennis'},
    {label: 'Basketball', value: 'Basketball'},
    {label: 'Soccer', value: 'Soccer'},
    {label: 'Volleyball', value: 'Volleyball'},
    {label: 'Baseball', value: 'Baseball'},
    {label: 'Football', value: 'Football'},
    {label: 'Golf', value: 'Golf'},
    {label: 'Cricket', value: 'Cricket'},
    {label: 'Hockey', value: 'Hockey'},
    {label: 'Rugby', value: 'Rugby'},
  ],
  initialValue = '',
  onChangeValue,
  value,
  containerStyle,
  mainStyles,
  contentContainerStyle,
}: CustomDropdownProps) => {
  const theme = useThemeStore();
  // const [selectedSport, setSelectedSport] = useState<string>(initialValue);

  const styles = useMemo(() => createStyles(theme), [theme]);

  const handleChange = (item: SportItem) => {
    // setSelectedSport(item.value);
    if (onChangeValue) {
      onChangeValue(item.value);
    }
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <Dropdown
        data={sportsData}
        labelField="label"
        valueField="value"
        value={value}
        onChange={handleChange}
        placeholder="Select sport"
        style={[styles.dropdown, mainStyles]}
        placeholderStyle={styles.placeholderText}
        selectedTextStyle={styles.selectedText}
        iconStyle={styles.icon}
        maxHeight={250}
        containerStyle={[styles.dropdownContainer, contentContainerStyle]}
        itemContainerStyle={[styles.itemContainer]}
        itemTextStyle={styles.itemText}
        activeColor={`${theme.colors.primary}20`}
      />
    </View>
  );
};

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      width: '100%',
    },
    dropdown: {
      borderWidth: 1,
      borderColor: theme.colors.primary,
      backgroundColor: 'transparent',
      borderRadius: 50,
      paddingHorizontal: 17,
      paddingVertical: 8,
    },
    placeholderText: {
      color: theme.colors.text,
    },
    selectedText: {
      color: theme.colors.text,
      fontWeight: 'bold',
    },
    icon: {
      tintColor: theme.colors.text,
    },
    dropdownContainer: {
      borderRadius: 20,
      borderWidth: 1,
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.background,
    },
    itemContainer: {
      borderBottomColor: theme.colors.border,
      borderRadius: 20,
      borderBottomWidth: 0.5,
    },
    itemText: {
      color: theme.colors.text,
      fontSize: theme.fontSize.medium,
    },
  });

export default React.memo(CustomDropdown);
