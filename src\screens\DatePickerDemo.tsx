import React, {useState} from 'react';
import {View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView} from 'react-native';
import { useThemeStore } from '@/store/themeStore';;
import {DateTimePicker} from '@components/index';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import {useNavigation} from '@react-navigation/native';

type DateRange = {
  startDate: Date | null;
  endDate: Date | null;
};

const DatePickerDemo: React.FC = () => {
  const navigation = useNavigation();
  const theme = useThemeStore();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      padding: 20,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 30,
    },
    backButton: {
      padding: 5,
      marginRight: 15,
    },
    title: {
      fontSize: 24,
      color: theme.colors.white,
      marginBottom: 0,
    },
    content: {
      flex: 1,
    },
    demoSection: {
      backgroundColor: theme.colors.white1,
      borderRadius: 12,
      padding: 20,
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.highlightColor,
      marginBottom: 15,
    },
    dateDisplay: {
      marginBottom: 15,
    },
    dateLabel: {
      fontSize: 16,
      color: theme.colors.text,
      marginBottom: 5,
      fontWeight: '500',
    },
    dateValue: {
      fontSize: 16,
      color: theme.colors.transparentWhite1,
    },
    optionsSection: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: 10,
      paddingTop: 15,
      borderTopWidth: 1,
      borderTopColor: theme.colors.semiTransparentWhite,
    },
    optionLabel: {
      fontSize: 16,
      color: theme.colors.text,
      fontWeight: '500',
    },
    toggleButton: {
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 20,
    },
    toggleActive: {
      backgroundColor: theme.colors.highlightColor,
    },
    toggleInactive: {
      backgroundColor: theme.colors.graniteGrey,
    },
    toggleText: {
      fontWeight: 'bold',
      color: theme.colors.black,
    },
    button: {
      marginTop: 20,
    },
  });

  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: null,
    endDate: null,
  });

  const handleBack = () => {
    navigation.goBack();
  };

  const handleConfirm = (range: DateRange) => {
    setDateRange(range);
    // hideDatePicker();
  };

  return (
    <SafeAreaView style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={handleBack}
            style={styles.backButton}
            hitSlop={{top: 20, bottom: 20, left: 20, right: 20}}>
            <FontAwesome name="arrow-left" size={20} color={theme.colors.white} />
          </TouchableOpacity>
          <Text style={[styles.title]}>Date Picker Demo</Text>
        </View>

        <DateTimePicker
          onClose={() => {}}
          onConfirm={handleConfirm}
          initialStartDate={dateRange.startDate}
          initialEndDate={dateRange.endDate}
          allowRangeSelection
          minDate={new Date()}
          title=""
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default DatePickerDemo;
