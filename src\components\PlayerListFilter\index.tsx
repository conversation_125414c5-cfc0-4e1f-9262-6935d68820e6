import React, {useRef, useEffect} from 'react';
import {View} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {CButton, RadioSelect} from '@/components';
import Typography from '@/components/Typography';
// import BottomSheet, {BottomSheetCompHandles} from '@/components/BottomSheet';
import BottomSheet, {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import CustomDateTimePicker from '../CustomDateTimePicker/CustomDateTimePicker';
import createStyles from './styles';
import useTranslation from '@/hooks/useTranslation';

interface PlayerListFilterProps {
  isVisible: boolean;
  onClose: () => void;
  onApply: (filters: {
    sport: string;
    dayType: string;
    showCertified: boolean;
    bookingData: {
      startDate: Date | null;
      endDate: Date | null;
      startTime: string | null;
      endTime: string | null;
    };
  }) => void;
  initialFilters?: {
    sport?: string;
    dayType?: string;
    showCertified?: boolean;
    bookingData?: {
      startDate: Date | null;
      endDate: Date | null;
      startTime: string | null;
      endTime: string | null;
    };
  };
}

const PlayerListFilter: React.FC<PlayerListFilterProps> = ({
  isVisible,
  onClose,
  onApply,
  initialFilters,
}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const bottomSheetRef = useRef<BottomSheet>(null);

  const {t} = useTranslation();

  // Filter options
  const [selectedSport, setSelectedSport] = React.useState<string>(initialFilters?.sport || '');
  const [selectedDayType, setSelectedDayType] = React.useState<string>(
    initialFilters?.dayType || '',
  );
  const [showCertified, setShowCertified] = React.useState<boolean>(
    initialFilters?.showCertified || false,
  );

  // Calendar state
  const [bookingData, setBookingData] = React.useState({
    startDate: initialFilters?.bookingData?.startDate || null,
    endDate: initialFilters?.bookingData?.endDate || null,
    startTime: initialFilters?.bookingData?.startTime || null,
    endTime: initialFilters?.bookingData?.endTime || null,
  });

  const sportOptions = [
    {value: 'tennis', label: t('filterResultsScreen.tennis')},
    {value: 'pickleball', label: t('filterResultsScreen.pickleball')},
    {value: 'padel', label: t('filterResultsScreen.padel')},
    {value: 'platform_tennis', label: t('filterResultsScreen.platformTennis')},
    {value: 'all', label: t('filterResultsScreen.all')},
  ];

  const dayOptions = [
    {value: 'weekdays', label: t('filterResultsScreen.weekdays')},
    {value: 'weekends', label: t('filterResultsScreen.weekends')},
  ];

  useEffect(() => {
    if (isVisible) {
      bottomSheetRef.current?.snapToIndex(0);
    } else {
      bottomSheetRef.current?.close();
    }
  }, [isVisible]);

  const handleApplyFilters = () => {
    onApply({
      sport: selectedSport,
      dayType: selectedDayType,
      showCertified,
      bookingData,
    });
    onClose();
  };

  const handleDateTimeSelect = (dateTimeData: any) => {
    setBookingData(prev => ({
      ...prev,
      dateTime: dateTimeData,
    }));
  };

  const handleDateChange = (dateRange: any) => {
    setBookingData(prev => ({
      ...prev,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    }));
  };

  const handleTimeChange = (timeData: any) => {
    setBookingData(prev => ({
      ...prev,
      startTime: timeData.startTime,
      endTime: timeData.endTime,
    }));
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      snapPoints={['100%']}
      enablePanDownToClose={true}
      index={-1}
      enableOverDrag={false}
      keyboardBehavior="interactive"
      keyboardBlurBehavior="restore"
      backgroundStyle={{backgroundColor: theme.colors.background}}
      handleIndicatorStyle={{backgroundColor: theme.colors.bottomSheetBackground}}
      onChange={index => {
        if (index === -1) {
          onClose();
        }
      }}
      animateOnMount>
      <BottomSheetScrollView contentContainerStyle={styles.modalContainer}>
        <View style={styles.bottomSheetHeader}>
          <Typography variant="coachTitle" style={styles.filterTitle}>
            {t('filterResultsScreen.title')}
          </Typography>
        </View>

        {/* Sport Selection */}
        <View>
          <Typography variant="subtitle" style={styles.sectionTitle}>
            {t('addMembersScreen.show')}
          </Typography>
          {sportOptions.map(option => (
            <RadioSelect
              key={option.value}
              label={option.label}
              selected={selectedSport === option.value}
              onPress={() => setSelectedSport(option.value)}
              containerStyle={styles.radioOption}
            />
          ))}
        </View>

        <View style={styles.divider} />

        {/* Days Selection */}
        <View>
          <Typography variant="body" style={styles.sectionTitle}>
            {t('filterResultsScreen.days')}
          </Typography>
          {dayOptions.map(option => (
            <RadioSelect
              key={option.value}
              label={option.label}
              selected={selectedDayType === option.value}
              onPress={() => setSelectedDayType(option.value)}
              containerStyle={styles.radioOption}
            />
          ))}
        </View>

        <View style={styles.divider} />

        {/* Date/Time Selection */}
        <View>
          <Typography variant="body" style={styles.sectionTitle}>
            {t('filterResultsScreen.dateTime')}
          </Typography>
          <CustomDateTimePicker
            onConfirm={handleDateTimeSelect}
            onClose={onClose}
            allowRangeSelection
            minDate={new Date()}
            title=""
            initialStartDate={bookingData.startDate}
            initialEndDate={bookingData.endDate}
            initialStartTime={bookingData.startTime}
            initialEndTime={bookingData.endTime}
            onDateChange={handleDateChange}
            onTimeChange={handleTimeChange}
            hidePlayerInput={true}
            hideActions={true}
          />
        </View>

        <View style={styles.divider} />

        {/* Certified Option */}
        <View>
          <RadioSelect
            label={t('filterResultsScreen.certified')}
            selected={showCertified}
            onPress={() => setShowCertified(!showCertified)}
            containerStyle={styles.radioOption}
          />
        </View>

        {/* Apply Filters Button */}
        <CButton
          title={t('filterResultsScreen.applyFilters')}
          onPress={handleApplyFilters}
          containerStyle={styles.applyButton}
          textStyle={styles.applyButtonText}
        />
      </BottomSheetScrollView>
    </BottomSheet>
  );
};

export default PlayerListFilter;
