import React from 'react';
import {View, StyleSheet, TouchableOpacity, FlatList} from 'react-native';
import Typography from '../Typography';
import {useThemeStore} from '@/store/themeStore';
import {useConfigStore} from '@/store';
import useTranslation from '@/hooks/useTranslation';
interface ShoppingCartSectionProps {
  onBack: () => void;
}

export const ShoppingCartSection: React.FC<ShoppingCartSectionProps> = ({onBack}) => {
  const theme = useThemeStore();
  const {bookingData, updateBookingData, setBookingStep} = useConfigStore();
  const {t} = useTranslation();
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 16,
    },
    backButton: {
      width: 30,
      height: 30,
      borderRadius: 15,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.Gainsboro,
    },
    backButtonText: {
      color: theme.colors.Gainsboro,
      fontSize: 18,
      fontWeight: 'bold',
    },
    title: {
      marginVertical: 16,
      fontSize: 24,
      fontWeight: 'bold',
    },
    list: {
      flex: 1,
    },
    cartItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 12,
      marginBottom: 8,
      borderRadius: 8,
      borderWidth: 1,
    },
    itemDetails: {
      flex: 1,
    },
    removeButton: {
      width: 30,
      height: 30,
      borderRadius: 15,
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: 8,
    },
    summary: {
      marginVertical: 16,
      alignItems: 'flex-end',
    },
    emptyCart: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 40,
    },
    checkoutButton: {
      padding: 14,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

  const handleCheckout = () => {
    setBookingStep('cart_view');
  };

  const handleRemoveItem = (itemId: string) => {
    if (bookingData?.items) {
      const updatedItems = bookingData.items.filter((item: any) => item.id !== itemId);
      updateBookingData({items: updatedItems});
    }
  };

  const renderCartItem = ({item}: {item: any}) => (
    <View style={[styles.cartItem, {borderColor: theme.colors.divider}]}>
      <View style={styles.itemDetails}>
        <Typography variant="body" color={theme.colors.text}>
          {item.name}
        </Typography>
        <Typography variant="caption" color={theme.colors.gray}>
          {item.description}
        </Typography>
        <Typography variant="subtitle" color={theme.colors.primary}>
          ${item.price}
        </Typography>
      </View>
      <TouchableOpacity
        style={[styles.removeButton, {backgroundColor: theme.colors.error}]}
        onPress={() => handleRemoveItem(item.id)}>
        <Typography color={theme.colors.white}>X</Typography>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={onBack}>
        <Typography style={styles.backButtonText}>{'\u2190'}</Typography>
      </TouchableOpacity>

      <Typography variant="title" color={theme.colors.text} style={styles.title}>
        {t('ShoppingCartSection.yourCart')}
      </Typography>

      {bookingData?.items && bookingData.items.length > 0 ? (
        <>
          <FlatList
            data={bookingData.items}
            renderItem={renderCartItem}
            keyExtractor={(item: any) => item.id}
            style={styles.list}
          />

          <View style={styles.summary}>
            <Typography variant="subtitle" color={theme.colors.text}>
              {t('ShoppingCartSection.total')}: $
              {bookingData.items
                .reduce((sum: number, item: any) => sum + parseFloat(item.price), 0)
                .toFixed(2)}
            </Typography>
          </View>

          <TouchableOpacity
            style={[styles.checkoutButton, {backgroundColor: theme.colors.primary}]}
            onPress={handleCheckout}>
            <Typography variant="button" color={theme.colors.white}>
              {t('ShoppingCartSection.proceedToCheckout')}
            </Typography>
          </TouchableOpacity>
        </>
      ) : (
        <View style={styles.emptyCart}>
          <Typography variant="subtitle" color={theme.colors.gray}>
            {t('ShoppingCartSection.emptyCart')}
          </Typography>
          <TouchableOpacity
            style={[styles.checkoutButton, {backgroundColor: theme.colors.primary, marginTop: 20}]}
            onPress={() => setBookingStep('search_parks')}>
            <Typography variant="button" color={theme.colors.white}>
              {t('ShoppingCartSection.findAPark')}
            </Typography>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
