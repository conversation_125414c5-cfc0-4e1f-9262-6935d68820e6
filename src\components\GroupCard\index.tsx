import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {createStyles} from './styles';
import Typography from '@/components/Typography';
import {Icon} from '@/components';

interface GroupCardProps {
  name: string;
  members: number;
  highlighted?: boolean;
  onPress?: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  locked?: boolean;
  location?: string;
  showJoinGroup?: boolean;
  showMore?: boolean;
  onMorePress?: () => void;
  onSelect?: () => void;
  isSelected?: boolean;
}

const GroupCard: React.FC<GroupCardProps> = ({
  name,
  members,
  highlighted,
  onPress,
  containerStyle,
  locked = false,
  location = '',
  showJoinGroup = false,
  showMore = false,
  onMorePress = () => {},
  onSelect = () => {},
  isSelected = false,
}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  return (
    <TouchableOpacity
      style={[styles.groupCard, containerStyle]}
      onPress={onPress}
      activeOpacity={0.8}>
      <View style={styles.groupIconContainer}>
        <Icon name="groupicon-1" size={33} color={theme.colors.black} />
      </View>
      <View style={styles.groupTextContainer}>
        <View style={styles.groupNameContainer}>
          <Typography
            variant="permissionTitle"
            style={{lineHeight: 20}}
            color={highlighted ? theme.colors.orange : theme.colors.white}>
            {name}
          </Typography>
          <Typography
            variant="gameCardDescription"
            color={highlighted ? theme.colors.orange : theme.colors.white}
            style={{marginTop: 2}}>
            {members} members
          </Typography>
          {location ? (
            <View style={styles.locationContainer}>
              <Icon name="location-pin" size={18} color={theme.colors.orange} />
              <Typography variant="gameCardDescription" color={theme.colors.white}>
                {location}
              </Typography>
            </View>
          ) : null}
        </View>
        {locked && (
          <TouchableOpacity style={styles.lockContainer} onPress={onSelect}>
            <Icon name={isSelected ? 'check' : 'lock-1'} size={18} color={theme.colors.black} />
          </TouchableOpacity>
        )}
        <View style={styles.buttonContainer}>
          {showJoinGroup && (
            <TouchableOpacity activeOpacity={0.7}>
              <Typography variant="permissionTitle" color={theme.colors.white}>
                {' '}
                Join group
              </Typography>
            </TouchableOpacity>
          )}
          {showMore && (
            <TouchableOpacity activeOpacity={0.7} onPress={onMorePress}>
              <Typography variant="permissionTitle" color={theme.colors.white} align="right">
                More{' '}
              </Typography>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default GroupCard;
