import React, {useState, useEffect} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Animated, Easing, ViewStyle} from 'react-native';

interface CustomToggleSwitchProps {
  isEnabled?: boolean;
  onToggle: (value: boolean) => void;
  activeText?: string;
  inactiveText?: string;
  trackColor?: {active: string; inactive: string};
  thumbColor?: {active: string; inactive: string};
  backgroundColorOn?: string;
  backgroundColorOff?: string;
  containerStyle?: ViewStyle;
  width?: number;
  height?: number;
  disabled?: boolean;
}

const CustomToggleSwitch: React.FC<CustomToggleSwitchProps> = ({
  isEnabled = false,
  onToggle,
  activeText = 'ON',
  inactiveText = 'OFF',
  trackColor = {active: '#FFFFFF', inactive: '#DFFF4F'},
  thumbColor = {active: '#000000', inactive: '#CCCCCC'},
  backgroundColorOn = '#FFFFFF',
  backgroundColorOff = '#616161',
  containerStyle,
  width = 60,
  height = 30,
  disabled = false,
}) => {
  const [isActive, setIsActive] = useState(isEnabled);
  const [animatedValue] = useState(new Animated.Value(isEnabled ? 1 : 0));

  // Update internal state when prop changes
  useEffect(() => {
    setIsActive(isEnabled);
    Animated.timing(animatedValue, {
      toValue: isEnabled ? 1 : 0,
      duration: 200,
      easing: Easing.bezier(0.68, -0.55, 0.265, 1.55), // Custom easing for more natural movement
      useNativeDriver: false,
    }).start();
  }, [isEnabled]);

  // Toggle switch handler
  const toggleSwitch = () => {
    if (disabled) return;

    const newValue = !isActive;
    setIsActive(newValue);

    Animated.timing(animatedValue, {
      toValue: newValue ? 1 : 0,
      duration: 200,
      easing: Easing.bezier(0.68, -0.55, 0.265, 1.55),
      useNativeDriver: false,
    }).start();

    if (onToggle) {
      onToggle(newValue);
    }
  };

  // Calculate dynamic styles based on animation value
  const thumbPosition = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [2, width - height + 2], // Adjust for proper thumb position
  });

  const trackBackgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [trackColor.inactive, trackColor.active],
  });

  const containerBackgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [backgroundColorOff, backgroundColorOn],
  });

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={toggleSwitch}
      disabled={disabled}
      style={[styles.container, containerStyle]}>
      <Animated.View
        style={[
          styles.trackContainer,
          {
            width,
            height,
            borderRadius: height / 2,
            backgroundColor: containerBackgroundColor,
          },
        ]}>
        {/* Text inside track */}
        <View style={styles.textContainer}>
          <Text style={[styles.text, {opacity: isActive ? 1 : 0, left: 8}]}>{activeText}</Text>
          <Text style={[styles.text1, {opacity: isActive ? 0 : 1, right: 8}]}>{inactiveText}</Text>
        </View>

        {/* Thumb */}
        <Animated.View
          style={[
            styles.thumb,
            {
              width: height - 8,
              height: height - 8,
              borderRadius: (height - 4) / 2,
              backgroundColor: isActive ? thumbColor.active : thumbColor.inactive,
              transform: [{translateX: thumbPosition}],
            },
          ]}
        />
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  trackContainer: {
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#DDDDDD',
  },
  thumb: {
    position: 'absolute',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 5,
  },
  text: {
    position: 'absolute',
    fontSize: 12,
    fontWeight: '600',
    color: '#707070',
  },
  text1: {
    position: 'absolute',
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default CustomToggleSwitch;

// Example usage
{
  /* <CustomToggleSwitch
isEnabled={isEnabled}
onToggle={toggleSwitch}
activeText="ON"
inactiveText="OFF"
width={70}
height={34}
trackColor={{ active: '#FFFFFF', inactive: '#FFFFFF' }}
thumbColor={{ active: '#000000', inactive: '#CCCCCC' }}
backgroundColorOn="#FFFFFF"
backgroundColorOff="#FFFFFF"
/> */
}
