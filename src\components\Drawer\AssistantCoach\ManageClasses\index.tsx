import CoachWrapper from '@/components/CoachWrapper';
import {useThemeStore} from '@/store/themeStore';
import React, {useRef} from 'react';
import {
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {createStyles} from './styles';
import {Controller, SubmitHandler, useForm} from 'react-hook-form';
import {CInput, Icon} from '@/components';
import {useNavigation} from '@react-navigation/native';
import Typography from '@/components/Typography';
import * as yup from 'yup';
import {yupResolver} from '@hookform/resolvers/yup';
import {FONT_SIZE} from '@/utils/fonts';
import CustomToggleSwitch from '@/components/CustomToggleSwitch';
import ClassCard from '@/components/ClassCard';
import useTranslation from '@/hooks/useTranslation';

interface FormData {
  className: string;
  classDescription: string;
  NumberOfStudents: string;
  isPrivate: boolean;
}

const scheduledClasses = [
  {
    id: 1,
    type: 'Beginners',
    startTime: '9:00am',
    endTime: '10:00am',
    frequency: 'Every Friday',
    numberOfOpenings: 10,
  },
  {
    id: 2,
    type: 'Advanced',
    startTime: '10:00am',
    endTime: '11:00am',
    frequency: 'Every Sunday',
    numberOfOpenings: 10,
  },
  {
    id: 3,
    type: 'Intermediate',
    startTime: '11:00am',
    endTime: '12:00pm',
    frequency: 'Every Monday',
    numberOfOpenings: 10,
  },
  {
    id: 3,
    type: 'Intermediate',
    startTime: '11:00am',
    endTime: '12:00pm',
    frequency: 'Every Monday',
    numberOfOpenings: 10,
  },
];

const ManageClasses = (props: any) => {
  const {onClose} = props;
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const navigation = useNavigation();

  const classNameInputRef = useRef<TextInput>(null);
  const classDescriptionInputRef = useRef<TextInput>(null);
  const numberOfStudentsInputRef = useRef<TextInput>(null);

  const {t} = useTranslation();

  const schema = yup.object({
    className: yup.string().required('Class name is required'),
    classDescription: yup.string().required('class description is required'),
    NumberOfStudents: yup.number().required('Number of students is required'),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm<FormData>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      className: '',
      classDescription: '',
      NumberOfStudents: '',
    },
  });

  const onSubmit: SubmitHandler<FormData> = data => {
    console.log('Form data:', data);
  };

  const renderItem = ({item}: {item: any}) => {
    return (
      <View style={styles.classCardContainer}>
        <ClassCard
          type={item.type}
          startTime={item.startTime}
          endTime={item.endTime}
          frequency={item.frequency}
          numberOfOpenings={item.numberOfOpenings}
          editable={true}
        />
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardAvoidingView}>
      <ScrollView
        nestedScrollEnabled
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}>
        <View style={styles.headerContainer}>
          <TouchableOpacity onPress={onClose}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
          <Typography variant="invitePlayersTitle" color={theme.colors.white}>
            {t('manageClassesScreen.title')}
          </Typography>
        </View>
        <View style={styles.titleContainer}>
          <Typography variant="invitePlayersTitle" color={theme.colors.white}>
            {t('manageClassesScreen.unscheduled')}
          </Typography>
          <TouchableOpacity activeOpacity={0.8} onPress={handleSubmit(onSubmit)}>
            <Typography
              variant="invitePlayersTitle"
              color={theme.colors.white}
              style={{fontSize: FONT_SIZE.xxl}}>
              {t('common.save')}
            </Typography>
          </TouchableOpacity>
        </View>
        <View style={styles.inputContainer}>
          <Controller
            control={control}
            name="className"
            render={({field: {onChange, onBlur, value}}) => (
              <CInput
                label={t('manageClassesScreen.className')}
                showLabel={true}
                labelStyle={styles.label}
                placeholder={t('manageClassesScreen.classNamePlaceholder')}
                placeholderTextColor={theme.colors.placeholder}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                hasError={!!errors.className}
                error={errors.className?.message}
                inputStyle={styles.input}
                ref={classNameInputRef}
                returnKeyType="next"
                onSubmitEditing={() => classNameInputRef.current?.focus()}
                blurOnSubmit={false}
                cursorColor={theme.colors.black}
              />
            )}
          />
        </View>

        <View style={styles.inputContainer}>
          <Controller
            control={control}
            name="classDescription"
            render={({field: {onChange, onBlur, value}}) => (
              <View style={styles.birthYearWrapper}>
                <CInput
                  multiline={true}
                  style={styles.textArea}
                  numberOfLines={10}
                  label={t('manageClassesScreen.classDescription')}
                  showLabel={true}
                  labelStyle={styles.label}
                  placeholder={t('manageClassesScreen.classDescriptionPlaceholder')}
                  placeholderTextColor={theme.colors.placeholder}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  hasError={!!errors.classDescription}
                  error={errors.classDescription?.message}
                  inputStyle={{...styles.input, textAlignVertical: 'top'}}
                  containerStyle={{flex: 1}}
                  ref={classDescriptionInputRef}
                  returnKeyType="next"
                  onSubmitEditing={() => numberOfStudentsInputRef.current?.focus()}
                  blurOnSubmit={false}
                  cursorColor={theme.colors.black}
                />
              </View>
            )}
          />
          <TouchableOpacity activeOpacity={0.8} style={styles.selectDateContainer}>
            <Typography variant="dateTimeFrequency" color={theme.colors.white}>
              Select date, time and frequency
            </Typography>
          </TouchableOpacity>
        </View>

        <View style={styles.inputContainer}>
          <Controller
            control={control}
            name="NumberOfStudents"
            render={({field: {onChange, onBlur, value}}) => (
              <CInput
                label={t('manageClassesScreen.maximumStudents')}
                showLabel={true}
                labelStyle={styles.label}
                placeholder={t('manageClassesScreen.numberPlaceholder')}
                placeholderTextColor={theme.colors.placeholder}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                hasError={!!errors.NumberOfStudents}
                error={errors.NumberOfStudents?.message}
                keyboardType="numeric"
                autoCapitalize="none"
                inputStyle={styles.input}
                ref={numberOfStudentsInputRef}
                returnKeyType="done"
                blurOnSubmit={false}
                cursorColor={theme.colors.black}
              />
            )}
          />
        </View>

        <View style={styles.rowBetween}>
          <Typography variant="pushNotificationTitle" color={theme.colors.white}>
            {t('manageClassesScreen.private')}
          </Typography>
          <Controller
            control={control}
            name="isPrivate"
            render={({field: {value, onChange}}) => (
              <CustomToggleSwitch
                isEnabled={value}
                onToggle={onChange}
                activeText={t('common.yes')}
                inactiveText={t('common.no')}
                trackColor={{active: theme.colors.white, inactive: theme.colors.activeColor}}
                thumbColor={{
                  active: theme.colors.activeColor,
                  inactive: theme.colors.activeColor,
                }}
                backgroundColorOn={theme.colors.white}
                backgroundColorOff={theme.colors.dimGray}
              />
            )}
          />
        </View>
        <View style={styles.divider} />
        <View style={[styles.titleContainer, {marginTop: 10, marginBottom: 10}]}>
          <Typography variant="title" color={theme.colors.white}>
            {t('manageClassesScreen.scheduledClasses')}
          </Typography>
          <TouchableOpacity activeOpacity={0.7} style={styles.createGroupContainer}>
            <Icon name="create-group" size={22} color={theme.colors.white} />
          </TouchableOpacity>
        </View>
        <View style={styles.flatListContainer}>
          <FlatList
            data={scheduledClasses}
            renderItem={renderItem}
            keyExtractor={item => item.id.toString()}
            contentContainerStyle={styles.flatListContent}
            onEndReached={() => console.log('onEndReached')}
            onEndReachedThreshold={0.5}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default ManageClasses;
