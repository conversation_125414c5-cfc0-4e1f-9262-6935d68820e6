import React, {useRef, useState, useEffect} from 'react';
import {
  View,
  StyleSheet,
  ImageBackground,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Keyboard,
  ActivityIndicator,
} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {CButton, Header, Icon, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import {Images} from '@/config/images';
import CInput from '@/components/CInput';
import {useForm, Controller, SubmitHandler} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {useAuthStore} from '@/store';
import {useCheckDisplayName, useUpdateUserProfile} from '@/hooks/queries/useUser';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {debounce, isBoolean} from 'lodash-es';
import {toaster} from '@/utils/commonFunctions';
import {useRandomName} from '@/hooks/queries/useUsers';
import {useQueryClient} from '@tanstack/react-query';
import ModalYearPicker from '@/components/ModalYearPicker';
import useTranslation from '@/hooks/useTranslation';

// Form data type explicitly defined first
interface FormData {
  fullName: string;
  email: string;
  birthYear: string;
  profession?: string;
  primarySport: string;
  location: string;
  myCourt: string;
  yearsPlaying: string;
  skillLevel: string;
  favoriteBrand?: string;
  memberships?: string;
  affiliations?: string;
  randomName: string;
}

// Stats data for profile
interface StatItem {
  value: string;
  label: string;
}

type RootStackParamList = {
  Notifications: undefined;
  ProfileQrScreen: undefined;
  // Add other screens as needed
};

type NavigationProp = StackNavigationProp<RootStackParamList>;

const EditProfileScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation<NavigationProp>();

  const fullNameInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const birthYearInputRef = useRef<TextInput>(null);
  const professionInputRef = useRef<TextInput>(null);
  const primarySportInputRef = useRef<TextInput>(null);
  const locationInputRef = useRef<TextInput>(null);
  const myCourtInputRef = useRef<TextInput>(null);
  const yearsPlayingInputRef = useRef<TextInput>(null);
  const skillLevelInputRef = useRef<TextInput>(null);
  const favoriteBrandInputRef = useRef<TextInput>(null);
  const membershipsInputRef = useRef<TextInput>(null);
  const affiliationsInputRef = useRef<TextInput>(null);
  const [showYearModal, setShowYearModal] = useState(false);

  const randomNameApi = useRandomName();
  const queryClient = useQueryClient();
  const cachedData = queryClient.getQueryData(['randomName']);

  const {t} = useTranslation();

  // Profile stats data
  const statsData: StatItem[] = [
    {value: '13', label: t('editProfileScreen.matches')},
    {value: '20', label: t('editProfileScreen.following')},
    {value: '12', label: t('editProfileScreen.followers')},
  ];

  const updateProfileMutation = useUpdateUserProfile();
  const {user, login, isApiStatus} = useAuthStore();

  const [displayName, setDisplayName] = useState('');
  const [ageStatus, setAgeStatus] = useState<string>('');
  const {data: nameCheck, isLoading: isCheckingName} = useCheckDisplayName(displayName);

  // Form validation schema
  const schema = yup.object({
    fullName: yup.string().required(t('editProfileScreen.fullNameRequired')),
    email: yup
      .string()
      .email(t('editProfileScreen.invalidEmail'))
      .required(t('editProfileScreen.emailRequired')),
    birthYear: yup
      .string()
      .required(t('editProfileScreen.birthYearRequired'))
      .matches(/^[0-9]{4}$/, t('editProfileScreen.invalidBirthYear'))
      .test('is-valid-year', t('editProfileScreen.invalidBirthYear'), value => {
        const numValue = parseInt(value, 10);
        const currentYear = new Date().getFullYear();
        if (numValue > currentYear) {
          return false;
        }
        return !isNaN(numValue) && numValue >= currentYear - 60 && numValue <= currentYear;
      }),
    profession: yup
      .string()
      .nullable()
      .optional()
      .matches(/^[A-Za-z\s]*$/, t('editProfileScreen.invalidProfession')),
    primarySport: yup
      .string()
      .required(t('editProfileScreen.primarySportRequired'))
      .matches(/^[A-Za-z\s]*$/, t('editProfileScreen.invalidPrimarySport')),
    location: yup
      .string()
      .required(t('editProfileScreen.locationRequired'))
      .matches(/^[A-Za-z\s]*$/, t('editProfileScreen.invalidLocation')),
    myCourt: yup
      .string()
      .required(t('editProfileScreen.myCourtRequired'))
      .matches(/^[A-Za-z\s]*$/, t('editProfileScreen.invalidMyCourt')),
    yearsPlaying: yup
      .string()
      .required(t('editProfileScreen.yearsPlayingRequired'))
      .matches(/^[0-9]+$/, t('editProfileScreen.invalidYearsPlaying'))
      .test('is-positive', t('editProfileScreen.negativeYearsPlaying'), value => {
        const numValue = parseInt(value, 10);
        return !isNaN(numValue) && numValue > 0;
      }),
    skillLevel: yup.string().required(t('editProfileScreen.skillLevelRequired')),
    favoriteBrand: yup.string().optional(),
    memberships: yup.string().optional(),
    affiliations: yup.string().optional(),
    randomName: yup.string().optional(),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
    watch,
    setValue,
  } = useForm<FormData>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      fullName: user?.display_name || '',
      email: user?.email || '',
      birthYear: String(user?.birthyear) || '',
      profession: user?.profession || '',
      primarySport: user?.primary_sport_reference || '',
      location: user?.location || '',
      myCourt: user?.court_name || '',
      yearsPlaying: String(user?.years_playing) || '',
      skillLevel: user?.skill_level || '',
      favoriteBrand: user?.favorite_brand || '',
      memberships: user?.memberships || '',
      affiliations: user?.affiliations || '',
      randomName: '',
    },
  });

  // Get the current birth year value
  const birthYearValue = watch('birthYear');

  // Determine if user is adult or minor based on birth year
  useEffect(() => {
    if (birthYearValue && /^\d{4}$/.test(birthYearValue)) {
      const birthYear = parseInt(birthYearValue, 10);
      const currentYear = new Date().getFullYear();
      if (birthYear > currentYear) {
        setAgeStatus('');
      } else if (birthYear >= currentYear - 60 && birthYear <= currentYear) {
        const age = currentYear - birthYear;
        setAgeStatus(age >= 18 ? 'ADULT' : 'JUNIOR');
      } else {
        setAgeStatus('');
      }
    } else {
      setAgeStatus('');
    }
  }, [birthYearValue]);

  useEffect(() => {
    handleRandomName();
  }, []);

  const onSubmit: SubmitHandler<FormData> = data => {
    const updateData = {
      name: data.fullName,
      birthyear: data.birthYear,
      profession: data.profession,
      primary_sport_reference: data.primarySport,
      location: data.location,
      court_name: data.myCourt,
      years_playing: data.yearsPlaying,
      skill_level: data.skillLevel,
      favorite_brand: data.favoriteBrand,
      memberships: data.memberships,
      affiliations: data.affiliations,
    };

    updateProfileMutation.mutate(updateData, {
      onSuccess: data => {
        login(data.data);
        navigation.goBack();
      },
      onError: error => {
        toaster('error', error.message || 'Failed to update profile', 'top');
      },
    });
  };

  const handleNavigate = () => {
    navigation.navigate('ProfileQrScreen');
  };

  const handleRandomName = () => {
    const {userType, fitnessLevel, birthYear} = control._formValues;
    const data =
      userType || fitnessLevel || birthYear
        ? {
            ...(userType && {user_type: userType}),
            ...(fitnessLevel && {fitness_level: fitnessLevel}),
            ...(birthYear && {birthyear: birthYear}),
          }
        : {};
    if (isApiStatus) {
      randomNameApi.mutate(data as any, {
        onSuccess: response => {
          if (!response?.status) {
            toaster('error', response.message, 'top');
          }
        },
        onError: error => {
          toaster('error', error.message, 'top');
        },
      });
    } else {
      toaster('success', 'Random name generation API called', 'top');
    }
  };

  return (
    <ImageBackground source={Images.gradientBg} style={styles(theme).backgroundImage}>
      <SafeAreaView style={styles(theme).container}>
        <Header
          pageTitle={t('editProfileScreen.title')}
          showBack={false}
          leftComponent={
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Icon name="Left-chevron" size={24} color={theme.colors.white} />
            </TouchableOpacity>
          }
          rightComponent={
            <View style={styles(theme).rightComponentContainer}>
              <TouchableOpacity onPress={() => navigation.navigate('Notifications')}>
                <Icon name="notification" size={26} color={theme.colors.activeColor} />
              </TouchableOpacity>
              <TouchableOpacity>
                <Icon name="chat" size={26} color={theme.colors.activeColor} />
              </TouchableOpacity>
            </View>
          }
          backgroundColor="transparent"
        />
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles(theme).keyboardAvoidingView}>
          <ScrollView
            contentContainerStyle={styles(theme).scrollContainer}
            showsVerticalScrollIndicator={false}>
            {/* Profile Section */}
            <View style={styles(theme).profileSection}>
              {/* Profile Image */}
              <View style={styles(theme).profileImageContainer}>
                <View style={styles(theme).profileImage}>
                  <Typography variant="tagTitle" color={theme.colors.black}>
                    {t('editProfileScreen.addPhoto')}
                  </Typography>
                </View>
                <TouchableOpacity style={styles(theme).qrCodeContainer} onPress={handleNavigate}>
                  <Icon name="QR" size={30} color={theme.colors.black} />
                </TouchableOpacity>
              </View>
              <View style={styles(theme).nameContainer}>
                <View style={styles(theme).profileStatsContainer}>
                  <Typography variant="title" color={theme.colors.primary}>
                    {user?.name || 'Alex'}
                  </Typography>
                  <Typography
                    variant="bodyMedium"
                    color={theme.colors.primary}
                    style={styles(theme).emailText}>
                    {user?.email || '<EMAIL>'}
                  </Typography>
                </View>
                <View style={styles(theme).ballContainer}>
                  <Icon name="badge" size={26} color={theme.colors.activeColor} />
                </View>
              </View>

              {/* Profile Stats */}
              <View style={styles(theme).statsContainer}>
                {statsData.map((stat, index) => (
                  <React.Fragment key={stat.label}>
                    <View style={styles(theme).statItem}>
                      <Typography variant="subtitle2" color={theme.colors.white}>
                        {stat.value}
                      </Typography>
                      <Typography
                        variant="parkTitle"
                        color={theme.colors.primary}
                        style={styles(theme).statLabel}>
                        {stat.label}
                      </Typography>
                    </View>
                    {index < statsData.length - 1 && <View style={styles(theme).statDivider} />}
                  </React.Fragment>
                ))}
              </View>
            </View>

            {/* Form */}
            <View style={styles(theme).formContainer}>
              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="fullName"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.displayName')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.fullNamePlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={text => {
                        onChange(text);
                        const debouncedCheck = debounce((name: string) => {
                          setDisplayName(name);
                        }, 1000);
                        debouncedCheck(text);
                      }}
                      onBlur={onBlur}
                      hasError={
                        !!errors.fullName ||
                        (value.length > 0 && isBoolean(nameCheck?.status) && !nameCheck?.status)
                      }
                      error={
                        errors.fullName?.message ||
                        (value.length > 0 && isBoolean(nameCheck?.status) && !nameCheck?.status
                          ? nameCheck?.message
                          : '')
                      }
                      inputStyle={styles(theme).input}
                      ref={fullNameInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => emailInputRef.current?.focus()}
                      blurOnSubmit={false}
                      editable={!isCheckingName}
                      isLoading={isCheckingName}
                    />
                  )}
                />
              </View>

              {/* <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="email"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label="Email"
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder="Enter your email address"
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.email}
                      error={errors.email?.message}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      inputStyle={styles(theme).input}
                      ref={emailInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => birthYearInputRef.current?.focus()}
                      blurOnSubmit={false}
                      editable={false}
                    />
                  )}
                />
              </View> */}

              <View style={[styles(theme).inputContainer, {marginTop: -5, marginBottom: 25}]}>
                <View style={styles(theme).randomName}>
                  <Typography variant="bodyMedium" style={styles(theme).radioTitle}>
                    {t('editProfileScreen.randomName')}
                    {randomNameApi?.isPending && (
                      <ActivityIndicator
                        size={18}
                        style={{paddingLeft: 10}}
                        color={theme.colors.white}
                      />
                    )}
                  </Typography>
                  <TouchableOpacity
                    activeOpacity={0.7}
                    onPress={() => !randomNameApi?.isPending && handleRandomName()}>
                    <Ionicons name="reload" color={theme.colors.white} size={18} />
                  </TouchableOpacity>
                </View>
                <Controller
                  control={control}
                  name="randomName"
                  render={({field: {onChange, value}}) => (
                    <View style={[styles(theme).radioContainer]}>
                      {cachedData?.data?.map((option, index) => (
                        <CButton
                          containerStyle={styles(theme).fitnessBtn}
                          textStyle={styles(theme).fitnessText}
                          key={index}
                          title={option}
                          variant="primary"
                          onPress={() => {
                            Keyboard.dismiss();
                            setDisplayName(option);
                            setValue('fullName', option, {
                              shouldValidate: true,
                            });
                            onChange(option); // Call onChange to update the form value
                          }}
                        />
                      ))}
                    </View>
                  )}
                />
                {/* {errors.randomName && (
                  <Typography variant="body" style={styles(theme).errorText}>
                    {errors.randomName.message}
                  </Typography>
                )} */}
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="birthYear"
                  render={({field: {onChange, onBlur, value}}) => (
                    <View>
                      <TouchableOpacity activeOpacity={1} onPress={() => setShowYearModal(true)}>
                        <View pointerEvents="none">
                          <CInput
                            label={t('editProfileScreen.birthYear')}
                            showLabel={true}
                            labelStyle={styles(theme).label}
                            placeholder="YYYY"
                            placeholderTextColor={theme.colors.placeholder}
                            value={value}
                            onChangeText={text => {
                              onChange(text);
                              if (text.length === 4) {
                                const birthYear = parseInt(text, 10);
                                const currentYear = new Date().getFullYear();
                                if (birthYear > currentYear) {
                                  setAgeStatus('');
                                  setValue('birthYear', text, {
                                    shouldValidate: true,
                                  });
                                } else if (
                                  birthYear >= currentYear - 60 &&
                                  birthYear <= currentYear
                                ) {
                                  const age = currentYear - birthYear;
                                  setAgeStatus(age >= 18 ? 'ADULT' : 'JUNIOR');
                                  setValue('birthYear', text, {
                                    shouldValidate: true,
                                  });
                                } else {
                                  setAgeStatus('');
                                  setValue('birthYear', text, {
                                    shouldValidate: true,
                                  });
                                }
                              } else {
                                setAgeStatus('');
                                setValue('birthYear', text, {
                                  shouldValidate: true,
                                });
                              }
                            }}
                            onBlur={onBlur}
                            hasError={!!errors.birthYear}
                            error={errors.birthYear?.message}
                            keyboardType="numeric"
                            maxLength={4}
                            inputStyle={styles(theme).input}
                            containerStyle={{flex: 1}}
                            ref={birthYearInputRef}
                            returnKeyType="next"
                            onSubmitEditing={() => professionInputRef.current?.focus()}
                            blurOnSubmit={false}
                          />
                          {ageStatus && (
                            <View style={styles(theme).ageTag}>
                              <Typography variant="bodyMedium" style={styles(theme).ageTagText}>
                                {ageStatus}
                              </Typography>
                            </View>
                          )}
                        </View>
                      </TouchableOpacity>
                    </View>
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="profession"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.profession')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.professionPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value || ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      inputStyle={styles(theme).input}
                      ref={professionInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => primarySportInputRef.current?.focus()}
                      blurOnSubmit={false}
                      hasError={!!errors.profession}
                      error={errors.profession?.message}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="primarySport"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.primarySport')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.primarySportPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.primarySport}
                      error={errors.primarySport?.message}
                      inputStyle={styles(theme).input}
                      ref={primarySportInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => locationInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="location"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.location')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.locationPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.location}
                      error={errors.location?.message}
                      inputStyle={styles(theme).input}
                      ref={locationInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => myCourtInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="myCourt"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.myCourt')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.myCourtPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.myCourt}
                      error={errors.myCourt?.message}
                      inputStyle={styles(theme).input}
                      ref={myCourtInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => yearsPlayingInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="yearsPlaying"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.yearsPlaying')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.yearsPlayingPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      maxLength={2}
                      hasError={!!errors.yearsPlaying}
                      error={errors.yearsPlaying?.message}
                      keyboardType="numeric"
                      inputStyle={styles(theme).input}
                      ref={yearsPlayingInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => skillLevelInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="skillLevel"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.skillLevel')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.skillLevelPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.skillLevel}
                      error={errors.skillLevel?.message}
                      inputStyle={styles(theme).input}
                      ref={skillLevelInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => favoriteBrandInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="favoriteBrand"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.favoriteBrand')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.favoriteBrandPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value || ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      inputStyle={styles(theme).input}
                      ref={favoriteBrandInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => membershipsInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="memberships"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.memberships')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.membershipsPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value || ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      inputStyle={styles(theme).input}
                      ref={membershipsInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => affiliationsInputRef.current?.focus()}
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <View style={styles(theme).inputContainer}>
                <Controller
                  control={control}
                  name="affiliations"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('editProfileScreen.affiliations')}
                      showLabel={true}
                      labelStyle={styles(theme).label}
                      placeholder={t('editProfileScreen.affiliationsPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value || ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      inputStyle={styles(theme).input}
                      ref={affiliationsInputRef}
                      returnKeyType="done"
                      blurOnSubmit={false}
                    />
                  )}
                />
              </View>

              <CButton
                title={t('editProfileScreen.completeProfile')}
                variant="primary"
                onPress={handleSubmit(onSubmit)}
                loading={updateProfileMutation.isPending}
                containerStyle={styles(theme).buttonContainer}
              />
            </View>
          </ScrollView>
          <ModalYearPicker
            visible={showYearModal}
            onClose={() => setShowYearModal(false)}
            title={t('editProfileScreen.selectYear')}
            initialYear={
              watch('birthYear') ? parseInt(watch('birthYear'), 10) : new Date().getFullYear()
            }
            onYearSelected={year => {
              setValue('birthYear', String(year), {
                shouldValidate: true,
              });
              // Update age status when year is selected
              const currentYear = new Date().getFullYear();
              if (year >= 1920 && year <= currentYear) {
                const age = currentYear - year;
                setAgeStatus(
                  age >= 18 ? t('editProfileScreen.adult') : t('editProfileScreen.junior'),
                );
              } else {
                setAgeStatus('');
              }
              setShowYearModal(false);
            }}
          />
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    container: {
      flex: 1,
      paddingHorizontal: 16,
    },
    nameContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 10,
      marginBottom: 30,
    },
    profileStatsContainer: {
      alignItems: 'center',
    },
    formContainer: {
      flex: 1,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingBottom: 40,
      paddingHorizontal: 16,
    },
    label: {
      color: theme.colors.white,
      fontSize: 18,
      fontWeight: '400',
    },
    profileSection: {
      alignItems: 'center',
      paddingTop: 5,
      paddingBottom: 20,
    },
    profileImageContainer: {
      position: 'relative',
      marginBottom: 16,
    },
    profileImage: {
      width: 142,
      height: 142,
      borderRadius: 100,
      backgroundColor: theme.colors.activeColor,
      justifyContent: 'center',
      alignItems: 'center',
    },

    fitnessBtn: {
      width: '48%',
      paddingHorizontal: 10,
      paddingVertical: 10,
    },

    fitnessText: {
      fontSize: theme.fontSize.medium,
      fontWeight: '400',
    },

    qrCodeContainer: {
      position: 'absolute',
      bottom: -10,
      right: -10,
      backgroundColor: theme.colors.white,
      borderRadius: 100,
      padding: 6,
      width: 56,
      height: 56,
      borderWidth: 3,
      borderColor: theme.colors.black,
      justifyContent: 'center',
      alignItems: 'center',
    },
    emailText: {
      marginTop: -10,
    },
    statsContainer: {
      flexDirection: 'row',
      backgroundColor: theme.colors.TranslucentWhite || 'rgba(255, 255, 255, 0.15)',
      borderRadius: 20,
      paddingVertical: 20,
    },
    statItem: {
      alignItems: 'center',
      flex: 1,
    },
    statDivider: {
      width: 1,
      height: '100%',
      backgroundColor: theme.colors.white,
    },
    inputContainer: {
      marginBottom: 10,
    },
    input: {
      backgroundColor: theme.colors.TranslucentWhite || 'rgba(255, 255, 255, 0.15)',
      color: theme.colors.white,
      borderWidth: 0,
      borderRadius: 18,
      height: 66,
      fontWeight: '400',
      fontSize: 18,
    },
    birthYearWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      position: 'relative',
    },
    ageTag: {
      position: 'absolute',
      right: 20,
      top: 51,
    },
    ageTagText: {
      color: theme.colors.white,
      fontWeight: '700',
      fontSize: 14,
    },
    ballContainer: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      right: -45,
      borderRadius: 100,
      padding: 6,
      justifyContent: 'center',
      alignItems: 'center',
    },
    rightComponentContainer: {
      flexDirection: 'row',
      gap: 20,
    },
    statLabel: {
      fontSize: 16,
    },
    buttonContainer: {
      marginTop: 20,
    },
    randomName: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    radioTitle: {
      color: theme.colors.text,
    },
    fitnessRadio: {
      width: 150,
    },
    radioContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginVertical: 10,
      gap: 10,
    },
    errorText: {
      color: theme.colors.coralRed,
      marginTop: 8,
      fontSize: theme.fontSize.small,
    },
  });

export default EditProfileScreen;
