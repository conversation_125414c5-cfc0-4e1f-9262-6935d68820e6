import React, {useRef, useEffect} from 'react';
import {
  NavigationContainer,
  NavigationContainerRef,
  NavigationState,
  PartialState,
} from '@react-navigation/native';
import * as Sentry from '@sentry/react-native';
import {setNavigationContext} from '@/utils/sentryUtils';

interface SentryNavigationContainerProps {
  children: React.ReactNode;
  ref?: React.RefObject<NavigationContainerRef<any>>;
  onReady?: () => void;
}

/**
 * A wrapper around NavigationContainer that adds Sentry tracking
 * for navigation events and screen views.
 */
const SentryNavigationContainer: React.FC<SentryNavigationContainerProps> = ({
  children,
  ref,
  onReady,
  ...rest
}) => {
  const navigationRef = useRef<NavigationContainerRef<any>>(null);
  const routeNameRef = useRef<string | undefined>(undefined);

  // Get the current route name from navigation state
  const getActiveRouteName = (
    state: NavigationState | PartialState<NavigationState> | undefined,
  ): string | undefined => {
    if (!state) return undefined;

    const route = state.routes[state.index || 0];

    // Dive into nested navigators
    if (route.state) {
      return getActiveRouteName(route.state);
    }

    return route.name;
  };

  useEffect(() => {
    if (ref && 'current' in ref) {
      // If a ref was passed, we need to forward it
      // Using type assertion to avoid the deprecated MutableRefObject
      (ref as {current: NavigationContainerRef<any> | null}).current = navigationRef.current;
    }
  }, [ref]);

  // We can't use NavigationErrorBoundary here because it would create a circular dependency
  // Instead, we'll use a simple try/catch block to handle errors
  try {
    return (
      <NavigationContainer
        ref={navigationRef}
        onReady={() => {
          try {
            routeNameRef.current = getActiveRouteName(navigationRef.current?.getRootState());
            if (onReady) {
              onReady();
            }
          } catch (error) {
            console.error('Error in SentryNavigationContainer onReady:', error);
            Sentry.captureException(error);
          }
        }}
        onStateChange={state => {
          try {
            const previousRouteName = routeNameRef.current;
            const currentRouteName = getActiveRouteName(state);

            if (previousRouteName !== currentRouteName && currentRouteName) {
              // Track screen view in Sentry
              setNavigationContext(currentRouteName);

              // Update the route name ref
              routeNameRef.current = currentRouteName;
            }
          } catch (error) {
            console.error('Error in SentryNavigationContainer onStateChange:', error);
            Sentry.captureException(error);
          }
        }}
        {...rest}>
        {children}
      </NavigationContainer>
    );
  } catch (error) {
    console.error('Error in SentryNavigationContainer render:', error);
    Sentry.captureException(error);

    // Fallback UI in case of error
    return <NavigationContainer {...rest}>{children}</NavigationContainer>;
  }
};

export default SentryNavigationContainer;
