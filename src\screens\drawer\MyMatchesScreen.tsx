import React, {useState, useMemo} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Switch, ScrollView, FlatList} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {useNavigation} from '@react-navigation/native';
import {Header, Icon, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import {OfferBanner} from '@/components/common/OfferBanner';
import CustomToggleSwitch from '@/components/CustomToggleSwitch';
import SearchInput from '@/components/SearchInput';
import useTranslation from '@/hooks/useTranslation';
import {myMatchesData} from '@/config/staticData';

const MyMatchesScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [chatWidgetEnabled, setChatWidgetEnabled] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const {t} = useTranslation();
  // Sample match data (would come from API/database in real app)

  // Filter list data based on search value
  const filteredListData = useMemo(() => {
    if (!searchValue.trim()) {
      return myMatchesData;
    }
    return myMatchesData.filter(item =>
      t(item.title).toLowerCase().includes(searchValue.toLowerCase()),
    );
  }, [searchValue, myMatchesData]);

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles(theme).container}>
      {/* Header with back button */}
      <Header
        leftComponent={
          <TouchableOpacity onPress={() => navigation.goBack()} activeOpacity={0.7}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        }
        rightIcons={[
          {name: 'notification', size: 24, color: theme.colors.activeColor},
          {name: 'chat', size: 24, badge: 14, color: theme.colors.activeColor},
        ]}
        pageTitle={t('drawerMyMatches.myMatches')}
        backgroundColor={'transparent'}
        transparent={false}
        showBack={false}
      />

      {/* Search Bar */}

      <SearchInput
        placeholder={t('drawerMyMatches.searchMatches')}
        value={searchValue}
        onChangeText={setSearchValue}
        onClear={() => setSearchValue('')}
        containerStyle={styles(theme).searchContainer}
      />

      <FlatList
        style={styles(theme).contentContainer}
        data={filteredListData}
        renderItem={({item}) => (
          <>
            <View style={styles(theme).sectionContainer}>
              <Typography variant="subtitle" style={styles(theme).sectionTitle}>
                {t(item.title)}
              </Typography>
              {item.id === 'notifications' || item.id === 'chat' ? (
                <CustomToggleSwitch
                  isEnabled={item.id === 'notifications' ? notificationsEnabled : chatWidgetEnabled}
                  onToggle={value => {
                    if (item.id === 'notifications') {
                      setNotificationsEnabled(value);
                    } else {
                      setChatWidgetEnabled(value);
                    }
                  }}
                  activeText={t('common.on')}
                  inactiveText={t('common.off')}
                />
              ) : (
                <Icon name="chevron-right" size={20} color={theme.colors.white} />
              )}
            </View>

            {item.id !== 'chat' && <View style={styles(theme).divider} />}
          </>
        )}
        ListFooterComponent={() => <View style={{height: 80}} />}
      />

      {/* Footer */}
      <OfferBanner text="Invite friends, get 10% off" />
    </SafeAreaView>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.colors.black,
    },
    backButton: {
      padding: 8,
    },
    headerTitle: {
      fontSize: 24,
      color: theme.colors.white,
      fontWeight: 'bold',
    },
    headerIcons: {
      flexDirection: 'row',
    },
    headerIcon: {
      marginLeft: 20,
    },
    badgeContainer: {
      position: 'relative',
    },
    badge: {
      position: 'absolute',
      top: -5,
      right: -8,
      backgroundColor: theme.colors.lime,
      borderRadius: 10,
      minWidth: 18,
      height: 18,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 2,
    },
    searchContainer: {
      marginHorizontal: 16,
    },

    contentContainer: {
      flexGrow: 1,
      paddingHorizontal: 16,
    },
    sectionContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 20,
    },
    sectionTitle: {
      fontSize: 18,
      color: theme.colors.white,
      fontWeight: '500',
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.secondary,
    },
    settingsSection: {
      marginTop: 16,
    },
    settingItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 16,
    },
    settingLabel: {
      fontSize: 16,
      color: theme.colors.white,
    },
    footer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
    },
    footerButton: {
      backgroundColor: theme.colors.oceanBlue,
      paddingVertical: 14,
      alignItems: 'center',
    },
    footerButtonText: {
      color: theme.colors.white,
      fontSize: 16,
      fontWeight: '500',
    },
  });

export default MyMatchesScreen;
