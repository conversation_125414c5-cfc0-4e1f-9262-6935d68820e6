import React, {useState} from 'react';
import { useThemeStore } from '@/store/themeStore';;
import {View, Modal, StyleSheet, Text, TouchableOpacity} from 'react-native';
import DatePicker from 'react-native-date-picker';
interface ModalTimePickerProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  initialHour: number;
  initialMinute: number;
  initialAmPm: 'AM' | 'PM';
  onTimeSelected: (hour: number, minute: number, amPm: 'AM' | 'PM') => void;
}

const ModalTimePicker: React.FC<ModalTimePickerProps> = ({
  visible,
  onClose,
  title,
  initialHour = 8,
  initialMinute = 0,
  initialAmPm = 'AM',
  onTimeSelected,
}) => {
  const theme = useThemeStore();

  // Create initial date with the provided hour and minute
  const getInitialDate = () => {
    const date = new Date();
    let hours = initialHour;

    // Convert to 24-hour format for Date object
    if (initialAmPm === 'PM' && initialHour < 12) {
      hours += 12;
    } else if (initialAmPm === 'AM' && initialHour === 12) {
      hours = 0;
    }

    date.setHours(hours);
    date.setMinutes(initialMinute);
    return date;
  };

  const [date, setDate] = useState(getInitialDate());

  const handleConfirm = () => {
    const hours = date.getHours();
    const minutes = date.getMinutes();

    // Convert hours to 12-hour format
    let hour12 = hours % 12;
    if (hour12 === 0) {
      hour12 = 12;
    }

    // Determine AM/PM
    const amPm = hours < 12 ? 'AM' : 'PM';

    onTimeSelected(hour12, minutes, amPm as 'AM' | 'PM');
    onClose();
  };

  return (
    <Modal visible={visible} transparent={true} animationType="slide" onRequestClose={onClose}>
      <View style={styles(theme).modalContainer}>
        <TouchableOpacity style={styles(theme).overlay} activeOpacity={1} onPress={onClose} />
        <View style={styles(theme).modalContent}>
          <View style={styles(theme).header}>
            <Text style={[styles(theme).headerText, {color: theme.colors.text}]}>{title}</Text>
          </View>

          <DatePicker
            date={date}
            onDateChange={setDate}
            mode="time"
            theme="dark"
            style={{backgroundColor: theme.colors.blackBg}}
          />

          <TouchableOpacity
            style={[styles(theme).confirmButton, {borderColor: theme.colors.activeColor}]}
            onPress={handleConfirm}>
            <Text style={[styles(theme).confirmText, {color: theme.colors.activeColor}]}>Done</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: 'flex-end',
      margin: 0,
    },
    overlay: {
      position: 'absolute',
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
      backgroundColor: theme.colors.semiTransparentBlack,
    },
    modalContent: {
      backgroundColor: theme.colors.blackBg,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      padding: 20,
      alignItems: 'center',
    },
    header: {
      width: '100%',
      alignItems: 'center',
      marginBottom: 20,
    },
    headerText: {
      fontSize: 18,
      fontWeight: 'bold',
    },
    confirmButton: {
      marginTop: 20,
      paddingVertical: 12,
      paddingHorizontal: 40,
      borderWidth: 1,
      borderRadius: 20,
    },
    confirmText: {
      fontSize: 16,
      fontWeight: '600',
    },
  });

export default ModalTimePicker;
