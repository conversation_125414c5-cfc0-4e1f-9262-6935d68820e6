import React from 'react';
import {
  View,
  TouchableOpacity,
  Modal as RNModal,
  TouchableWithoutFeedback,
  ViewStyle,
  TextStyle,
  GestureResponderEvent,
  Platform,
  KeyboardAvoidingView,
  ImageBackground,
} from 'react-native';
import {createStyles} from './styles';
import {Icon} from '@/components';
import Typography from '@/components/Typography';
import {useThemeStore} from '@/store/themeStore';
import {Images} from '@/config';

interface CustomModalProps {
  visible: boolean;
  title?: string;
  onClose?: () => void;
  onAccept?: () => void;
  acceptButtonText?: string;
  showAcceptButton?: boolean;
  children: React.ReactNode;
  modalContainerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  acceptButtonStyle?: ViewStyle;
  acceptButtonTextStyle?: TextStyle;
  showCloseButton?: boolean;
  showCloseButtonRight?: boolean;
  showClearButtonRight?: string;
  onClear?: () => void;
  variant?: 'bottom' | 'default';
  imageBg?: boolean;
  animationType?: 'slide' | 'fade' | 'none';
}

const CustomModal: React.FC<CustomModalProps> = ({
  visible,
  title,
  onClose,
  onClear,
  children,
  modalContainerStyle,
  titleStyle,
  showCloseButton = false,
  showCloseButtonRight = false,
  showClearButtonRight = '',
  variant = 'default',
  imageBg = false,
  animationType = 'fade',
}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  const handleOverlayClick = (e: GestureResponderEvent) => {
    if (e.target === e.currentTarget && onClose) {
      onClose();
    }
  };

  const Header = () => {
    return (
      <View style={styles.header}>
        {showCloseButton && (
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Icon name="close1-1" size={28} color="red" />
          </TouchableOpacity>
        )}
        {title && (
          <Typography variant="subtitle" style={{...styles.title, ...titleStyle}}>
            {title}
          </Typography>
        )}
        {showCloseButtonRight && (
          <TouchableOpacity onPress={onClose} style={styles.closeButtonRight}>
            <Icon name="close1-1" size={36} color="red" />
          </TouchableOpacity>
        )}
        {showClearButtonRight && (
          <TouchableOpacity onPress={onClear} style={styles.closeButtonRight}>
            <Typography variant="bodyMedium" style={{...styles.clearButtonRight}}>
              {showClearButtonRight}
            </Typography>
          </TouchableOpacity>
        )}
      </View>
    );
  };
  return (
    <View style={[styles.centeredView]}>
      <RNModal
        animationType={animationType}
        transparent={true}
        visible={visible}
        onRequestClose={onClose}>
        <TouchableWithoutFeedback onPress={handleOverlayClick}>
          <View style={variant === 'bottom' ? styles.bottomOverlay : styles.modalOverlay}>
            <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
              {imageBg ? (
                <ImageBackground
                  source={Images.gradientBg}
                  style={[styles.gradientContainer]}
                  resizeMode="cover">
                  <View
                    style={[
                      styles.gradientContent,
                      modalContainerStyle,
                      variant === 'bottom' && {width: '100%', maxWidth: '100%'},
                    ]}>
                    <View style={styles.borderContainer}>
                      <Header />

                      {/* Content */}
                      {children}
                    </View>
                  </View>
                </ImageBackground>
              ) : (
                <View
                  style={[
                    styles.modalContent,
                    modalContainerStyle,
                    variant === 'bottom' && {width: '100%', maxWidth: '100%'},
                  ]}>
                  {/* Header */}
                  <Header />

                  {/* Content */}
                  {children}
                </View>
              )}
            </KeyboardAvoidingView>
          </View>
        </TouchableWithoutFeedback>
      </RNModal>
    </View>
  );
};

export default CustomModal;

/**
 * Usage Examples:
 *
 * Basic Usage:
 * ```tsx
 * <CustomModal
 *   visible={isModalVisible}
 *   title="My Modal"
 *   onClose={() => setIsModalVisible(false)}
 *   onAccept={() => {
 *     // Handle accept action
 *     setIsModalVisible(false);
 *   }}
 * >
 *   <Text>Your modal content goes here</Text>
 * </CustomModal>
 * ```
 *
 * Custom Styled Modal:
 * ```tsx
 * <CustomModal
 *   visible={isModalVisible}
 *   title="Custom Styled Modal"
 *   onClose={() => setIsModalVisible(false)}
 *   modalContainerStyle={{ backgroundColor: '#333' }}
 *   titleStyle={{ color: '#00FF00' }}
 *   acceptButtonStyle={{ backgroundColor: '#FF0000' }}
 *   acceptButtonTextStyle={{ fontSize: 18 }}
 * >
 *   <Text>Custom styled content</Text>
 * </CustomModal>
 * ```
 *
 * Modal without Accept Button:
 * ```tsx
 * <CustomModal
 *   visible={isModalVisible}
 *   title="Info Modal"
 *   onClose={() => setIsModalVisible(false)}
 *   showAcceptButton={false}
 * >
 *   <Text>This is an information modal without an accept button</Text>
 * </CustomModal>
 * ```
 */

// --------------- don't remove be

// this design is used for the Reserve balls and equipment modal
{
  /*
  const [isModalVisible, setIsModalVisible] = useState(false);
   <CustomModal visible={isModalVisible} onClose={() => setIsModalVisible(false)}></CustomModal>
<View style={{alignItems: 'center', gap: 25}}>
<Icon name="arrow" size={100} color={theme.colors.activeColor} />
<Typography variant="subtitle" style={{textAlign: 'center', color: theme.colors.text}}>
  Reserve balls and equipment
</Typography>
<CButton
  title="Yes!"
  onPress={() => setIsModalVisible(false)}
  variant="pill"
  containerStyle={{width: '85%', backgroundColor: theme.colors.activeColor}}
  textStyle={{color: theme.colors.black}}
/>
<CButton
  title="Close"
  onPress={() => setIsModalVisible(false)}
  variant="outline"
  containerStyle={{
    width: '85%',
    borderWidth: 0,
    borderColor: theme.colors.text,
  }}
/>
</View>
</CustomModal> */
}
