import {Dimensions, StyleSheet} from 'react-native';

const {width, height} = Dimensions.get('window');

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme?.colors?.background,
    },
    carousel: {
      flexGrow: 1,
    },
    question: {
      color: theme?.colors?.white,
      textAlign: 'center',
    },
    pagination: {
      flexDirection: 'row',
      justifyContent: 'center',
      position: 'absolute',
      bottom: '2%',
      left: 0,
      right: 0,
    },
    dotActive: {
      width: 10,
      height: 10,
      borderRadius: 5,
      backgroundColor: theme.colors.activeColor,
      marginHorizontal: 4,
    },
    dot: {
      width: 10,
      height: 10,
      borderRadius: 5,
      backgroundColor: theme.colors.dimGray,
      marginHorizontal: 4,
    },
    racquetImageWrapper: {
      position: 'relative',
      alignItems: 'center',
      flex: 1,
      paddingTop: 25,
    },
    racquetImage: {
      width: width,
      height: height * 0.57,
    },
    plusCircle: {
      position: 'absolute',
      top: '5%',
      right: '24%',
      alignItems: 'center',
      justifyContent: 'center',
    },
    racquetName: {
      color: theme.colors.white,
      textAlign: 'center',
    },
    tryNow: {
      color: theme.colors.activeColor,
      fontSize: 16,
    },
    tryNowContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
      gap: 8,
    },
    modalContainer: {
      flexGrow: 1,
      backgroundColor: theme.colors.background,
      paddingHorizontal: 16,
      paddingBottom: 20,
    },
    carouselContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
    },
    racquetCarousel: {
      marginTop: 15,
      alignSelf: 'center',
    },
    racquetSlide: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    indicatorContainer: {
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
    },
    indicatorItem: {
      marginHorizontal: 5,
      alignItems: 'center',
      justifyContent: 'center',
    },
    indicatorIcon: {
      width: 40,
      height: 40,
    },
    contentContainer: {
      marginTop: 16,
    },
    titleContainer: {
      marginBottom: 16,
    },
    centerText: {
      textAlign: 'center',
      color: theme.colors.text,
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.white2,
      marginVertical: 2,
      width: '100%',
      alignSelf: 'center',
    },
    listItem: {
      color: theme.colors.white,
      fontSize: 14,
    },
    ratingContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    racketImage: {
      width: 210,
      height: 320,
    },
  });
