import React, {useState, useEffect, useCallback} from 'react';
import {View, Alert, StyleSheet, Platform, Linking, TouchableOpacity} from 'react-native';
import {launchCamera, launchImageLibrary} from 'react-native-image-picker';
import {check, request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import CImage from '../CImage';
import CIcon from '../CIcon';
import CustomModal from '../Modal';
import Typography from '../Typography';
import {useThemeStore} from '@/store';
import {isArray, isEmpty} from 'lodash';
import {IOS} from '@/utils/commonFunctions';
import CButton from '../CButton';
import {Icons} from '@/config/icons';

interface UploadComponentProps {
  allowGallerySelection?: boolean;
  allowCameraCapture?: boolean;
  allowUpload?: boolean;
  onSelected?: (media: string | {uri: string; type: 'image' | 'video'}[]) => void;
  value?: string | null | {uri: string; type: 'image' | 'video'}[];
  containerStyle?: object;
  imageContentStyle?: object;
  uploadTitle?: string;
  multiple?: boolean;
}

interface ImageOption {
  id: number;
  optionTitle: string;
  handleClick: () => void;
  icon: string;
}

const UploadComponent: React.FC<UploadComponentProps> = ({
  onSelected,
  imageContentStyle,
  containerStyle,
  value = [],
  uploadTitle = '',
  multiple = false,
}) => {
  const theme = useThemeStore();
  const [imageUri, setImageUri] = useState<string | null>(isArray(value) ? value[0]?.uri : value);
  const [mediaFiles, setMediaFiles] = useState<{uri: string; type: 'image' | 'video'}[]>(
    isArray(value) ? value : [],
  );
  const [visible, setVisible] = useState(false);

  const imageOptionsArray: ImageOption[] = [
    {
      id: 1,
      optionTitle: 'Open camera',
      handleClick: () => capturePhoto(),
      icon: 'camera-1',
    },
    {
      id: 2,
      optionTitle: 'Open gallery',
      handleClick: () => pickImageFromGallery(),
      icon: 'camera-1',
    },
  ];

  // Notify parent of image URI changes
  useEffect(() => {
    if (onSelected) {
      if (multiple) {
        onSelected(mediaFiles);
      } else if (imageUri) {
        // If a single image is selected, pass the image URI
        onSelected(imageUri);
      }
    }
  }, [mediaFiles, imageUri, onSelected]);

  // Function to show alert prompting user to open settings
  const openSettingsAlert = useCallback(({title}: {title: string}) => {
    Alert.alert(title, 'Please enable the required permission in your device settings.', [
      {
        text: 'Open Settings',
        onPress: () => Linking.openSettings(),
      },
      {
        text: 'Cancel',
        style: 'cancel',
      },
    ]);
  }, []);

  // Request permissions on component mount, based on props

  const requestPermissions = async (
    camera: boolean,
    album: boolean,
  ): Promise<{camera: boolean; album: boolean}> => {
    const cameraPermission = IOS ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.ANDROID.CAMERA;
    const galleryPermission = IOS
      ? PERMISSIONS.IOS.PHOTO_LIBRARY
      : Number(Platform.Version) >= 33
        ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
        : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;

    let cameraGranted = true;
    let galleryGranted = true;

    if (camera) {
      let cameraStatus = await check(cameraPermission);
      if (cameraStatus !== RESULTS.GRANTED) {
        cameraStatus = await request(cameraPermission);
      }

      if (cameraStatus !== RESULTS.GRANTED) {
        cameraGranted = false;
        openSettingsAlert({title: 'Camera Permission'});
      }
    }

    if (album) {
      let galleryStatus = await check(galleryPermission);
      if (galleryStatus !== RESULTS.GRANTED) {
        galleryStatus = await request(galleryPermission);
      }

      if (galleryStatus !== RESULTS.GRANTED) {
        galleryGranted = false;
        openSettingsAlert({title: 'Gallery Permission'});
      }
    }

    return {camera: cameraGranted, album: galleryGranted};
  };

  // Function to pick an image from the gallery
  const pickImageFromGallery = async () => {
    const {album} = await requestPermissions(false, true);
    if (album) {
      launchImageLibrary(
        {
          mediaType: uploadTitle ? 'mixed' : 'photo',
          quality: 1,
          includeBase64: false,
          selectionLimit: multiple ? 0 : 1, // 0 for unlimited selection
        },
        response => {
          if (response.didCancel) {
            console.log('User cancelled image picker');
          } else if (response.errorCode) {
            Alert.alert('Error', `Image picker error: ${response.errorMessage}`);
          } else if (response.assets && response.assets.length > 0) {
            const newMedia = response?.assets?.map(asset => ({
              uri: asset.uri ?? '',
              type: asset.type?.startsWith('image') ? ('image' as const) : ('video' as const),
            }));
            if (multiple) {
              setMediaFiles(newMedia as {uri: string; type: 'image' | 'video'}[]);
            } else {
              setImageUri(response?.assets[0]?.uri ?? null);
            }
            setVisible(false);
          }
        },
      );
    }
  };

  // Function to capture a photo from the camera
  const capturePhoto = async () => {
    const {camera} = await requestPermissions(true, false);
    if (camera) {
      launchCamera(
        {
          mediaType: 'photo',
          quality: 1,
          includeBase64: false,
        },
        response => {
          if (response.didCancel) {
            console.log('User cancelled camera');
          } else if (response.errorCode) {
            Alert.alert('Error', `Camera error: ${response.errorMessage}`);
          } else if (response.assets && response.assets.length > 0) {
            const newMedia = response?.assets?.map(asset => ({
              uri: asset.uri ?? '',
              type: asset.type?.startsWith('image') ? ('image' as const) : ('video' as const),
            }));
            if (multiple) {
              setMediaFiles(newMedia as {uri: string; type: 'image' | 'video'}[]);
            } else {
              setImageUri(response?.assets[0]?.uri ?? null);
            }
            setVisible(false);
          }
        },
      );
    }
  };

  return (
    <>
      {uploadTitle ? (
        <CButton variant="active" title={uploadTitle} onPress={() => pickImageFromGallery()} />
      ) : (
        <>
          <View style={[containerStyle, styles.modalContainer]}>
            <TouchableOpacity onPress={() => setVisible(true)}>
              {imageUri ? (
                <View style={[styles.imageContainer, imageContentStyle]}>
                  <CImage source={{uri: imageUri}} style={styles.image} pointerEvents="none" />
                </View>
              ) : (
                <CIcon name="camera-1" size={24} color={theme.colors.black} />
              )}
            </TouchableOpacity>
          </View>

          <CustomModal
            variant="bottom"
            visible={visible}
            onClose={() => setVisible(false)}
            title={'Choose a photo from your gallery or take a new one with your camera.'}
            titleStyle={{color: theme.colors.text}}
            modalContainerStyle={{backgroundColor: theme.colors.background}}>
            <View style={styles.importContainer}>
              {!isEmpty(imageOptionsArray) &&
                isArray(imageOptionsArray) &&
                imageOptionsArray.map((item, index) => (
                  <TouchableOpacity
                    key={`options_${item.id}_${index}`}
                    onPress={item.handleClick}
                    style={styles.importBlock}>
                    {index === 0 ? (
                      <CIcon name={item.icon} size={32} color={theme.colors.white} />
                    ) : (
                      <Icons.Entypo name="images" size={28} color={theme.colors.white} />
                    )}
                    <Typography color={theme.colors.white}>{item.optionTitle}</Typography>
                  </TouchableOpacity>
                ))}
            </View>
          </CustomModal>
        </>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    overflow: 'hidden',
  },
  imageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  importContainer: {
    flexDirection: 'row',
    gap: 20,
    justifyContent: 'center',
    marginVertical: 10,
  },
  importBlock: {
    backgroundColor: '#333',
    borderRadius: 12,
    gap: 10,
    flex: 0.5,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 30,
  },
});

export default UploadComponent;
