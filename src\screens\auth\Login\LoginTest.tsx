import React, {useState} from 'react';
import {View, Text, StyleSheet, Button, TextInput, Alert} from 'react-native';
import axios from 'axios';

/**
 * A simple component to test the login API directly
 */
const LoginTest = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('$miT2345');
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);

  const handleLogin = async () => {
    setLoading(true);
    try {
      console.log('Sending login request with:', {email, password});
      const result = await axios.post(
        'https://6559-171-78-198-103.ngrok-free.app/go-reqt/v1/login',
        {
          email,
          password,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      console.log('Raw API response:', result);

      setResponse(result.data);
      Alert.alert('Success', 'Login successful');
    } catch (error) {
      console.error('Login error:', error);
      if (axios.isAxiosError(error)) {
        setResponse(error.response?.data || error.message);
        Alert.alert('Error', error.response?.data?.message || error.message);
      } else {
        setResponse({error: 'Unknown error occurred'});
        Alert.alert('Error', 'Unknown error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Login API Test</Text>

      <TextInput
        style={styles.input}
        value={email}
        onChangeText={setEmail}
        placeholder="Email"
        keyboardType="email-address"
        autoCapitalize="none"
      />

      <TextInput
        style={styles.input}
        value={password}
        onChangeText={setPassword}
        placeholder="Password"
        secureTextEntry
      />

      <Button
        title={loading ? 'Loading...' : 'Test Login'}
        onPress={handleLogin}
        disabled={loading}
      />

      {response && (
        <View style={styles.responseContainer}>
          <Text style={styles.responseTitle}>API Response:</Text>
          <Text style={styles.responseText}>{JSON.stringify(response, null, 2)}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  responseContainer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  responseTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  responseText: {
    fontFamily: 'monospace',
  },
});

export default LoginTest;
