import {FONT_SIZE} from '@/utils/fonts';
import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    closeButton: {
      position: 'absolute',
      top: 10,
      right: 10,
    },
    scrollContainer: {
      flexGrow: 1,
      justifyContent: 'center',
      paddingHorizontal: 16,
    },

    nameContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 10,
      marginBottom: 30,
    },
    profileStatsContainer: {
      alignItems: 'center',
    },
    formContainer: {
      flex: 1,
      justifyContent: 'center',
    },
    keyboardAvoidingView: {
      flex: 1,
    },

    label: {
      color: theme.colors.white,
      fontSize: 18,
      fontWeight: '400',
    },
    profileSection: {
      alignItems: 'center',
      paddingTop: 5,
      paddingBottom: 20,
    },
    profileImageContainer: {
      position: 'relative',
      marginBottom: 16,
    },
    profileImage: {
      width: 142,
      height: 142,
      borderRadius: 100,
      backgroundColor: theme.colors.activeColor,
      justifyContent: 'center',
      alignItems: 'center',
    },

    qrCodeContainer: {
      position: 'absolute',
      bottom: -10,
      right: -10,
      backgroundColor: theme.colors.white,
      borderRadius: 100,
      padding: 6,
      width: 56,
      height: 56,
      borderWidth: 3,
      borderColor: theme.colors.black,
      justifyContent: 'center',
      alignItems: 'center',
    },
    emailText: {
      marginTop: -10,
    },
    statsContainer: {
      flexDirection: 'row',
      backgroundColor: theme.colors.TranslucentWhite || 'rgba(255, 255, 255, 0.15)',
      borderRadius: 20,
      paddingVertical: 20,
    },
    statItem: {
      alignItems: 'center',
      flex: 1,
    },
    statDivider: {
      width: 1,
      height: '100%',
      backgroundColor: theme.colors.white,
    },
    inputContainer: {
      marginBottom: 5,
    },
    input: {
      borderWidth: 0,
      borderRadius: 8,
      height: 70,
      fontWeight: '400',
      fontSize: 16,
    },
    birthYearWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      position: 'relative',
    },
    ageTag: {
      position: 'absolute',
      right: 20,
      bottom: 33,
    },
    ageTagText: {
      color: theme.colors.white,
      fontWeight: '700',
      fontSize: 14,
    },
    ballContainer: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      right: -45,
      borderRadius: 100,
      padding: 6,
      justifyContent: 'center',
      alignItems: 'center',
    },
    rightComponentContainer: {
      flexDirection: 'row',
      gap: 20,
    },
    statLabel: {
      fontSize: 16,
    },
    buttonText: {
      color: theme.colors.black,
      fontSize: FONT_SIZE.xl,
      fontWeight: '700',
    },
    buttonContainer: {
      marginTop: 5,
      backgroundColor: theme.colors.inputLabel,
      width: '50%',
      borderRadius: 10,
      paddingVertical: 10,
    },
    btnMain: {
      alignItems: 'center',
    },
  });
