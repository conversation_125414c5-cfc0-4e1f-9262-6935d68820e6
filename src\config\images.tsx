/* eslint-disable @typescript-eslint/no-require-imports */
/**
 * Exports Image Paths
 * @function Images
 */
export const Images = {
  goRactLogo: require('../assets/images/goRactLogo.webp'),
  // social icons
  facebook: require('../assets/images/facebook.webp'),
  google: require('../assets/images/google.webp'),
  mail: require('../assets/images/email.webp'),

  splash: require('../assets/images/splash.webp'),
  advertisementLogo: require('../assets/images/advertisement.webp'),
  logo: require('../assets/images/logo.webp'),
  polygon: require('../assets/images/Polygon.webp'),
  shoes: require('../assets/images/shoes.webp'),
  gym: require('../assets/images/gym.webp'),
  goEat: require('../assets/images/goEat.webp'),
  goTravel: require('../assets/images/goTravel.webp'),
  thumbnail: require('../assets/images/Thumbnail.webp'),
  gradientBg: require('../assets/images/GradientBg.webp'),

  // map markers
  availableKiosk: require('../assets/images/availableKiosk.webp'),
  plannedLocation: require('../assets/images/plannedLocation.webp'),

  profile1: require('../assets/images/profile1.webp'),
  profile2: require('../assets/images/profile2.webp'),
  profile3: require('../assets/images/profile3.webp'),
  dunlopRound: require('../assets/images/dunlopRound.webp'),

  coachProfile: require('../assets/images/CoachProfile.webp'),
  goFitImage: require('../assets/images/GoFitImage.webp'),
  goEatImage: require('../assets/images/GoEatImage.webp'),
  goTravelImage: require('../assets/images/GoTravelImage.webp'),

  // community home
  personMaker: require('../assets/images/PersonMarker.webp'),
  review: require('../assets/images/review.webp'),
  conversation: require('../assets/images/conversation.webp'),
  goDeeper: require('../assets/images/goDeeper.webp'),
  tips: require('../assets/images/tips.webp'),
  goFit: require('../assets/images/goFit.webp'),
  dunlop: require('../assets/images/dunlop.webp'),

  // brands
  babolat: require('../assets/images/babolat.webp'),
  topBrand: require('../assets/images/topBrand.webp'),

  // racqutes
  babolatPureDrive: require('../assets/images/Babolat-PureDrive.webp'),

  tennisBrand: require('../assets/images/tennisBrand.webp'),
  racquet1: require('../assets/images/Dunlop-racket.webp'),
  racquet2: require('../assets/images/Dunlop-racket1.webp'),
  racquet3: require('../assets/images/Dunlop-racket2.webp'),
  racquet4: require('../assets/images/Dunlop-racket3.webp'),
  racquet5: require('../assets/images/Dunlop-racket4.webp'),

  // header background
  headerBg: require('../assets/images/headerBg.webp'),

  // lottie
  soundWave: require('../assets/lottieFiles/soundWave.json'),
};
