import {StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    tabContainer: {
      flexDirection: 'row',
      gap: 15,
    },
    tabContainerEquipment: {
      flexDirection: 'row',
      gap: 10,
    },
    tab: {
      paddingVertical: 10,
      paddingHorizontal: 40,
      borderRadius: 50,
      borderWidth: 2,
      minWidth: 100,
      alignItems: 'center',
    },
    activeTab: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.text,
    },
    inactiveTab: {
      backgroundColor: theme.colors.white,
      borderColor: theme.colors.primary,
    },
    inactiveTabEquipment: {
      backgroundColor: theme.colors.bgGrey,
      borderColor: theme.colors.primary,
    },
    equipmentTab: {
      paddingVertical: 3,
      paddingHorizontal: 10,
      borderRadius: 50,
      minWidth: 70,
      alignItems: 'center',
    },

    squareTab: {
      paddingVertical: 6,
      paddingHorizontal: 20,
      borderRadius: 10,
      color: theme.colors.text,
      backgroundColor: '#FFFFFF15',
    },
    container: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 8,
    },
    tab1: {
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 20,
    },
    activeTab1: {
      backgroundColor: theme.colors.primary,
    },
    inactiveTab1: {
      backgroundColor: theme.colors.background,
    },

    inactiveTabEquipment1: {
      backgroundColor: theme.colors.bgGrey,
    },
  });
