import React, {useState, useRef, useMemo} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  PanResponder,
  LayoutChangeEvent,
  Dimensions,
  ViewStyle,
  TextStyle,
} from 'react-native';
import * as shape from 'd3-shape';
import {
  Svg,
  Defs,
  LinearGradient,
  Stop,
  Path,
  Circle,
  G,
  Line as SvgLine,
  Rect,
  Text as SvgText,
} from 'react-native-svg';
import CustomDropdown from './CustomDropdown/CustomDropdown';

interface ChartData {
  [key: string]: number[];
}

interface SvgLayout {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface PlayerStatsChartProps {
  // Data customization
  variant?: 'default' | 'state';
  data?: ChartData;
  months?: string[];
  yearOptions?: Array<{label: string; value: string}>;
  initialYear?: string;

  // Colors customization
  colors?: {
    highlight?: string;
    inactive?: string;
    background?: string;
    text?: string;
    gradientStart?: string;
    gradientEnd?: string;
  };

  // Dimensions customization
  dimensions?: {
    maxWidth: number;
    height: number;
    padding: number;
  };

  // Style customization
  containerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  legendStyle?: TextStyle;
  monthLabelStyle?: TextStyle;

  // Component customization
  title?: string;
  showLegend?: boolean;
  showTooltip?: boolean;
  showYearSelector?: boolean;
  customTooltipFormat?: (month: string, value: number) => string;
  highlightIndex?: number;
}

const defaultColors = {
  highlight: '#eaff00',
  inactive: '#888',
  background: '#8E8E8E33',
  text: '#fff',
  gradientStart: '#fff',
  gradientEnd: '#888',
};

const defaultDimensions = {
  maxWidth: 380,
  height: 140,
  padding: 24,
} as const;

const defaultMonths = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
];

const defaultYearOptions = [
  {label: '2023', value: '2023'},
  {label: '2024', value: '2024'},
];

const defaultData = {
  '2023': [2, 4, 6, 8, 12, 18, 25, 20, 15, 10, 10, 11],
  '2024': [1, 2, 3, 5, 8, 13, 21, 24],
};

const PlayerStatsChart: React.FC<PlayerStatsChartProps> = ({
  variant = 'default',
  data = defaultData,
  months = defaultMonths,
  yearOptions = defaultYearOptions,
  initialYear = '2023',
  colors = defaultColors,
  dimensions = defaultDimensions,
  highlightIndex = 7,
  containerStyle,
  titleStyle,
  legendStyle,
  monthLabelStyle,
  title = 'Player Stats',
  showLegend = true,
  showTooltip = true,
  showYearSelector = true,
  customTooltipFormat,
}) => {
  const {maxWidth, height, padding} = dimensions;
  const {highlight, inactive, background, text, gradientStart, gradientEnd} = colors;

  const getChartDimensions = () => {
    const screenWidth = Dimensions.get('window').width;
    const chartWidth = Math.min(screenWidth - 32, maxWidth);
    return {
      chartWidth,
      chartHeight: height,
      chartPadding: padding,
    } as const;
  };

  const {chartWidth, chartHeight, chartPadding} = useMemo(
    () => getChartDimensions(),
    [maxWidth, height, padding],
  );

  // Move styles inside component to access dynamic dimensions
  const styles = StyleSheet.create({
    container: {
      backgroundColor: background,
      borderRadius: 20,
      padding: 16,
      width: chartWidth + 32,
      alignSelf: 'center',
      shadowColor: '#000',
      shadowOpacity: 0.15,
      shadowRadius: 10,
      shadowOffset: {width: 0, height: 4},
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    title: {
      color: text,
      fontWeight: 'bold',
      fontSize: 22,
    },
    yearSelector: {
      backgroundColor: '#444',
      borderRadius: 12,
      paddingHorizontal: 12,
      paddingVertical: 4,
    },
    yearSelectorText: {
      color: text,
      fontWeight: 'bold',
      fontSize: 16,
    },
    legendRowWrap: {
      flexDirection: 'row',
      marginTop: 12,
      marginBottom: 0,
    },
    legendRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 24,
    },
    legendDot: {
      width: 10,
      height: 10,
      borderRadius: 5,
      marginRight: 6,
    },
    legendText: {
      fontSize: 16,
    },
    monthsRow: {
      marginTop: 10,
      width: chartWidth,
      alignSelf: 'center',
      position: 'relative',
    },
    monthLabel: {
      fontSize: 10,
      width: 24,
      textAlign: 'center',
      position: 'absolute',
      bottom: 0,
    },
    seeMoreText: {
      color: '#ADADAD',
      fontSize: 14,
    },
  });

  const [selectedYear, setSelectedYear] = useState(initialYear);
  const [tooltipIndex, setTooltipIndex] = useState<number | null>(null);
  const [svgLayout, setSvgLayout] = useState<SvgLayout>({
    x: 0,
    y: 0,
    width: chartWidth,
    height: chartHeight + 24,
  });

  const data2023 = data['2023'];
  const data2024 = data['2024'];
  const chartData = selectedYear === '2023' ? data2023 : data2024;
  const highlightColor = selectedYear === '2023' ? inactive : highlight;
  const dataLength = chartData.length;

  // Move formatTooltip before it's used
  const formatTooltip = (month: string, value: number) => {
    if (customTooltipFormat) {
      return customTooltipFormat(month, value);
    }
    return `${month}: ${value || 0} wins`;
  };

  // X and Y scales
  const x = (i: number) =>
    chartPadding + i * ((chartWidth - 2 * chartPadding) / (data2023.length - 1));
  const y = (v: number) => {
    const min = Math.min(...data2023);
    const max = Math.max(...data2023);
    return chartHeight - ((v - min) / (max - min)) * (chartHeight - 32) - 16;
  };

  // Path for 2023 area
  const area = shape
    .area<number>()
    .x((_: number, i: number) => x(i))
    .y0(() => y(Math.min(...data2023)))
    .y1((d: number) => y(d))
    .curve(shape.curveMonotoneX)(data2023);

  // Path for 2023 top border
  const border = shape
    .line<number>()
    .x((_: number, i: number) => x(i))
    .y((d: number) => y(d))
    .curve(shape.curveMonotoneX)(data2023);

  // Path for 2024 yellow line (up to August)
  const yellowLine = shape
    .line<number>()
    .x((_: number, i: number) => x(i))
    .y((d: number) => y(d))
    .curve(shape.curveMonotoneX)(data2024);

  // Dot position for highlight (static)
  const dotX = x(highlightIndex);
  const dotY = y(data2024[highlightIndex]);

  // Tooltip logic
  const getIndexForX = (touchX: number) => {
    // Clamp touchX to chart area
    const clampedX = Math.max(chartPadding, Math.min(touchX, chartWidth - chartPadding));
    // Find closest index
    let minDist = Infinity;
    let minIdx = 0;
    for (let i = 0; i < chartData.length; i++) {
      const px = x(i);
      const dist = Math.abs(px - clampedX);
      if (dist < minDist) {
        minDist = dist;
        minIdx = i;
      }
    }
    return minIdx;
  };

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: evt => {
        const {locationX} = evt.nativeEvent;
        setTooltipIndex(getIndexForX(locationX));
      },
      onPanResponderMove: evt => {
        const {locationX} = evt.nativeEvent;
        setTooltipIndex(getIndexForX(locationX));
      },
      onPanResponderRelease: () => setTooltipIndex(null),
      onPanResponderTerminate: () => setTooltipIndex(null),
    }),
  ).current;

  // Tooltip rendering
  let tooltip = null;
  if (tooltipIndex !== null) {
    const tipX = x(tooltipIndex);
    const tipY = y(chartData[tooltipIndex]);
    tooltip = (
      <G>
        <SvgLine
          x1={tipX}
          y1={24}
          x2={tipX}
          y2={chartHeight}
          stroke={highlightColor}
          strokeWidth={1.5}
          opacity={0.7}
        />
        <Circle cx={tipX} cy={tipY} r={7} fill={highlightColor} stroke={text} strokeWidth={2} />
        <G>
          <SvgText
            x={tipX}
            y={tipY - 20}
            fontSize={13}
            fill={highlightColor}
            textAnchor="middle"
            fontWeight="bold">
            {formatTooltip(months[tooltipIndex], chartData[tooltipIndex])}
          </SvgText>
        </G>
      </G>
    );
  }

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.header}>
        <Text style={[styles.title, titleStyle]}>{title}</Text>
        {showYearSelector && variant === 'default' ? (
          <View>
            <CustomDropdown
              sportsData={yearOptions}
              initialValue={selectedYear}
              onChangeValue={setSelectedYear}
              value={selectedYear}
              mainStyles={{borderWidth: 0, backgroundColor: '#D9D9D933', width: 95}}
              contentContainerStyle={{borderWidth: 0, marginTop: 25}}
            />
          </View>
        ) : (
          <TouchableOpacity activeOpacity={0.8} onPress={() => {}}>
            <Text style={styles.seeMoreText}>See more</Text>
          </TouchableOpacity>
        )}
      </View>

      {showLegend && (
        <View style={styles.legendRowWrap}>
          {yearOptions.map(year => (
            <View key={year.value} style={styles.legendRow}>
              <View
                style={[
                  styles.legendDot,
                  {backgroundColor: selectedYear === year.value ? highlight : inactive},
                ]}
              />
              <Text
                style={[
                  styles.legendText,
                  legendStyle,
                  selectedYear === year.value
                    ? {color: highlight, fontWeight: 'bold'}
                    : {color: inactive, fontWeight: 'bold'},
                ]}>
                {year.label}
              </Text>
              <Text style={[styles.legendText, {color: inactive, marginLeft: 4}, legendStyle]}>
                wins
              </Text>
            </View>
          ))}
        </View>
      )}

      <View
        style={{height: chartHeight + 24, width: chartWidth, alignSelf: 'center', marginTop: 10}}
        onLayout={(e: LayoutChangeEvent) => {
          const {x, y, width, height} = e.nativeEvent.layout;
          setSvgLayout({x, y, width, height});
        }}
        {...(showTooltip ? panResponder.panHandlers : {})}>
        <Svg width={chartWidth} height={chartHeight + 24}>
          <Defs>
            <LinearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
              <Stop offset="0%" stopColor={gradientStart} stopOpacity={0.18} />
              <Stop offset="100%" stopColor={gradientEnd} stopOpacity={0.18} />
            </LinearGradient>
          </Defs>
          <Path d={area || ''} fill="url(#areaGradient)" />
          <Path d={border || ''} stroke={text} strokeWidth={2} fill="none" />
          {selectedYear === '2024' && (
            <>
              <Path d={yellowLine || ''} stroke={highlight} strokeWidth={3} fill="none" />
              <SvgLine
                x1={dotX}
                y1={24}
                x2={dotX}
                y2={chartHeight}
                stroke="#d3d3d3"
                strokeWidth={1}
                opacity={0.7}
              />
              <Circle cx={dotX} cy={dotY} r={5} fill={highlight} stroke={text} strokeWidth={1} />
            </>
          )}
          {Array.from({length: 12}).map((_, i) => (
            <Circle key={i} cx={x(i)} cy={chartHeight} r={1.5} fill="#aaa" opacity={0.7} />
          ))}
          {tooltip}
        </Svg>
      </View>

      <View style={styles.monthsRow}>
        {months.map((m, i) => (
          <Text
            key={i}
            style={[
              styles.monthLabel,
              {color: inactive},
              monthLabelStyle,
              {left: x(i) - 12, position: 'absolute'},
            ]}>
            {m}
          </Text>
        ))}
      </View>
    </View>
  );
};

export default PlayerStatsChart;
