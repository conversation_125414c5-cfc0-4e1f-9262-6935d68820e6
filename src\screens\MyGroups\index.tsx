import React, {useState} from 'react';
import {FlatList, View, TouchableOpacity, ActivityIndicator} from 'react-native';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import {Header, SafeAreaView} from '@/components';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import Typography from '@/components/Typography';
import GroupCard from '@/components/GroupCard';
import useTranslation from '@/hooks/useTranslation';

type NavigationProp = NativeStackNavigationProp<CommunityStackParamList>;

interface GroupsData {
  id: number;
  name: string;
  members: number;
  highlighted: boolean;
  type: string;
  locked: boolean;
}

interface PaginationState {
  page: number;
  loadMore: boolean;
}

const MyGroups = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  const navigation = useNavigation<NavigationProp>();

  const [groupType, setGroupType] = useState('Public');

  const [paginationLoader, setPaginationLoader] = useState<boolean>(false);
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    loadMore: false,
  });

  const {t} = useTranslation();

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const GroupsData: GroupsData[] = [
    {
      id: 1,
      name: 'Sunday Clinic - Fort Greene',
      members: 7,
      highlighted: false,
      type: 'group',
      locked: true,
    },
    {id: 2, name: 'Tennis Bash', members: 5, highlighted: false, type: 'group', locked: true},
  ];

  const renderComponent = ({item}: {item: GroupsData}) => {
    return (
      <GroupCard
        name={item.name}
        members={item.members}
        highlighted={item.highlighted}
        locked={item.locked}
        containerStyle={{
          borderWidth: 1,
          borderColor: theme.colors.divider,
          marginVertical: 8,
        }}
        onPress={() => navigation.navigate('Chat')}
      />
    );
  };

  function loadMoreData(): void {
    if (pagination?.loadMore && !paginationLoader) {
      setPaginationLoader(true);
      // add api call here
    }
  }

  const renderHeader = () => (
    <View style={styles.tabContainer}>
      <View style={styles.privacyRow}>
        {[t('common.public'), t('common.private'), t('common.hidden')].map(option => (
          <TouchableOpacity
            activeOpacity={0.7}
            key={option}
            style={[styles.privacyButton, groupType === option && styles.privacyButtonSelected]}
            onPress={() => setGroupType(option)}>
            <Typography
              variant="tryNow"
              style={[
                styles.privacyButtonText,
                groupType === option && styles.privacyButtonTextSelected,
              ]}>
              {option}
            </Typography>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView includeBottom={false} style={styles.root}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: theme.colors.primary,
        }}
        leftIconButtonStyle={styles.menuButton}
        onLeftPress={openDrawer}
        rightIcons={[
          {name: 'notification', size: 24, badge: 0},
          {name: 'chat', size: 24, badge: 14},
        ]}
        pageTitle={t('myGroupsScreen.title')}
        backgroundColor="transparent"
        // showBack={false}
      />
      <View style={styles.headerContainer}>{renderHeader()}</View>
      <FlatList
        data={GroupsData}
        showsVerticalScrollIndicator={false}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderComponent}
        contentContainerStyle={styles.content}
        onEndReachedThreshold={0.5}
        onEndReached={loadMoreData}
        ListFooterComponent={() =>
          paginationLoader ? (
            <ActivityIndicator color={theme.colors.activeColor} size="small" />
          ) : null
        }
      />
    </SafeAreaView>
  );
};

export default MyGroups;
