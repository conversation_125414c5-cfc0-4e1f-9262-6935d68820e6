import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  // SafeAreaView,
  Platform,
  Linking,
  Alert,
  PermissionsAndroid,
  ImageBackground,
} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {check, PERMISSIONS, request} from 'react-native-permissions';
import DeviceInfo from 'react-native-device-info';
import {CButton, SafeAreaView} from '@/components';
import {useIsFocused} from '@react-navigation/native';
import {Images} from '@/config';
import Typography from '@/components/Typography';
import {useConfigStore} from '@/store/configStore';
import {useTranslationContext} from '@/context/TranslationContext';

interface PermissionsScreenProps {
  onComplete: () => void;
}

const PermissionsScreen: React.FC<PermissionsScreenProps> = ({onComplete}) => {
  const theme = useThemeStore();
  const [currentPermission, setCurrentPermission] = useState<string>('location');
  const {t} = useTranslationContext();

  const IOS = Platform.OS === 'ios';

  const {permissions, setPermissions} = useConfigStore();

  const isFocused = useIsFocused();
  useEffect(() => {
    if (permissions?.location === true) {
      setCurrentPermission('bluetooth');
    }
    if (permissions?.bluetooth === true) {
      setCurrentPermission('notification');
    }
    if (permissions?.notification === true) {
      onComplete();
    }
  }, [isFocused, permissions, onComplete]);

  // handle permission response
  const handlePermissionResponse = (
    permission: 'location' | 'bluetooth' | 'notification' | 'contact',
    granted: boolean,
  ) => {
    // For now, just move to the next permission or complete
    if (permission === 'location') {
      if (!granted) {
        // Location is necessary, so don't proceed if denied
        return;
      }
      setCurrentPermission('bluetooth');
    } else if (permission === 'bluetooth') {
      setCurrentPermission('notification');
    }
    // else if (permission === 'notification') {
    //   setCurrentPermission('contact');
    // }
    else {
      // All permissions handled, proceed to the app
      onComplete();
    }
  };

  // Render location permission block
  const renderLocationPermission = () => (
    <View style={styles(theme).permissionCard}>
      <View>
        <Typography variant="permissionTitle" align="center" style={styles(theme).permissionTitle}>
          {t('permissions.locationPermissions')}
        </Typography>
        <Typography variant="body" align="center" style={styles(theme).permissionDescription}>
          {t('permissions.locationDescription')}
        </Typography>
      </View>
      <View>
        <CButton
          title={t('permissions.enableLocation')}
          variant="primary"
          onPress={() => checkLocationPermission()}
          containerStyle={styles(theme).buttonContainer}
        />

        <View style={styles(theme).dotsContainer}>
          <View style={[styles(theme).dot, {backgroundColor: theme.colors.activeColor}]} />
          <View style={styles(theme).dot} />
          <View style={styles(theme).dot} />
        </View>
      </View>
    </View>
  );

  // Render bluetooth permission block
  const renderBluetoothPermission = () => (
    <View style={[styles(theme).permissionCard]}>
      <View>
        <Typography variant="permissionTitle" align="center" style={styles(theme).permissionTitle}>
          {t('permissions.bluetoothPermissions')}
        </Typography>
        <Typography variant="body" align="center" style={styles(theme).permissionDescription}>
          {t('permissions.bluetoothDescription')}
        </Typography>
      </View>

      <View>
        <CButton
          title={t('permissions.enableBluetooth')}
          variant="primary"
          containerStyle={styles(theme).buttonContainer}
          onPress={() => requestBluetoothPermissions()}
        />
        <CButton
          title={t('permissions.noThanks')}
          containerStyle={styles(theme).buttonContainer}
          variant="secondary"
          onPress={() => {
            handlePermissionResponse('bluetooth', false);
            setPermissions({...permissions, bluetooth: true});
          }}
        />

        <View style={styles(theme).dotsContainer}>
          <View style={styles(theme).dot} />
          <View style={[styles(theme).dot, {backgroundColor: theme.colors.activeColor}]} />
          <View style={styles(theme).dot} />
        </View>
      </View>
    </View>
  );

  // Render bluetooth permission block
  const renderNotificationPermission = () => (
    <View style={styles(theme).permissionCard}>
      <View>
        <Typography variant="permissionTitle" align="center" style={styles(theme).permissionTitle}>
          {t('permissions.notificationPermissions')}
        </Typography>
        <Typography variant="body" align="center" style={styles(theme).permissionDescription}>
          {t('permissions.notificationDescription')}
        </Typography>
      </View>

      <View>
        <CButton
          title={t('permissions.enableNotifications')}
          variant="primary"
          containerStyle={styles(theme).buttonContainer}
          onPress={() => checkNotificationPermission()}
        />

        <CButton
          title={t('permissions.noThanks')}
          variant="secondary"
          containerStyle={styles(theme).buttonContainer}
          onPress={() => {
            handlePermissionResponse('notification', false);
            setPermissions({...permissions, notification: true});
          }}
        />

        <View style={styles(theme).dotsContainer}>
          <View style={styles(theme).dot} />
          <View style={styles(theme).dot} />
          <View style={[styles(theme).dot, {backgroundColor: theme.colors.activeColor}]} />
        </View>
      </View>
    </View>
  );

  // function for check location permission
  const checkLocationPermission = async () => {
    try {
      if (IOS) {
        const result = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
        switch (result) {
          case 'granted':
            setPermissions({...permissions, location: true});
            return true;
          case 'denied':
            requestLocationPermission();
            return false;
          case 'blocked':
            requestLocationPermission();
            return false;
          case 'unavailable':
            return false;
        }
      } else {
        const granted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );
        if (granted) {
          setPermissions({...permissions, location: true});
          handlePermissionResponse('location', true);
        } else {
          requestLocationPermission();
        }
      }
    } catch (error) {
      console.error('Error occurred while requesting location permission:', error);
    }
  };

  // function for request location permission
  const requestLocationPermission = async () => {
    try {
      if (IOS) {
        const result = await request(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
        switch (result) {
          case 'granted':
            setPermissions({...permissions, location: true});
            break;
          case 'denied':
            showEnablePermissionAlert();
            break;
          case 'blocked':
            showEnablePermissionAlert();
            break;
          case 'unavailable':
            break;
        }
      } else {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          setPermissions({...permissions, location: true});
          handlePermissionResponse('location', true);
        } else {
          showEnablePermissionAlert();
        }
      }
    } catch (error) {
      console.error('Error occurred while requesting location permission:', error);
    }
  };

  // function for show enable permission alert
  const showEnablePermissionAlert = () => {
    Alert.alert('Oops!', 'Application will not work properly without your location turned on.', [
      {
        text: 'Cancel',
        onPress: () => {
          console.log('Cancel Pressed');
        },
        style: 'cancel',
      },
      {
        text: 'Go to Settings',
        onPress: () => {
          if (Platform.OS === 'ios') {
            Linking.openURL('app-settings:');
          } else {
            Linking.openSettings();
          }
        },
      },
    ]);
  };

  // Function to check notification permission
  async function checkNotificationPermission() {
    const androidVersion = await DeviceInfo.getApiLevel();
    if (Platform.OS === 'android' && androidVersion >= 33) {
      try {
        const granted = await PermissionsAndroid.request('android.permission.POST_NOTIFICATIONS');

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          setPermissions({...permissions, notification: true});
          handlePermissionResponse('notification', true);
        }
      } catch (error) {
        console.log('error=====>>>>>', error);
      }

      // } else {
      //   const authStatus = await messaging().requestPermission();
      //   const enabled =
      //     authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      //     authStatus === messaging.AuthorizationStatus.PROVISIONAL;
    }
  }

  // Function to check bluetooth permission
  const requestBluetoothPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ]);
        setPermissions({...permissions, bluetooth: true});
        handlePermissionResponse('bluetooth', true);
        console.log('Permission result:', granted);
      } catch (err) {
        console.warn(err);
      }
    } else if (Platform.OS === 'ios') {
      const result = await request(PERMISSIONS.IOS.BLUETOOTH);
      setPermissions({...permissions, bluetooth: true});
      handlePermissionResponse('bluetooth', true);
      console.log('iOS Bluetooth permission:', result);
    }
  };

  return (
    <ImageBackground
      source={Images.splash}
      style={[styles(theme).background]} // Set opacity on the background only
      resizeMode="contain" // Or 'cover', depending on your image
    >
      <SafeAreaView style={[styles(theme).container]}>
        <View style={styles(theme).cardContainer}>
          {currentPermission === 'location' && renderLocationPermission()}
          {currentPermission === 'bluetooth' && renderBluetoothPermission()}
          {currentPermission === 'notification' && renderNotificationPermission()}
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

const {width, height} = Dimensions.get('window');

const styles = (theme: any) =>
  StyleSheet.create({
    background: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.black, // fallback color
    },
    container: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    cardContainer: {
      alignItems: 'center',
    },
    permissionCard: {
      backgroundColor: theme.colors.white,
      width: width * 0.75,
      borderRadius: 8,
      paddingVertical: 20,
      paddingHorizontal: 15,
      elevation: 5,
      // opacity: 0.95,
      minHeight: height * 0.4,
      justifyContent: 'space-between',
    },
    activeCard: {
      borderColor: theme.colors.primary,
      borderWidth: 2,
    },
    permissionTitle: {
      marginBottom: 4,
    },
    buttonContainer: {
      marginBottom: 5,
      height: 70,
    },
    permissionDescription: {
      color: theme.colors.black,
      marginBottom: 20,
      letterSpacing: 0,
      lineHeight: 18,
      fontWeight: 400,
    },

    dotsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginTop: 10,
    },
    dot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: theme.colors.divider,
      marginHorizontal: 3,
    },
  });

export default PermissionsScreen;
