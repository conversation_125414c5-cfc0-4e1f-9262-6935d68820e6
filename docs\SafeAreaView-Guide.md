# SafeAreaView Implementation Guide

This guide explains how to properly implement SafeAreaView in the GoRaqt app to handle the status bar area correctly on both iOS and Android.

## Overview

The app uses `react-native-safe-area-context` to handle safe area insets across different devices. This ensures that content is properly displayed and doesn't overlap with system UI elements like the status bar, notches, or navigation bars.

## Implementation Details

### Root Level Setup

The `SafeAreaProvider` is set up at the root level in `App.tsx`. This provider makes safe area insets available throughout the app.

```jsx
// App.tsx
import { SafeAreaProvider } from 'react-native-safe-area-context';

function App() {
  return (
    <SafeAreaProvider>
      {/* Rest of your app */}
    </SafeAreaProvider>
  );
}
```

### Header Component

The `Header` component uses `SafeAreaView` with the `edges={['top']}` prop to handle the top safe area (status bar). This ensures that the header content doesn't overlap with the status bar.

```jsx
// Header.tsx
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

const Header = (props) => {
  const insets = useSafeAreaInsets();

  return (
    <SafeAreaView edges={['top']} style={{backgroundColor: headerBackgroundColor}}>
      {/* Header content */}
    </SafeAreaView>
  );
};
```

### Screen Components

For screen components, use `SafeAreaView` with the `edges={['bottom', 'left', 'right']}` prop to handle the bottom, left, and right safe areas. The top edge is already handled by the Header component.

```jsx
// ScreenComponent.tsx
import { SafeAreaView } from 'react-native-safe-area-context';

const ScreenComponent = () => {
  return (
    <SafeAreaView style={{flex: 1}} edges={['bottom', 'left', 'right']}>
      <Header />
      {/* Screen content */}
    </SafeAreaView>
  );
};
```

### Global SafeAreaView Component

The app provides a global SafeAreaView component in `src/components/common/SafeAreaViewWrapper.tsx` that automatically applies the correct edges:

```jsx
// Import the global SafeAreaView component
import { SafeAreaView } from '@/components/common';

const ScreenComponent = () => {
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Header />
      {/* Screen content */}
    </SafeAreaView>
  );
};
```

By default, this component:
- Excludes the top edge (since the Header component handles it)
- Includes the bottom, left, and right edges

You can customize which edges are included:

```jsx
// For screens without a header
<SafeAreaView includeTop={true} style={{ flex: 1 }}>
  {/* Screen content without a header */}
</SafeAreaView>

// To exclude the bottom edge (rare cases)
<SafeAreaView includeBottom={false} style={{ flex: 1 }}>
  {/* Screen content */}
</SafeAreaView>

// For full control, specify edges directly
<SafeAreaView edges={['left', 'right']} style={{ flex: 1 }}>
  {/* Screen content */}
</SafeAreaView>
```

## Android-Specific Considerations

For Android, the status bar is configured in `MainActivity.kt` to be transparent with the following flags:

```kotlin
window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
        or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)

window.statusBarColor = android.graphics.Color.TRANSPARENT
```

This allows content to be drawn behind the status bar, which is why we need to use `SafeAreaView` with the proper edges to ensure content doesn't overlap with the status bar.

## Best Practices

1. Use the global `SafeAreaView` component from `@/components/common` instead of importing directly from react-native-safe-area-context.
2. For screens with a header, use the default configuration (no need to specify edges).
3. For screens without a header, use the `includeTop={true}` prop.
4. Always set the `transparent` prop explicitly in the Header component:
   - Use `transparent={false}` for solid color backgrounds
   - Use `transparent={true}` for screens with background images or gradients
5. Always provide a proper `backgroundColor` for the Header that matches your screen's background.
6. If you need more control, you can use the `edges` prop directly.
7. Use the `useSafeAreaInsets` hook from react-native-safe-area-context if you need to apply custom padding based on safe area insets.

## Examples

### Notification Screen

Here's how the Notification screen is implemented with the global SafeAreaView component:

```jsx
// src/screens/Notification/index.tsx
import { SafeAreaView } from '@/components/common';

const NotificationsListScreen = () => {
  // ...

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Notifications"
        backgroundColor={theme.colors.notificationBg}
        transparent={false}
        showBack={false}
        // Other header props...
      />
      {/* Screen content */}
    </SafeAreaView>
  );
};
```

### Share Screen (with background image)

Here's how the Share screen is implemented with a background image:

```jsx
// src/screens/tabs/ShareScreen.tsx
import { SafeAreaView } from '@/components/common';

const ShareScreen = () => {
  // ...

  return (
    <SafeAreaView style={{flex: 1}}>
      <ImageBackground source={Images.gradientBg} style={styles.backgroundImage} resizeMode="cover">
        <View style={styles.container}>
          <Header
            backgroundColor="transparent"
            transparent={true}
            // Other header props...
          />
          {/* Screen content */}
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
};
```

Key points:
- Import `SafeAreaView` from '@/components/common', not from 'react-native' or 'react-native-safe-area-context'
- No need to specify edges - the component handles this automatically
- Set `transparent={false}` on the Header for solid backgrounds
- Set `transparent={true}` on the Header for screens with background images
- Provide a proper background color for the Header that matches the screen

## Troubleshooting

If content is still appearing under the status bar:

1. Make sure `SafeAreaProvider` is at the root level of your app.
2. Check that you're using the global `SafeAreaView` from `@/components/common`.
3. Ensure you've explicitly set the `transparent` prop on the Header component:
   ```jsx
   <Header
     transparent={false}  // Set this explicitly for solid backgrounds
     backgroundColor={theme.colors.yourBackgroundColor}
   />
   ```
4. If using a background image, make sure to set `transparent={true}` on the Header:
   ```jsx
   <Header
     transparent={true}  // For background images
     backgroundColor="transparent"
   />
   ```
5. For screens without a header, make sure to use `includeTop={true}` on the SafeAreaView:
   ```jsx
   <SafeAreaView includeTop={true} style={{flex: 1}}>
     {/* Content */}
   </SafeAreaView>
   ```
6. For Android, ensure that the status bar is properly configured in `MainActivity.kt`.
