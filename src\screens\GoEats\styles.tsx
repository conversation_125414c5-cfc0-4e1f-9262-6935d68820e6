import {Dimensions, StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    root: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      paddingHorizontal: 16,
    },
    contentContainer: {
      flexGrow: 1,
      padding: 16,
    },
    actionContainer: {
      backgroundColor: theme.colors.black,
      paddingHorizontal: 30,
      paddingVertical: 5,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    cardContainer: {
      marginVertical: 7,
      marginHorizontal: 16,
    },
    tabStyle: {
      width: Dimensions.get('screen').width / 3 - 17,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 4,
      borderWidth: 0,
    },
    tabTitleStyle: {
      textAlign: 'center',
    },
    listContainerStyle: {
      paddingHorizontal: 0,
    },
    tabContainer: {
      marginTop: 10,
      marginBottom: 5,
    },
    blueButton: {
      backgroundColor: theme.colors.primary,
      padding: 12,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 0,
      marginVertical: 15,
    },
    content: {
      flexGrow: 1,
      paddingBottom: 16,
    },
    headerContainer: {
      paddingHorizontal: 16,
      paddingTop: 16,
    },
    imageContainer: {
      width: '100%',
    },
  });
