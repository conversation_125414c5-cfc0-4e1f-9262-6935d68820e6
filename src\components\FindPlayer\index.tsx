import React, {useState, useEffect, useRef} from 'react';
import {useThemeStore} from '@/store/themeStore';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import Tabs from '../Tabs';
import PlayerCard from '../PlayerCard';
import CButton from '../CButton';
import {styles as createStyles} from './styles';
import {
  BottomSheetFlatList,
  BottomSheetScrollView,
  BottomSheetTextInput,
} from '@gorhom/bottom-sheet';
import Typography from '../Typography';
import {CInput, CustomModal, Icon, RadioSelect} from '@/components';
import SearchInput from '../SearchInput';
import RatingSlider from '../RatingSlider';
import GroupCard from '../GroupCard';
import {Controller, useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {findPayerTabs} from '@/config/staticData';
import useTranslation from '@/hooks/useTranslation';

type Player = {
  id: string;
  name: string;
  rating: string;
  location: string;
  image: string;
  color?: string;
  isPremium?: boolean;
};

interface FindPlayersProps {
  onConfirm?: (players: Player[]) => void;
  initialSelectedPlayers?: Player[];
  onInvitePress?: () => void;
}

interface Group {
  id: number;
  name: string;
  members: number;
  highlighted: boolean;
  type: string;
  locked: boolean;
}

interface FormData {
  name: string;
  email: string;
  phoneNumber: string;
  rating?: number | null;
}

const FindPlayers: React.FC<FindPlayersProps> = ({
  onConfirm,
  initialSelectedPlayers = [],
  onInvitePress = () => {},
}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const {t} = useTranslation();
  const [activeTab, setActiveTab] = useState(t('findPlayer.findPlayerTabs.schedulePlay'));
  const [selectedPlayers, setSelectedPlayers] = useState<Player[]>(initialSelectedPlayers);
  const [selectedGroups, setSelectedGroups] = useState<Group[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isInviteModalVisible, setIsInviteModalVisible] = useState(false);
  const [selectedValue, setSelectedValue] = React.useState<string | null>(null);
  const [searchValueFilter, setSearchValueFilter] = useState('');

  const nameInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const phoneNumberInputRef = useRef<TextInput>(null);
  const ratingNumberInputRef = useRef<TextInput>(null);

  const clearSearch = () => {
    setSearchQuery('');
  };

  const GroupsData: Group[] = [
    {
      id: 1,
      name: 'Sunday Clinic - Fort Greene',
      members: 7,
      highlighted: false,
      type: 'group',
      locked: true,
    },
    {id: 2, name: 'Tennis Bash', members: 5, highlighted: false, type: 'group', locked: true},
    {
      id: 3,
      name: 'Crown Heights Public Group',
      members: 7,
      highlighted: false,
      type: 'group',
      locked: true,
    },
  ];

  const players: Player[] = [
    {
      id: '1',
      name: 'John Doe',
      rating: '3.5',
      location: 'Fort Greene',
      image: 'https://randomuser.me/api/portraits/men/1.jpg',
    },
    {
      id: '2',
      name: 'Jane Doe',
      rating: '3.5',
      location: 'Lincoln Terrace',
      image: 'https://randomuser.me/api/portraits/women/2.jpg',
    },
    {
      id: '3',
      name: 'Babolat',
      rating: '3.5',
      location: 'McCarren Park',
      image: 'https://randomuser.me/api/portraits/men/3.jpg',
      color: theme.colors.orange,
      isPremium: true,
    },
    {
      id: '4',
      name: 'John Doe',
      rating: '3.5',
      location: 'Fort Greene',
      image: 'https://randomuser.me/api/portraits/men/1.jpg',
    },
    {
      id: '5',
      name: 'Jane Doe',
      rating: '3.5',
      location: 'Lincoln Terrace',
      image: 'https://randomuser.me/api/portraits/women/2.jpg',
      isPremium: true,
    },
    {
      id: '6',
      name: 'Babolat',
      rating: '3.5',
      location: 'McCarren Park',
      image: 'https://randomuser.me/api/portraits/men/3.jpg',
      color: theme.colors.orange,
    },
    {
      id: '7',
      name: 'John Doe',
      rating: '3.5',
      location: 'Fort Greene',
      image: 'https://randomuser.me/api/portraits/men/1.jpg',
    },
    {
      id: '8',
      name: 'Jane Doe',
      rating: '3.5',
      location: 'Lincoln Terrace',
      image: 'https://randomuser.me/api/portraits/women/2.jpg',
    },
    {
      id: '9',
      name: 'Babolat',
      rating: '3.5',
      location: 'McCarren Park',
      image: 'https://randomuser.me/api/portraits/men/3.jpg',
      color: theme.colors.orange,
    },
  ];

  const options = [
    {label: 'Tennis', value: 'tennis'},
    {label: 'Pickleball', value: 'pickleball'},
    {label: 'Plateform Tennis', value: 'plateform-tennis'},
    {label: 'Padel', value: 'padel'},
  ];
  const options2 = [
    {label: 'Friends', value: 'friends'},
    {label: 'Invited', value: 'invited'},
    {label: 'Sponsored', value: 'sponsored'},
  ];

  // Initialize with initial players data on mount
  useEffect(() => {
    if (initialSelectedPlayers && initialSelectedPlayers.length > 0) {
      setSelectedPlayers(initialSelectedPlayers);
    }
  }, [initialSelectedPlayers]);

  const handlePlayerSelect = (player: Player) => {
    if (selectedPlayers.some(p => p.id === player.id)) {
      setSelectedPlayers(selectedPlayers.filter(p => p.id !== player.id));
    } else {
      setSelectedPlayers([...selectedPlayers, player]);
    }
  };

  const handleGroupSelect = (group: Group) => {
    if (selectedGroups.some(g => g.id === group.id)) {
      setSelectedGroups(selectedGroups.filter(g => g.id !== group.id));
    } else {
      setSelectedGroups([...selectedGroups, group]);
    }
  };

  const renderItem = ({item}: {item: any}) => {
    if (activeTab === t('findPlayer.findPlayerTabs.group')) {
      return (
        <GroupCard
          name={item.name}
          members={item.members}
          highlighted={item.highlighted}
          locked={item.locked}
          containerStyle={{
            borderWidth: 1,
            borderColor: theme.colors.divider,
          }}
          onSelect={() => handleGroupSelect(item)}
          isSelected={selectedGroups.some(g => g.id === item.id)}
        />
      );
    } else {
      return (
        <PlayerCard
          key={item.id}
          playerData={item}
          onSelect={() => handlePlayerSelect(item)}
          isSelected={selectedPlayers.some(p => p.id === item.id)}
        />
      );
    }
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm(selectedPlayers);
    }
  };

  const schema = yup.object({
    name: yup.string().required(t('findPlayer.nameRequired')),
    email: yup.string().email(t('findPlayer.emailInvalid')).required(t('findPlayer.emailRequired')),
    phoneNumber: yup.string().required(t('findPlayer.phoneRequired')),
    rating: yup.number().nullable().optional(),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
    setValue,
    watch,
  } = useForm<FormData>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      name: '',
      email: '',
      phoneNumber: '',
      rating: null,
    },
  });

  const onSubmit = (data: FormData) => {
    Keyboard.dismiss();
    setIsInviteModalVisible(true);
  };

  // Add filtered data logic
  const filteredData = React.useMemo(() => {
    if (!searchQuery.trim()) {
      return activeTab === t('findPlayer.findPlayerTabs.group') ? GroupsData : players;
    }

    const query = searchQuery.toLowerCase().trim();
    if (activeTab === t('findPlayer.findPlayerTabs.group')) {
      return GroupsData.filter(group => group.name.toLowerCase().includes(query));
    } else {
      return players.filter(
        player =>
          player.name.toLowerCase().includes(query) ||
          player.location.toLowerCase().includes(query),
      );
    }
  }, [searchQuery, activeTab]);

  return (
    <>
      <Typography variant="findTitle" color={theme.colors.text} style={{marginBottom: 10}}>
        {activeTab !== t('findPlayer.findPlayerTabs.invite')
          ? t('findPlayer.title')
          : t('findPlayer.invitePlayers')}
      </Typography>
      {activeTab !== t('findPlayer.findPlayerTabs.invite') && (
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Icon name="search-1" size={25} color={theme.colors.primary} />
            <BottomSheetTextInput
              style={styles.searchInput}
              placeholder={t('findPlayer.searchPlaceHolder')}
              placeholderTextColor={theme.colors.primary}
              onChangeText={setSearchQuery}
              value={searchQuery}
            />
            {searchQuery !== '' && (
              <TouchableOpacity onPress={clearSearch}>
                <Icon name="close" size={20} color={theme.colors.red} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

      <Tabs
        tabs={findPayerTabs.map(item => t(item.title))}
        activeTab={activeTab}
        onTabPress={tab => {
          setActiveTab(tab);
          setSelectedPlayers([]);
          setSelectedGroups([]);
        }}
        listContainerStyle={{
          marginTop: 20,
          marginBottom: 8,
        }}
      />
      {activeTab !== t('findPlayer.findPlayerTabs.invite') && (
        <View style={styles.titleContainer}>
          <Typography variant="parkTitle" color={theme.colors.text}>
            {activeTab}
          </Typography>

          <TouchableOpacity activeOpacity={0.7} onPress={() => setIsModalVisible(true)}>
            <Icon name="filter" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      )}
      {activeTab === t('findPlayer.findPlayerTabs.invite') ? (
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}>
          <BottomSheetScrollView
            contentContainerStyle={styles.scrollContainer}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}>
            <View style={styles.form}>
              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="name"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('findPlayer.name')}
                      variant="dark"
                      showLabel={false}
                      placeholder={t('findPlayer.namePlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.name}
                      error={errors.name?.message}
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={nameInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => emailInputRef.current?.focus()}
                      blurOnSubmit={false}
                      useBottomSheetInput={true}
                    />
                  )}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="email"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('findPlayer.email')}
                      showLabel={false}
                      variant="dark"
                      placeholder={t('findPlayer.emailPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.email}
                      error={errors.email?.message}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      inputStyle={[styles.input]}
                      containerStyle={{marginBottom: 0}}
                      ref={emailInputRef}
                      returnKeyType="next"
                      onSubmitEditing={() => phoneNumberInputRef.current?.focus()}
                      blurOnSubmit={false}
                      useBottomSheetInput={true}
                    />
                  )}
                />
              </View>

              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="phoneNumber"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('findPlayer.phoneNumber')}
                      showLabel={false}
                      variant="dark"
                      placeholder={t('findPlayer.phonePlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      hasError={!!errors.phoneNumber}
                      error={errors.phoneNumber?.message}
                      keyboardType="numeric"
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={phoneNumberInputRef}
                      returnKeyType="done"
                      blurOnSubmit={false}
                      useBottomSheetInput={true}
                      onSubmitEditing={() => ratingNumberInputRef.current?.focus()}
                      maxLength={10}
                    />
                  )}
                />
              </View>
              <View style={styles.inputContainer}>
                <Controller
                  control={control}
                  name="rating"
                  render={({field: {onChange, onBlur, value}}) => (
                    <CInput
                      label={t('findPlayer.rating')}
                      showLabel={false}
                      variant="dark"
                      placeholder={t('findPlayer.ratingPlaceholder')}
                      placeholderTextColor={theme.colors.placeholder}
                      value={value?.toString() || ''}
                      onChangeText={text => onChange(text ? Number(text) : null)}
                      onBlur={onBlur}
                      hasError={!!errors.rating}
                      error={errors.rating?.message}
                      keyboardType="numeric"
                      inputStyle={styles.input}
                      containerStyle={{marginBottom: 0}}
                      ref={ratingNumberInputRef}
                      returnKeyType="done"
                      blurOnSubmit={false}
                      useBottomSheetInput={true}
                      onSubmitEditing={() => handleSubmit(onSubmit)()}
                    />
                  )}
                />
              </View>
              <CButton
                title={t('findPlayer.findPlayerTabs.invite')}
                variant="primary"
                onPress={handleSubmit(onSubmit)}
                containerStyle={styles.inviteBtn}
                textStyle={styles.inviteBtnText}
              />
            </View>
          </BottomSheetScrollView>
        </KeyboardAvoidingView>
      ) : (
        <BottomSheetFlatList
          data={filteredData}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          bounces={false}
          contentContainerStyle={styles.scrollView}
          scrollEnabled={true}
          nestedScrollEnabled={true}
          keyboardShouldPersistTaps="handled"
          style={styles.listContainer}
          onEndReached={() => console.log('Hello')}
        />
      )}

      {activeTab === t('findPlayer.findPlayerTabs.invite') ? null : (
        <CButton
          containerStyle={{
            marginBottom: 20,
          }}
          title={`${t('findPlayer.accept')} ${
            selectedPlayers.length > 0 ? `(${selectedPlayers.length})` : ''
          }`}
          onPress={handleConfirm}
          isDisabled={
            activeTab === t('findPlayer.findPlayerTabs.group')
              ? selectedGroups.length === 0
              : selectedPlayers.length === 0
          }
        />
      )}

      <CustomModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        variant="bottom"
        showCloseButton={true}
        title={t('invitePlayers.filterResults')}>
        <View>
          <Typography variant="subtitle" style={{color: theme.colors.offWhite}}>
            Show
          </Typography>
          {options.map(option => (
            <RadioSelect
              key={option.value}
              label={option.label}
              selected={selectedValue === option.value}
              onPress={() => setSelectedValue(option.value)}
            />
          ))}

          {/* Rating Slider */}
          <RatingSlider />

          {options2.map(option => (
            <RadioSelect
              key={option.value}
              label={option.label}
              selected={selectedValue === option.value}
              onPress={() => setSelectedValue(option.value)}
            />
          ))}

          <SearchInput
            variant="light"
            placeholder={t('findPlayer.searchEvent')}
            containerStyle={styles.filterInput}
            inputStyle={{color: theme.colors.primary}}
            value={searchValueFilter}
            placeholderTextColor={theme.colors.primary}
            onChangeText={setSearchValueFilter}
            iconColor={theme.colors.primary}
            onClear={() => setSearchValueFilter('')}
          />
          <CButton
            title={`80 ${t('RacquetSelectorDetail.results')}`}
            onPress={() => setIsModalVisible(false)}
            variant="primary"
          />
        </View>
      </CustomModal>

      <CustomModal
        visible={isInviteModalVisible}
        onClose={() => setIsInviteModalVisible(false)}
        modalContainerStyle={{
          justifyContent: 'center',
          alignItems: 'center',
          minWidth: '85%',
          paddingHorizontal: 40,
          paddingTop: 40,
        }}>
        <View style={{alignItems: 'center', gap: 25, width: '100%'}}>
          <Icon name="arrow" size={150} color={theme.colors.activeColor} />
          <Typography variant="subtitle" style={{textAlign: 'center', color: theme.colors.text}}>
            {t('common.inviteSent')}
          </Typography>
          <CButton
            title={t('common.inviteMore')}
            onPress={() => setIsInviteModalVisible(false)}
            variant="pill"
            containerStyle={{width: '100%', backgroundColor: theme.colors.activeColor}}
            textStyle={{color: theme.colors.black}}
          />
          <CButton
            title={t('common.close')}
            onPress={() => setIsInviteModalVisible(false)}
            variant="outline"
            containerStyle={{
              width: '100%',
              borderWidth: 0,
              borderColor: theme.colors.text,
            }}
          />
        </View>
      </CustomModal>
    </>
  );
};

export default FindPlayers;
