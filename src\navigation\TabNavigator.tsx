import React, {useEffect, useState} from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {useThemeStore} from '@/store/themeStore';
import {ShareScreen, RacquetsScreen} from '@screens/tabs';
import {Icon} from '@/components';
import {Keyboard, Platform} from 'react-native';
import CommunityStack from './CommunityStack';
import SearchStack from './SearchStack';
import TennisBallsReservation from '@/screens/tabs/TennisBallsReservation';
import {SafeAreaView} from 'react-native-safe-area-context';
import {getTabBarVisibility, HIDE_TAB_SCREENS} from '@/utils/navigationUtils';
import {useTranslationContext} from '@/context/TranslationContext';

// Define tab icons outside the render function to avoid recreation on each render
// These are memoized components to prevent unnecessary re-renders
const SearchIcon = React.memo(({color, size}: {color: string; size: number}) => (
  <Icon name="search-1" color={color} size={size} />
));

const RacquetsIcon = React.memo(({color, size}: {color: string; size: number}) => (
  <Icon name="RACQUETS-1" color={color} size={size} />
));

const BallsIcon = React.memo(({color, size}: {color: string; size: number}) => (
  <Icon name="ball" color={color} size={size} />
));

const CommunityIcon = React.memo(({color, size}: {color: string; size: number}) => (
  <Icon name="groupicon-1" color={color} size={size + 7} />
));

const KioskIcon = React.memo(({color, size}: {color: string; size: number}) => (
  <Icon name="kiosk" color={color} size={size} />
));

// Factory functions to create tab icon renderers
const createSearchIconRenderer =
  (activeColor: string, inactiveColor: string) =>
  ({focused, size}: {focused: boolean; size: number}) => (
    <SearchIcon color={focused ? activeColor : inactiveColor} size={size} />
  );

const createRacquetsIconRenderer =
  (activeColor: string, inactiveColor: string) =>
  ({focused, size}: {focused: boolean; size: number}) => (
    <RacquetsIcon color={focused ? activeColor : inactiveColor} size={size} />
  );

const createBallsIconRenderer =
  (activeColor: string, inactiveColor: string) =>
  ({focused, size}: {focused: boolean; size: number}) => (
    <BallsIcon color={focused ? activeColor : inactiveColor} size={size} />
  );

const createCommunityIconRenderer =
  (activeColor: string, inactiveColor: string) =>
  ({focused, size}: {focused: boolean; size: number}) => (
    <CommunityIcon color={focused ? activeColor : inactiveColor} size={size} />
  );

const createKioskIconRenderer =
  (activeColor: string, inactiveColor: string) =>
  ({focused, size}: {focused: boolean; size: number}) => (
    <KioskIcon color={focused ? activeColor : inactiveColor} size={size} />
  );

export type TabParamList = {
  SEARCH: undefined;
  RACQUETS: undefined;
  BALLS: undefined;
  COMMUNITY: {
    screen?: string;
    params?: Record<string, unknown>;
  };
  KIOSK: undefined;
};

const Tab = createBottomTabNavigator<TabParamList>();

// Use _route to indicate it's intentionally unused
const TennisScreenWithBottomSheet = ({route}: {route: unknown}) => (
  <>
    {/* <TennisScreen /> */}
    <TennisBallsReservation route={route} />
    {/* <BottomSheetComp>
      <Text>Tennis Sheet 🎾</Text>
    </BottomSheetComp> */}
  </>
);

// Use _route to indicate it's intentionally unused
const ShareScreenWithBottomSheet = ({route: _route}: {route: unknown}) => <ShareScreen />;

const TabNavigator = () => {
  const {t} = useTranslationContext();
  const theme = useThemeStore();
  const activeIconColor = theme.colors.primary; // Lime green for icons
  const activeTextColor = theme.colors.white; // Blue for text
  const inactiveColor = theme.colors.Gunmetal;
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Create tab icon renderers once per component instance
  const searchIconRenderer = React.useMemo(
    () => createSearchIconRenderer(activeIconColor, inactiveColor),
    [activeIconColor, inactiveColor],
  );

  const racquetsIconRenderer = React.useMemo(
    () => createRacquetsIconRenderer(activeIconColor, inactiveColor),
    [activeIconColor, inactiveColor],
  );

  const ballsIconRenderer = React.useMemo(
    () => createBallsIconRenderer(activeIconColor, inactiveColor),
    [activeIconColor, inactiveColor],
  );

  const communityIconRenderer = React.useMemo(
    () => createCommunityIconRenderer(activeIconColor, inactiveColor),
    [activeIconColor, inactiveColor],
  );

  const kioskIconRenderer = React.useMemo(
    () => createKioskIconRenderer(activeIconColor, inactiveColor),
    [activeIconColor, inactiveColor],
  );

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      },
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  return (
    <SafeAreaView
      edges={['bottom']}
      style={{
        flex: 1,
        backgroundColor: theme.colors.background,
      }}>
      <Tab.Navigator
        screenOptions={() => ({
          headerShown: false,
          // ✅ Enable gestures for tab navigator
          gestureEnabled: true,
          gestureDirection: 'horizontal',
          tabBarStyle: {
            backgroundColor: theme.colors.background,
            borderTopColor: theme.colors.white1,
            height: 70, // Standard height without insets
            paddingBottom: 8,
            paddingTop: 8,
            display: keyboardVisible ? 'none' : 'flex',
          },
          tabBarActiveTintColor: activeTextColor, // This affects text color
          tabBarInactiveTintColor: inactiveColor,
          tabBarLabelStyle: {
            fontSize: 10,
            fontWeight: 700,
          },
        })}>
        <Tab.Screen
          name={t('common.SEARCH') as keyof TabParamList}
          component={SearchStack}
          options={{
            tabBarIcon: searchIconRenderer,
          }}
        />
        <Tab.Screen
          name={t('equipment.racquets') as keyof TabParamList}
          // name="RACQUETS"
          component={RacquetsScreen}
          options={{
            tabBarIcon: racquetsIconRenderer,
          }}
        />
        <Tab.Screen
          name={t('equipment.balls') as keyof TabParamList}
          // name="BALLS"
          component={TennisScreenWithBottomSheet}
          options={{
            tabBarIcon: ballsIconRenderer,
          }}
        />
        <Tab.Screen
          name={t('common.community') as keyof TabParamList}
          // name="COMMUNITY"
          component={CommunityStack}
          options={({route}) => {
            // Default tab bar style when visible
            const defaultTabBarStyle = {
              backgroundColor: theme.colors.background,
              borderTopColor: theme.colors.white1,
              height: 70,
              paddingBottom: 8,
              paddingTop: 8,
              display: keyboardVisible ? 'none' : 'flex',
            };

            return {
              tabBarIcon: communityIconRenderer,
              // Use the utility function to determine tab bar visibility
              tabBarStyle: getTabBarVisibility(
                route,
                HIDE_TAB_SCREENS.getAllHiddenScreens(),
                keyboardVisible,
                defaultTabBarStyle,
              ),
            };
          }}
        />
        <Tab.Screen
          name={t('common.kiosk') as keyof TabParamList}
          // name="KIOSK"
          component={ShareScreenWithBottomSheet}
          options={{
            tabBarIcon: kioskIconRenderer,
          }}
        />
      </Tab.Navigator>
    </SafeAreaView>
  );
};

export default TabNavigator;
