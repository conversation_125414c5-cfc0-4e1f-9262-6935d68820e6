@echo off
echo This script will create a virtual drive pointing to your project folder.
echo This helps avoid the 260 character path limitation in Windows.
echo.
echo Current project path: %CD%
echo.
echo Please enter the drive letter to use (e.g., X):
set /p drive_letter=

if "%drive_letter%"=="" (
    echo No drive letter specified. Using X as default.
    set drive_letter=X
)

echo.
echo Creating virtual drive %drive_letter%: pointing to %CD%...
subst %drive_letter%: "%CD%"

echo.
echo Virtual drive %drive_letter%: has been created.
echo.
echo Please navigate to %drive_letter%:\ and try building the app from there.
echo.
echo To remove the virtual drive later, use: subst %drive_letter%: /d
echo.
pause
