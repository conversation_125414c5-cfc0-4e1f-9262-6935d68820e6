import React, {useState} from 'react';
import {TouchableOpacity, ScrollView} from 'react-native';
import {useThemeStore} from '@/store';
import Typography from '@/components/Typography';
import {createStyles} from './styles';
import {CustomModal} from '@/components';
import InvitePlayers from '@/components/Drawer/AssistantCoach/InvitePlayers';
import EditCoachProfile from '../EditCoachProfile';
import CalendarScreen from '../Calendar';
import ManageClasses from '../ManageClasses';
import ManagePlayerAssetsScreen from '../ManagePlayerAssets';
import ManageServiceRequest from '../ManageServiceRequest';
import ManageContent from '../ManageContent';
import PostAnAd from '../PostAnAd';
import OrderReserveEquipmentScreen from '../OrderReserveEquipment';
import GetCertified from '../GetCertified';
import MessageNotificationPreferences from '../MessageNotificationPreferences';
import useTranslation from '@/hooks/useTranslation';

type RootDrawerParamList = {
  welcome: undefined;
  InvitePlayers: undefined;
  CoachOptions: undefined;
  EditCoachProfile: undefined;
  Calendar: undefined;
  ManageClasses: undefined;
  ManagePlayerAssets: undefined;
  ManageServiceRequest: undefined;
  ManageContent: undefined;
  PostAnAd: undefined;
  OrderReserveEquipment: undefined;
  getCertified: undefined;
  MessageNotificationPreferences: undefined;
};

const CoachOptionsScreen = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const [isModalVisible, setIsModalVisible] = useState<{visible: boolean; type: string}>({
    visible: false,
    type: '',
  });

  const {t} = useTranslation();

  const options: Array<{
    id: number;
    title: string;
    screen: keyof RootDrawerParamList;
  }> = [
    {
      id: 1,
      title: t('assistantCoachOptions.editProfile'),
      screen: 'EditCoachProfile',
    },
    {
      id: 2,
      title: t('assistantCoachOptions.calendar'),
      screen: 'Calendar',
    },
    {
      id: 3,
      title: t('assistantCoachOptions.manageClasses'),
      screen: 'ManageClasses',
    },
    {
      id: 4,
      title: t('assistantCoachOptions.managePlayerAssets'),
      screen: 'ManagePlayerAssets',
    },
    {
      id: 5,
      title: t('assistantCoachOptions.manageServiceRequest'),
      screen: 'ManageServiceRequest',
    },
    {
      id: 6,
      title: t('assistantCoachOptions.manageContent'),
      screen: 'ManageContent',
    },
    {
      id: 7,
      title: t('assistantCoachOptions.postAnAd'),
      screen: 'PostAnAd',
    },
    {
      id: 8,
      title: t('assistantCoachOptions.orderReserveEquipment'),
      screen: 'OrderReserveEquipment',
    },
    {
      id: 9,
      title: t('assistantCoachOptions.getCertified'),
      screen: 'getCertified',
    },
    {
      id: 10,
      title: t('assistantCoachOptions.messageNotificationPreferences'),
      screen: 'MessageNotificationPreferences',
    },
    {
      id: 11,
      title: t('assistantCoachOptions.invitePlayers'),
      screen: 'InvitePlayers',
    },
  ];
  const renderModalContent = (type: string) => {
    if (type === 'InvitePlayers') {
      return <InvitePlayers onClose={() => setIsModalVisible({visible: false, type: ''})} />;
    } else if (type === 'EditCoachProfile') {
      return <EditCoachProfile onClose={() => setIsModalVisible({visible: false, type: ''})} />;
    } else if (type === 'Calendar') {
      return <CalendarScreen onClose={() => setIsModalVisible({visible: false, type: ''})} />;
    } else if (type === 'ManageClasses') {
      return <ManageClasses onClose={() => setIsModalVisible({visible: false, type: ''})} />;
    } else if (type === 'ManagePlayerAssets') {
      return (
        <ManagePlayerAssetsScreen onClose={() => setIsModalVisible({visible: false, type: ''})} />
      );
    } else if (type === 'ManageServiceRequest') {
      return <ManageServiceRequest onClose={() => setIsModalVisible({visible: false, type: ''})} />;
    } else if (type === 'ManageContent') {
      return <ManageContent onClose={() => setIsModalVisible({visible: false, type: ''})} />;
    } else if (type === 'PostAnAd') {
      return <PostAnAd onClose={() => setIsModalVisible({visible: false, type: ''})} />;
    } else if (type === 'OrderReserveEquipment') {
      return (
        <OrderReserveEquipmentScreen
          onClose={() => setIsModalVisible({visible: false, type: ''})}
        />
      );
    } else if (type === 'getCertified') {
      return <GetCertified onClose={() => setIsModalVisible({visible: false, type: ''})} />;
    } else if (type === 'MessageNotificationPreferences') {
      return (
        <MessageNotificationPreferences
          onClose={() => setIsModalVisible({visible: false, type: ''})}
        />
      );
    }
  };

  return (
    <ScrollView contentContainerStyle={styles.scrollContainer}>
      <Typography variant="invitePlayersTitle" color={theme.colors.white}>
        {t('assistantCoachOptions.title')}
      </Typography>
      {options.map((option, idx) => (
        <TouchableOpacity
          key={idx}
          onPress={() => {
            setIsModalVisible({
              visible: true,
              type: option?.screen,
            });
          }}>
          <Typography variant="bodyMedium" color={theme.colors.activeColor}>
            {option?.title}
          </Typography>
        </TouchableOpacity>
      ))}
      <CustomModal
        animationType="slide"
        variant="bottom"
        visible={isModalVisible.visible}
        onClose={() => {
          setIsModalVisible({
            visible: false,
            type: '',
          });
        }}
        modalContainerStyle={{height: '100%'}}
        showCloseButtonRight={true}
        imageBg={true}>
        {renderModalContent(isModalVisible.type)}
      </CustomModal>
    </ScrollView>
  );
};

export default CoachOptionsScreen;
