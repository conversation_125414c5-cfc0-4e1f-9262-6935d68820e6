import React, {memo} from 'react';
import {RefreshControl as RNRefreshControl, RefreshControlProps} from 'react-native';
import {useThemeStore} from '@/store/themeStore';

interface CustomRefreshControlProps extends RefreshControlProps {
  refreshing: boolean;
  onRefresh: () => void;
  colors?: string[];
}

const RefreshControl: React.FC<CustomRefreshControlProps> = ({
  refreshing,
  onRefresh,
  colors,
  ...rest
}) => {
  const theme = useThemeStore();

  return (
    <RNRefreshControl
      refreshing={refreshing}
      onRefresh={onRefresh}
      colors={colors || [theme.colors.primary]}
      tintColor={theme.colors.primary}
      {...rest}
    />
  );
};

// Use React.memo to prevent unnecessary re-renders
export default memo(RefreshControl, (prevProps, nextProps) => {
  // Only re-render if refreshing state changes
  return (
    prevProps.refreshing === nextProps.refreshing && prevProps.onRefresh === nextProps.onRefresh
  );
});
