import {Platform} from 'react-native';

// Define connectivity levels
export enum ConnectivityLevel {
  NONE = 'none',
  POOR = 'poor',
  GOOD = 'good',
  EXCELLENT = 'excellent',
  UNKNOWN = 'unknown',
}

// Define network types
export enum NetworkType {
  WIFI = 'wifi',
  CELLULAR = 'cellular',
  ETHERNET = 'ethernet',
  UNKNOWN = 'unknown',
  NONE = 'none',
}

// Define the network info state
export interface NetworkStatus {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: NetworkType;
  connectivityLevel: ConnectivityLevel;
  details: any;
  timestamp: number;
}

// Define listeners
export type NetworkStatusListener = (status: NetworkStatus) => void;

// Singleton class to manage network status
class NetworkService {
  private static instance: NetworkService;
  private listeners: Set<NetworkStatusListener> = new Set();
  private lastStatus: NetworkStatus = {
    isConnected: true,
    isInternetReachable: true,
    type: NetworkType.UNKNOWN,
    connectivityLevel: ConnectivityLevel.GOOD,
    details: null,
    timestamp: Date.now(),
  };

  private constructor() {}

  public static getInstance(): NetworkService {
    if (!NetworkService.instance) {
      NetworkService.instance = new NetworkService();
    }
    return NetworkService.instance;
  }

  // Initialize the service
  public initialize(): void {
    // For now, we'll assume we're always connected
    this.updateStatus({
      isConnected: true,
      isInternetReachable: true,
      type: NetworkType.UNKNOWN,
      connectivityLevel: ConnectivityLevel.GOOD,
      details: null,
      timestamp: Date.now(),
    });
  }

  // Update status and notify listeners
  private updateStatus(newStatus: NetworkStatus): void {
    this.lastStatus = newStatus;
    this.notifyListeners();
  }

  // Notify all listeners
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      listener(this.lastStatus);
    });
  }

  // Add a listener
  public addListener(listener: NetworkStatusListener): () => void {
    this.listeners.add(listener);
    // Immediately notify the new listener of current status
    listener(this.lastStatus);
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  // Get current status
  public getCurrentStatus(): NetworkStatus {
    return this.lastStatus;
  }

  // Check connectivity
  public async checkConnectivityNow(): Promise<boolean> {
    // For now, we'll assume we're always connected
    return true;
  }

  // Set app state
  public setAppState(isInBackground: boolean): void {
    // No-op for now
  }

  // Cleanup
  public cleanup(): void {
    this.listeners.clear();
  }
}

export default NetworkService;
