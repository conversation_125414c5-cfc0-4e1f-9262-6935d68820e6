import React from 'react';
import {StyleSheet, View, TouchableOpacity, Text} from 'react-native';
import Typography from '../Typography';
import CImage from '../CImage';
import {useThemeStore} from '@/store/themeStore';
import {Icon} from '@/components';

interface PlayerProfileCardProps {
  name: string;
  description?: string;
  primaryCourts?: string;
  avatar?: any;
  isPremium?: boolean;
  onPress?: () => void;
}

const PlayerProfileCard: React.FC<PlayerProfileCardProps> = ({
  name,
  description = 'Tennis',
  primaryCourts,
  avatar,
  isPremium = false,
  onPress,
}) => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  return (
    <TouchableOpacity style={styles.container} activeOpacity={0.8} onPress={onPress}>
      <CImage source={avatar} resizeMode="cover" style={styles.avatar} />
      <View style={styles.contentContainer}>
        <View style={styles.nameContainer}>
          <Typography variant="profileCardTitle" style={styles.name}>
            {name}{' '}
            {description && (
              <Typography variant="coachType" style={styles.description}>
                .{description}
              </Typography>
            )}
          </Typography>
        </View>
        {primaryCourts && (
          <View style={styles.locationContainer}>
            <Typography variant="court" style={styles.location}>
              Primary courts: <Typography variant="body">{primaryCourts}</Typography>
            </Typography>
            <TouchableOpacity style={styles.learnMoreLink}>
              <Typography variant="moreText" style={styles.learnMoreText}>
                Learn more {'>>'}
              </Typography>
            </TouchableOpacity>
          </View>
        )}
      </View>
      {isPremium && <Icon name="badge" size={30} color={theme.colors.activeColor} />}
    </TouchableOpacity>
  );
};

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      flexDirection: 'row',
      backgroundColor: 'transparent',
      borderRadius: 8,
      alignItems: 'center',
      gap: 16,
    },

    avatar: {
      width: 70,
      height: 70,
      borderRadius: 100,
    },
    contentContainer: {
      flex: 1,
      justifyContent: 'center',
    },
    nameContainer: {
      marginBottom: 4,
    },
    name: {
      color: theme.colors.white,
    },
    description: {
      color: theme.colors.silver,
      fontSize: 12,
    },
    locationContainer: {
      flexDirection: 'column',
    },
    location: {
      color: theme.colors.white,
      fontSize: 12,
    },
    learnMoreLink: {
      marginTop: 2,
    },
    learnMoreText: {
      color: theme.colors.activeColor,
      fontSize: 12,
    },
    premiumBadge: {
      position: 'absolute',
      top: 8,
      right: 8,
      backgroundColor: theme.colors.activeColor,
      borderRadius: 12,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

export default PlayerProfileCard;
