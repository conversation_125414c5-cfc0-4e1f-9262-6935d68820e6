import React from 'react';
import {View} from 'react-native';
import Typography from '@/components/Typography';
import {useThemeStore} from '@/store';
import CButton from '@/components/CButton';
import {createStyles} from './styles';
import useTranslation from '@/hooks/useTranslation';

const WelcomeScreen = (props: any) => {
  const {onPress} = props;
  const theme = useThemeStore();
  const styles = createStyles(theme);

  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <Typography variant="invitePlayersTitle" style={styles.title}>
        {t('assistantCoach.welcome')}
      </Typography>
      <Typography variant="subtitle" style={styles.subtitle}>
        {t('assistantCoach.subtitle')}
      </Typography>
      <CButton
        title={t('common.next')}
        onPress={onPress}
        containerStyle={styles.button}
        textStyle={styles.buttonText}
      />
    </View>
  );
};

export default WelcomeScreen;
