import {StyleSheet} from 'react-native';

const createStyles = (theme: any) =>
  StyleSheet.create({
    modalContainer: {
      flexGrow: 1,
      paddingBottom: 10,
      paddingHorizontal: 16,
      backgroundColor: theme.colors.background,
    },
    bottomSheetHeader: {
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
    },
    filterTitle: {
      color: theme.colors.activeColor,
    },
    sectionTitle: {
      marginBottom: 5,
      color: theme.colors.offWhite,
    },
    radioOption: {
      marginBottom: 8,
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.white2,
      marginVertical: 15,
      width: '90%',
      alignSelf: 'center',
    },
    applyButton: {
      backgroundColor: theme.colors.activeColor,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 25,
      alignSelf: 'center',
      marginTop: 24,
      marginBottom: 32,
    },
    applyButtonText: {
      color: theme.colors.black,
      fontSize: 16,
      fontWeight: '600',
    },
  });

export default createStyles;
