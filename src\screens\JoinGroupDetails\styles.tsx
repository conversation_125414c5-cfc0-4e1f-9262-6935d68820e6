import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
      paddingHorizontal: 16,
    },
    cancel: {
      color: theme.colors.activeColor,
    },
    next: {
      color: theme.colors.dimGray,
    },
    scrollView: {
      flexGrow: 1,
      paddingHorizontal: 16,
    },
    groupInfoCard: {
      width: '100%',
      flexDirection: 'row',
      gap: 16,
      marginBottom: 16,
    },
    avatarContainer: {
      flexDirection: 'row',
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      gap: 16,
    },
    groupInfoContent: {
      flex: 1,
      justifyContent: 'center',
    },
    avatar: {
      width: 50,
      height: 50,
      borderRadius: 50,
    },
    groupName: {
      color: theme.colors.text,
    },
    members: {
      color: theme.colors.text,
    },
    locationRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    location: {
      color: theme.colors.text,
    },

    joinText: {
      color: theme.colors.text,
    },
    description: {
      color: theme.colors.text,
    },
    playerList: {
      marginTop: 16,
      flexGrow: 1,
      gap: 16,
      paddingBottom: 30,
    },
  });
