import React, {useState, useRef} from 'react';
import {View, Text, StyleSheet, FlatList, TouchableOpacity, Dimensions} from 'react-native';
import {Icon} from '@/components';
import {useThemeStore} from '@/store';
import Typography from '@/components/Typography';

// Define types for the grip size item
interface GripSizeItem {
  id: string;
  size: string;
}

// Define type for scrollToIndex direction
type ScrollDirection = 'left' | 'right';

// Define component props
interface GripSizeSelectorProps {
  gripSizes?: GripSizeItem[];
  defaultSelectedId?: string;
  onSelect?: (item: GripSizeItem) => void;
}

const defaultGripSizes: GripSizeItem[] = [
  {id: '1', size: '4 1/4'},
  {id: '2', size: '4 3/8'},
  {id: '3', size: '4 1/2'},
  {id: '4', size: '4 5/8'},
  {id: '5', size: '4 3/4'},
  {id: '6', size: '5'},
  {id: '7', size: '5 1/8'},
  {id: '8', size: '5 1/4'},
];

const GripSizeSelector = ({
  gripSizes = defaultGripSizes,
  defaultSelectedId = '2',
  onSelect,
}: GripSizeSelectorProps) => {
  const theme = useThemeStore();

  const [selectedId, setSelectedId] = useState(defaultSelectedId);
  const flatListRef = useRef<FlatList<GripSizeItem>>(null);
  const viewConfigRef = useRef({itemVisiblePercentThreshold: 50});

  // Find the initial index based on defaultSelectedId
  const initialIndex = gripSizes.findIndex(item => item.id === defaultSelectedId);
  const [currentIndex, setCurrentIndex] = useState(initialIndex !== -1 ? initialIndex : 0);

  // Handle item selection
  const handleSelect = (id: string, index: number) => {
    setSelectedId(id);
    setCurrentIndex(index);
    // Call the onSelect callback if provided
    if (onSelect) {
      onSelect(gripSizes[index]);
    }
  };

  // Scroll handling with arrows
  const scrollToIndex = (direction: ScrollDirection) => {
    let newIndex = direction === 'left' ? currentIndex - 1 : currentIndex + 1;

    // Ensure index is within bounds
    if (newIndex < 0) newIndex = 0;
    if (newIndex >= gripSizes.length) newIndex = gripSizes.length - 1;

    // Only perform scroll if index changed
    if (newIndex !== currentIndex) {
      flatListRef.current?.scrollToIndex({
        index: newIndex,
        animated: true,
        viewPosition: 0.5, // Center the item
      });
      setCurrentIndex(newIndex);
      setSelectedId(gripSizes[newIndex].id);

      // Call the onSelect callback if provided
      if (onSelect) {
        onSelect(gripSizes[newIndex]);
      }
    }
  };

  // Handle scroll failure (e.g., when trying to scroll to an item not rendered yet)
  const handleScrollToIndexFailed = (info: {index: number; averageItemLength: number}) => {
    const wait = new Promise(resolve => setTimeout(resolve, 500));
    wait.then(() => {
      flatListRef.current?.scrollToIndex({
        index: info.index,
        animated: true,
        viewPosition: 0.5,
      });
    });
  };

  // Render each grip size item
  const renderItem = ({item, index}: {item: GripSizeItem; index: number}) => (
    <TouchableOpacity
      style={[styles(theme).itemContainer]}
      onPress={() => handleSelect(item.id, index)}
      activeOpacity={0.7}>
      <Typography
        style={[
          styles(theme).sizeText,
          item.id === selectedId ? styles(theme).selectedText : styles(theme).unselectedText,
        ]}>
        {item.size}
      </Typography>
    </TouchableOpacity>
  );

  return (
    <View style={styles(theme).container}>
      <Typography style={styles(theme).label}>Select grip size</Typography>
      <View style={styles(theme).selectorContainer}>
        {/* Left Arrow */}
        <TouchableOpacity
          style={[
            styles(theme).arrowButton,
            currentIndex === 0 ? styles(theme).arrowDisabled : null,
          ]}
          onPress={() => scrollToIndex('left')}
          disabled={currentIndex === 0}>
          <Icon name="Left-chevron" size={28} color={theme.colors.dimGray} />
        </TouchableOpacity>

        {/* FlatList for grip sizes */}
        <FlatList
          ref={flatListRef}
          data={gripSizes}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles(theme).listContainer}
          initialScrollIndex={currentIndex}
          onScrollToIndexFailed={handleScrollToIndexFailed}
          getItemLayout={(data, index) => ({length: 100, offset: 100 * index, index})}
          viewabilityConfig={viewConfigRef.current}
          pagingEnabled={false}
          snapToAlignment="center"
          snapToInterval={100} // Approximate item width + margins
        />

        {/* Right Arrow */}
        <TouchableOpacity
          style={[
            styles(theme).arrowButton,
            currentIndex === gripSizes.length - 1 ? styles(theme).arrowDisabled : null,
          ]}
          onPress={() => scrollToIndex('right')}
          disabled={currentIndex === gripSizes.length - 1}>
          <Icon name="Right-chevron" size={28} color={theme.colors.dimGray} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: 'transparent',
      width: '100%',
    },
    label: {
      color: theme.colors.activeColor,
      fontSize: 14,
      fontWeight: '500',
    },
    selectorContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    listContainer: {
      alignItems: 'center',
    },
    itemContainer: {
      paddingVertical: 10,
      paddingHorizontal: 20,
      marginHorizontal: 5,
      borderRadius: 4,
      width: 90,
      alignItems: 'center',
      justifyContent: 'center',
      height: 44,
    },

    sizeText: {
      fontSize: 16,
      fontWeight: '500',
    },
    selectedText: {
      color: theme.colors.text,
    },
    unselectedText: {
      color: theme.colors.gray,
    },
    arrowButton: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    arrowDisabled: {
      opacity: 0.3,
    },
  });

export default GripSizeSelector;
