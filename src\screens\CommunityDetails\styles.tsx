import {Dimensions, StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    root: {
      flex: 1,
    },
    content: {
      flexGrow: 1,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    headerContainer: {
      paddingHorizontal: 16,
    },
    contentContainer: {
      marginTop: 10,
      flex: 1,
    },
    contentText: {
      color: theme.colors.white,
      lineHeight: 21,
      marginBottom: 16,
      paddingHorizontal: 16,
    },
    ctaButton: {
      backgroundColor: theme.colors.primary, // Blue color for the button
      padding: 12,
      alignItems: 'center',
      borderRadius: 4,
      marginVertical: 16,
    },
    ctaButtonText: {
      color: theme.colors.white,
      fontSize: 16,
      fontWeight: 'bold',
    },
    blueButton: {
      backgroundColor: theme.colors.primary,
      padding: 12,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 0,
      marginVertical: 15,
      width: Dimensions.get('screen').width,
    },
    container: {
      flex: 1,
    },
    pageTitleContainer: {
      paddingHorizontal: 16,
      marginTop: 10,
      marginBottom: 20,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
  });
