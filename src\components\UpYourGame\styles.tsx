import {Dimensions, StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    backgroundImage: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    root: {
      flex: 1,
    },
    menuButton: {
      width: 44,
      height: 44,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    container: {
      flex: 1,
      // backgroundColor: theme.colors.background,
      paddingHorizontal: 16,
    },
    contentContainer: {
      flexGrow: 1,
      padding: 16,
    },
    cardContainer: {
      marginVertical: 7,
      marginHorizontal: 16,
    },
    content: {
      flexGrow: 1,
      paddingBottom: 16,
    },
    headerContainer: {
      paddingHorizontal: 16,
      paddingTop: 16,
      marginBottom: 7,
    },
    imageContainer: {
      height: '100%',
      width: '100%',
    },
  });
