import React, {useRef} from 'react';
import {View, Dimensions} from 'react-native';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RacquetStackParamList} from '@/navigation/RacquetStack';
import {useThemeStore} from '@/store/themeStore';
import {createStyles} from './styles';
import {Header, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import Carousel from 'react-native-reanimated-carousel';
import PillLabel from '@/components/PillLabel';
import type {ICarouselInstance} from 'react-native-reanimated-carousel';
import useTranslation from '@/hooks/useTranslation';

type NavigationProp = StackNavigationProp<RacquetStackParamList>;

const RacquetSelector = () => {
  const {t} = useTranslation();
  const navigation = useNavigation<NavigationProp>();
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const carouselRef = useRef<ICarouselInstance>(null);

  const [currentIndex, setCurrentIndex] = React.useState(0);
  const [selectedOptions, setSelectedOptions] = React.useState<{[stepIdx: number]: number | null}>(
    {},
  );

  const steps = [
    {
      id: 0,
      question: t('RacquetSelector.howLongHaveYouBeenPlayingTennis'),
      options: [t('RacquetSelector.lessThan2Years'), t('RacquetSelector.moreThan2Years')],
    },
    {
      id: 1,
      question: t('RacquetSelector.whichBestDescribesYourFitnessLevel'),
      options: [
        t('RacquetSelector.slowAndSteadyOnTheTreadmill'),
        t('RacquetSelector.workoutWarrior'),
      ],
    },
    {
      id: 2,
      question: t('RacquetSelector.whichCharacteristicsDoYouMostWantInARacquet'),
      options: [t('RacquetSelector.comfortableAndArmFriendly'), t('RacquetSelector.lotsOfPower')],
    },
  ];

  const handleSelectOption = (stepIdx: number, optionIdx: number) => {
    setSelectedOptions(prev => ({...prev, [stepIdx]: optionIdx}));
    goToScreen(stepIdx + 1);
  };

  const goToScreen = (screenIdx: number) => {
    if (screenIdx === 3) {
      navigation.navigate('RacquetSelectorDetail');
    } else if (screenIdx >= 0 && screenIdx < steps.length) {
      setCurrentIndex(screenIdx);
      carouselRef.current?.scrollTo({
        index: screenIdx,
        animated: true,
      });
    } else if (screenIdx < 0) {
      navigation.goBack();
      setSelectedOptions({});
    }
  };

  const renderItem = ({item, index}: {item: (typeof steps)[0]; index: number}) => {
    const isSelected = selectedOptions[index];

    return (
      <View style={styles.page}>
        <Typography variant="question" style={styles.question}>
          {item.question}
        </Typography>
        <View style={styles.optionsContainer}>
          {item.options?.map((option, idx) => (
            <PillLabel
              backgroundColor={isSelected === idx ? theme.colors.orange1 : theme.colors.dimGray}
              textColor={theme.colors.white}
              key={idx}
              label={option}
              containerStyle={styles.pillLabel}
              triangle={true}
              onPress={() => handleSelectOption(index, idx)}
            />
          ))}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        leftIcon={{
          name: 'side-menu',
          size: 24,
          color: theme.colors.white,
          containerStyle: {backgroundColor: theme.colors.primary},
        }}
        onLeftPress={() => navigation.dispatch(DrawerActions.openDrawer())}
        leftIconButtonStyle={styles.menuButton}
        rightIcons={[{name: 'cart', size: 32, color: theme.colors.activeColor}]}
        pageTitle={t('RacquetSelector.racquetSelector')}
        backgroundColor="transparent"
        isBackPress={true}
        backNavigation={() => goToScreen(currentIndex - 1)}
      />

      <Carousel
        ref={carouselRef}
        width={Dimensions.get('window').width}
        height={Dimensions.get('window').height - 240}
        data={steps}
        style={styles.carousel}
        onSnapToItem={setCurrentIndex}
        renderItem={renderItem}
        mode="parallax"
        modeConfig={{
          parallaxScrollingScale: 1,
          parallaxScrollingOffset: 10,
        }}
        defaultIndex={0}
        snapEnabled={false}
        loop={false}
        enabled={false}
      />

      <View style={styles.pagination}>
        {steps?.map((_, idx) => (
          <View key={idx} style={idx === currentIndex ? styles.dotActive : styles.dot} />
        ))}
      </View>
    </SafeAreaView>
  );
};

export default RacquetSelector;
