import React from 'react';
import {View, StyleSheet} from 'react-native';
import {Icon} from '@/components';
import { useThemeStore } from '@/store/themeStore';;

export const AvailableKioskMarker = () => {
  const theme = useThemeStore();
  return (
    <View style={styles.markerContainer}>
      <Icon name="location3" size={30} color="#DFFF4F" /* Neon yellow/green color */ />
    </View>
  );
};

export const PlannedLocationMarker = () => {
  const theme = useThemeStore();
  return (
    <View style={styles.markerContainer}>
      <Icon name="location3" size={30} color="#FFA500" /* Orange color */ />
    </View>
  );
};

export const UserLocationMarker = () => {
  const theme = useThemeStore();
  return (
    <View style={styles.userMarkerContainer}>
      <View style={styles.userMarkerOuter}>
        <View style={styles.userMarkerInner} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  markerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  userMarkerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  userMarkerOuter: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(37, 150, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(37, 150, 255, 0.5)',
  },
  userMarkerInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#2596FF',
    borderWidth: 1,
    borderColor: 'white',
  },
});
