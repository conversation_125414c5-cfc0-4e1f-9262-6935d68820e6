import {FONT_SIZE} from '@/utils/fonts';
import {Dimensions, StyleSheet} from 'react-native';

export const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme?.colors?.background,
      gap: 4,
      padding: 20,
    },
    listContainer: {
      flex: 1, // Ensures list takes up available space
      marginBottom: 16, // Optional: space above the button
    },
    scrollView: {flexGrow: 1, marginVertical: 10, gap: 20, paddingBottom: 20},
    title: {fontSize: FONT_SIZE.xxxl},
    buttonContainer: {
      marginBottom: 10,
      marginTop: 10,
    },
    titleContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 10,
      marginBottom: 10,
    },
    filterInput: {
      marginVertical: 12,
    },
    inviteBtn: {
      width: '100%',
    },
    inviteBtnText: {
      color: theme.colors.background,
      fontSize: FONT_SIZE.xl,
      fontWeight: 'bold',
    },
    keyboardAvoidingView: {
      flex: 1,
      paddingTop: 10,
    },
    scrollContainer: {
      flexGrow: 1,
    },
    form: {
      paddingVertical: 10,
    },
    inputContainer: {
      marginBottom: 20,
    },
    input: {
      height: 47,
      paddingHorizontal: 16,
      borderRadius: 100,
      borderWidth: 1,
    },
    searchBar: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.white,
      borderRadius: 8,
      paddingHorizontal: 20,
      gap: 10,
      borderColor: theme.colors.primary,
      borderWidth: 2,
      height: 70,
    },
  });
