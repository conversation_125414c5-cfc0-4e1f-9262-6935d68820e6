import {StyleSheet} from 'react-native';

export const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    root: {
      flex: 1,
    },
    scrollContainer: {
      paddingHorizontal: 16,
      flexGrow: 1,
      marginTop: 16,
      paddingBottom: 30,
    },
    cardHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 16,
    },
    cardImageContainer: {
      width: 63,
      height: 63,
      borderRadius: 100,
      backgroundColor: theme.colors.activeColor,
      alignItems: 'center',
      justifyContent: 'center',
    },
    cardImageText: {
      fontSize: 8,
      color: theme.colors.black,
    },

    cardContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    progressContainer: {
      marginTop: 16,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 25,
    },
    progressCard: {
      padding: 20,
      borderRadius: 26,
      margin: 'auto',
      backgroundColor: 'rgba(43,43,43,0.4)',
      flex: 1,
      height: '100%',
    },
    chartContainer: {
      flex: 1,
      marginTop: 16,
      backgroundColor: 'rgba(43,43,43,0.4)',
      borderRadius: 26,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: 100,
    },
  });
