import {create} from 'zustand';
import {useThemeStore} from './themeStore';
import {useConfigStore} from './configStore';
import {useAuthStore} from './authStore';
import {useHealthStore} from './healthStore';

// Combined store for accessing all stores
export const useStore = create(() => ({
  get theme() {
    return useThemeStore.getState();
  },
  get config() {
    return useConfigStore.getState();
  },
  get auth() {
    return useAuthStore.getState();
  },
  get health() {
    return useHealthStore.getState();
  },
}));

// Re-export individual stores
export {useThemeStore} from './themeStore';
export {useConfigStore} from './configStore';
export {useAuthStore} from './authStore';
export {useHealthStore} from './healthStore';
